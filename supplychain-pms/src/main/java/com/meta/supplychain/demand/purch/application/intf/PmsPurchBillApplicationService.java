package com.meta.supplychain.demand.purch.application.intf;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.entity.dto.common.resp.BillAdjustLogResp;
import com.meta.supplychain.entity.dto.pms.req.purch.*;
import com.meta.supplychain.entity.dto.pms.resp.purch.*;

import java.util.List;

public interface PmsPurchBillApplicationService {



    //新建采购订单
    Result<PurchBillOptResp> createPurchBill(PurchaseBillChangeReq createReq);

    //更新采购订单
    Result<PurchBillOptResp> updatePurchBill(PurchaseBillChangeReq updateReq);

    //审核采购订单
    Result<PurchBillOptResp> auditPurchBill(PurchaseBillChangeReq auditReq);

    //批量审核采购订单
    Result<List<PurchBillOptResp>> batchAuditPurchBill(PurchaseBillOptReq auditReq);

    //取消/关闭采购订单
    Result<PurchBillOptResp> cancelPurchBill(PurchaseBillOptReq optReq);

    Result<List<PurchBillOptResp>> batchCancelPurchBill(PurchaseBillOptReq optReq);

    //过期采购订单
    Result<Boolean> expirePurchBill();

    //采购订单确认发货
    Result<PurchBillOptResp> confirmShipPurchBill(PurchaseBillconfirmShipReq optReq);

    //标记采购订单
    Result<PurchBillOptResp> signPurchBill(PurchaseBillOptReq optReq);

    //调整采购订单
    Result<PurchBillOptResp> adjustPurchBill(PurchaseBillAdjustReq adjustReq);

    Result<List<BillAdjustLogResp>> getAdjustLog(PurchaseBillDetReq req);

    /**
     * 不同状态单据数量统计
     */
    Result<List<PurchaseBillStatistic>> getPurchStateStatistic(QueryPurchaseBillReq query);

    //查询采购订单列表
    PageResult<PurchaseBillResp> queryPurchList(QueryPurchaseBillReq queryPurchaseBillReq);

    //查询采购订单详情
    PurchaseBillWithDetailResp queryPurchDetail(PurchaseBillDetReq detReq);

    //查询采购订单详情-含各种明细
    PurchaseBillWithDetailAllResp queryPurchDetailAll(PurchaseBillDetReq detReq);

    //采购订单打印
    Result<List<PurchaseBillWithDetailResp>> printPurchBill(PurchaseBillPrintReq printReq);

    //查询采购订单列表
    PageResult<PurchaseBillDetailSumResp> queryPurchDetailList(QueryPurchaseBillReq req);
    List<PurchaseDeliverySkuResp> getPurchByDeliveryNos(List<PurchaseByDeliveryQryReq> req);
    //SCM-查询采购订单直流明细列表
    PageResult<PurchaseBillDetailWithDirectResp> queryPurchDirectList(QueryPurchaseBillReq req);
}