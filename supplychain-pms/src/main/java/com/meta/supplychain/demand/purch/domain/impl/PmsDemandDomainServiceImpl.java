package com.meta.supplychain.demand.purch.domain.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.context.ThreadLocals;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Maps;
import com.meta.supplychain.common.component.domain.md.intf.IMdDeliveryAppointmentStrategyDomainService;
import com.meta.supplychain.common.component.domain.md.intf.IMdDemandStrategyDomainService;
import com.meta.supplychain.common.component.service.intf.ISupplychainBizSysParamRuleService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.ISupplychainPmsBizRuleEngineService;
import com.meta.supplychain.demand.purch.application.intf.PmsOrderApplyApplicationService;
import com.meta.supplychain.demand.purch.application.intf.PmsPurchBillApplicationService;
import com.meta.supplychain.demand.purch.domain.intf.IPmsDeliveryToPurchDomainService;
import com.meta.supplychain.demand.purch.domain.intf.PmsDemandDomainService;
import com.meta.supplychain.demand.purch.domain.intf.PmsOrderApplyDomainService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.bds.req.QueryBatchDeptListReq;
import com.meta.supplychain.entity.dto.bds.resp.QueryBatchDeptListResp;
import com.meta.supplychain.entity.dto.bds.resp.StoreDetailResp;
import com.meta.supplychain.entity.dto.bds.resp.SupplierByCodeResp;
import com.meta.supplychain.entity.dto.goods.resp.GoodsQueryResp;
import com.meta.supplychain.entity.dto.goods.resp.ManageAndCirculationResp;
import com.meta.supplychain.entity.dto.md.component.bizrule.BatchGenerateBillNoDTO;
import com.meta.supplychain.entity.dto.md.component.bizrule.OrderAttrQueryDTO;
import com.meta.supplychain.entity.dto.md.component.bizrule.OrderReturnAttrDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.*;
import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyAutoMappingGroupDTO;
import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyStatusMapping4DeptDTO;
import com.meta.supplychain.entity.dto.md.req.demandstrategy.MdDemandStrategyStatusMapping4DeptQueryReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategyexclude.MdDemandStrategyExcludePageQueryReq;
import com.meta.supplychain.entity.dto.md.resp.deliveryappointment.DeliveryAppointmentStrategyResultDTO;
import com.meta.supplychain.entity.dto.md.resp.demandstrategyexclude.MdDemandStrategyExcludeResponseDTO;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.GoodsPurchPriceInfoResp;
import com.meta.supplychain.entity.dto.md.resp.goodsstrategy.QueryGoodsPurchPriceInfoReq;
import com.meta.supplychain.entity.dto.pms.apply.UpdateRespDemandDTO;
import com.meta.supplychain.entity.dto.pms.demand.*;
import com.meta.supplychain.entity.dto.pms.req.apply.CancelApplyDetail4DemandDTO;
import com.meta.supplychain.entity.dto.pms.req.billconvert.BillConvertApplyDataDTO;
import com.meta.supplychain.entity.dto.pms.req.billconvert.BillConvertDataDTO;
import com.meta.supplychain.entity.dto.pms.req.deliverytopurch.DeliveryToPurchReq;
import com.meta.supplychain.entity.dto.pms.req.demand.*;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchaseBillOptReq;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyBillResp;
import com.meta.supplychain.entity.dto.pms.resp.deliverytopurch.DeliveryToPurchDeliverySourceResp;
import com.meta.supplychain.entity.dto.pms.resp.deliverytopurch.DeliveryToPurchRefResp;
import com.meta.supplychain.entity.dto.pms.resp.deliverytopurch.DeliveryToPurchResp;
import com.meta.supplychain.entity.dto.pms.resp.deliverytopurch.DeliveryToPurchResultResp;
import com.meta.supplychain.entity.dto.pms.resp.demand.*;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchBillOptResp;
import com.meta.supplychain.entity.dto.replenishment.resp.BatchGenerateBillNoResp;
import com.meta.supplychain.entity.dto.wds.req.WdDeliveryBillBatchOptReq;
import com.meta.supplychain.entity.dto.wds.resp.WdBatchOptResp;
import com.meta.supplychain.entity.po.md.MdDeliveryDockDeptPO;
import com.meta.supplychain.entity.po.md.MdDeliveryDockGoodsCategoryPO;
import com.meta.supplychain.entity.po.md.MdDeliveryDockStrategyPO;
import com.meta.supplychain.entity.po.pms.*;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import com.meta.supplychain.enums.CommonBillTypeEnum;
import com.meta.supplychain.enums.GroupDeptEnum;
import com.meta.supplychain.enums.OrderAttributeEnum;
import com.meta.supplychain.enums.md.*;
import com.meta.supplychain.enums.pms.*;
import com.meta.supplychain.enums.wds.WDDeliveryOptTypeEnum;
import com.meta.supplychain.enums.wds.WDDeliveryOrderBillStatusEnum;
import com.meta.supplychain.enums.wds.WDDeliveryOrderSourceEnum;
import com.meta.supplychain.infrastructure.feign.BaseDataSystemFeignClient;
import com.meta.supplychain.infrastructure.feign.WdsFeignClient;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.*;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillDetailRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillRepository;
import com.meta.supplychain.util.*;
import com.meta.supplychain.util.spring.SpringContextUtil;
import com.metadata.idaas.client.model.LoginUserDTO;
import com.metadata.idaas.client.util.ClientIdentUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/03/30 01:43
 **/
@Service
public class PmsDemandDomainServiceImpl implements PmsDemandDomainService {

    @Autowired
    private IPmsDemandBillRepositoryService pmsDemandBillRepositoryService;

    @Autowired
    private IPmsDemandDetailSourceRefRepositoryService pmsDemandDetailSourceRefRepositoryService;

    @Autowired
    private IPmsDemandDeptGoodsDetailRepositoryService pmsDemandDeptGoodsDetailRepositoryService;

    @Autowired
    private IPmsDemandPurchShipperRepositoryService pmsDemandPurchShipperRepositoryService;

    @Autowired
    private IPmsDemandDeliveryShipperRepositoryService pmsDemandDeliveryShipperRepositoryService;

    @Autowired
    private IPmsDemandGoodsDetailRepositoryService pmsDemandGoodsDetailRepositoryService;
    
    @Autowired
    private IPmsDemandDeliveryToPurchRefRepositoryService pmsDemandDeliveryToPurchRefRepositoryService;
    
    @Autowired
    private IPmsDemandDeliveryToPurchRepositoryService pmsDemandDeliveryToPurchRepositoryService;

    @Resource
    private DataSourceTransactionManager dstManager;

    @Autowired
    private ISupplychainControlEngineService supplychainControlEngineService;

    @Autowired
    private PmsApplyBillDetailRepositoryService pmsApplyBillDetailRepositoryService;

    @Autowired
    private PmsApplyBillRepositoryService pmsApplyBillRepositoryService;

    @Autowired
    private PmsPurchaseOrderRepositoryService pmsPurchaseOrderRepositoryService;

    @Autowired
    private PmsPurchaseDetailRepositoryService pmsPurchaseDetailRepositoryService;

    @Autowired
    private IPmsPruchDetailRefRepositoryService pmsPruchDetailRefRepositoryService;

    @Autowired
    private IPmsDist2purchSupplierRecordRepositoryService pmsDist2purchSupplierRecordRepositoryService;

    @Autowired
    private IPmsDemandPruchDeliveryRefRepositoryService pmsDemandPruchDeliveryRefRepositoryService;

    @Autowired
    private IWdDeliveryBillDetailRepository wdDeliveryBillDetailRepository;

    @Autowired
    private IWdDeliveryBillRepository wdDeliveryBillRepository;

    @Autowired
    private ISupplychainPmsBizRuleEngineService supplychainPmsBizRuleEngineService;

    @Autowired
    private ISupplychainBizSysParamRuleService iSupplychainBizSysParamRuleService;

    @Resource
    private IMdDeliveryAppointmentStrategyDomainService mdDeliveryAppointmentStrategyDomainService;

    @Resource
    private IMdDemandStrategyDomainService mdDemandStrategyDomainService;

    @Resource
    private BaseDataSystemFeignClient baseDataSystemFeignClient;

    @Resource
    private WdsFeignClient wdsFeignClient;

    @Resource
    private RedisUtil redisUtil;

    @Autowired
    UserUtil userUtil;

    @Autowired
    private BaseStoreUtil baseStoreUtil;

    @Autowired
    private PmsOrderApplyDomainService pmsOrderApplyDomainService;

    @Resource
    private ThreadPoolTaskExecutor applyToDemandProcessThreadPool;

    private static final Long EXPIRE_TIME = 5 * 60 * 1000L;

    private PmsPurchBillApplicationService getPmsPurchBillApplicationService(){
        PmsPurchBillApplicationService pmsPurchBillApplicationService = SpringContextUtil.getApplicationContext().getBean(PmsPurchBillApplicationService.class);
        return pmsPurchBillApplicationService;
    }

    private PmsOrderApplyApplicationService getPmsOrderApplyApplicationService(){
        PmsOrderApplyApplicationService pmsOrderApplyApplicationService = SpringContextUtil.getApplicationContext().getBean(PmsOrderApplyApplicationService.class);
        return pmsOrderApplyApplicationService;
    }

    private IPmsDeliveryToPurchDomainService getIPmsDeliveryToPurchDomainService(){
        IPmsDeliveryToPurchDomainService pmsDeliveryToPurchDomainService = SpringContextUtil.getApplicationContext().getBean(IPmsDeliveryToPurchDomainService.class);

        return pmsDeliveryToPurchDomainService;
    }


    /**
     * 查询需求单列表
     * @param param
     * @return
     */
    @Override
    public PageResult<PmsDemandBillListResp> listDemandBill(QueryDemandBillListReq param) {
        //查询列表
        int cnt = pmsDemandBillRepositoryService.getPmsDemandBillMapper().countDemandBill(param);
        if(0 == cnt){
            return PageResult.ofEmpty();
        }

        List<PmsDemandBillPO> pmsDemandBillPOList = pmsDemandBillRepositoryService.getPmsDemandBillMapper().listDemandBill(param);
        //查询所有的需求单号
        Map<String,String> pmsDemandBillPOMap = pmsDemandBillPOList.stream().collect(Collectors.toMap(PmsDemandBillPO::getBillNo,PmsDemandBillPO::getBillNo));
        List<String> billNoList = new ArrayList<>(pmsDemandBillPOMap.keySet());

//        订货部门数：该需求单的订货部门去重后
//        需求数量：该需求单的订货部门需求数量
//        响应数量：该需求单的订货部门上 响应数量
        List<PmsDemandBillListResp> aggreQtyList = pmsDemandDeptGoodsDetailRepositoryService.getPmsDemandDeptGoodsDetailMapper().getAggreQty(billNoList);

        Map<String, PmsDemandBillListResp> deptGoodsMap = aggreQtyList.stream().collect(Collectors.toMap(PmsDemandBillListResp::getBillNo, PmsDemandBillListDTO->PmsDemandBillListDTO));
//        商品品项数：该需求单商品行 按商品SKU 去重

        List<PmsDemandBillListResp> aggreSkuQtyList = pmsDemandGoodsDetailRepositoryService.getPmsDemandGoodsDetailMapper().getAggreQty(billNoList);
        Map<String, PmsDemandBillListResp> goodsMap = aggreSkuQtyList.stream().collect(Collectors.toMap(PmsDemandBillListResp::getBillNo, PmsDemandBillListDTO->PmsDemandBillListDTO));


        //计算明细数量
        List<PmsDemandBillListResp> list = CglibCopier.copy(pmsDemandBillPOList, PmsDemandBillListResp.class, (s, t) -> {

            if(deptGoodsMap.containsKey(s.getBillNo())){
                PmsDemandBillListResp pmsDemandDeptGoodsDetailTmp = deptGoodsMap.get(s.getBillNo());
                t.setTotalDemandQty(pmsDemandDeptGoodsDetailTmp.getTotalDemandQty());//需求数量
                t.setTotalOrderDeptQty(pmsDemandDeptGoodsDetailTmp.getTotalOrderDeptQty());//订货部门数
                t.setTotalResponseQty(pmsDemandDeptGoodsDetailTmp.getTotalResponseQty());//响应数量
            }

            if(goodsMap.containsKey(s.getBillNo())){
                PmsDemandBillListResp pmsDemandDeptGoodsDetailTmp = goodsMap.get(s.getBillNo());

                t.setTotalSkuQty(pmsDemandDeptGoodsDetailTmp.getTotalSkuQty());//需求单商品行
            }
        });

        return PageResult.of(cnt,list);
    }

    /**
     * 查询需求单详情
     * @param param
     * @return
     */
    @Override
    public PmsDemandBillResultResp queryDemandDetail(QueryDemandDetailReq param) {
        PmsDemandBillResultResp pmsDemandBillResultResp = new PmsDemandBillResultResp();

        LambdaQueryWrapper<PmsDemandBillPO> demandBillWrapper = new LambdaQueryWrapper<PmsDemandBillPO>()
                .eq(PmsDemandBillPO::getBillNo, param.getBillNo());
        //需求单主表
        PmsDemandBillPO pmsDemandBillPO = pmsDemandBillRepositoryService.getOne(demandBillWrapper);

        if(null == pmsDemandBillPO){
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B001);
        }

        PmsDemandBillResp demandBillResult = CglibCopier.copy(pmsDemandBillPO,PmsDemandBillResp.class);
        pmsDemandBillResultResp.setPmsDemandBillResp(demandBillResult);

        DemandBillHandleDataDTO demandBillHandleDataDTO = new DemandBillHandleDataDTO();
        pmsDemandBillResultResp.setDemandBillHandleData(demandBillHandleDataDTO);

        demandBillHandleDataDTO.setPmsDemandBillPO(pmsDemandBillPO);

        //出货方-配送
        LambdaQueryWrapper<PmsDemandDeliveryShipperPO> pmsDemandDeliveryShipperWrapper = new LambdaQueryWrapper<PmsDemandDeliveryShipperPO>()
                .eq(PmsDemandDeliveryShipperPO::getBillNo, param.getBillNo());
        List<PmsDemandDeliveryShipperPO> pmsDemandDeliveryShipperPOS = pmsDemandDeliveryShipperRepositoryService.getPmsDemandDeliveryShipperMapper().selectList(pmsDemandDeliveryShipperWrapper);
        demandBillHandleDataDTO.setDemandDeliveryShipperPOList(pmsDemandDeliveryShipperPOS);

        List<PmsDemandDeliveryShipperResp> demandDeliveryShipperDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(pmsDemandDeliveryShipperPOS)){
            demandDeliveryShipperDTOList = CglibCopier.copy(pmsDemandDeliveryShipperPOS, PmsDemandDeliveryShipperResp.class);
            Map<Long, List<PmsDemandDeliveryShipperResp>> demandDeliveryShipperMap = demandDeliveryShipperDTOList.parallelStream().collect(Collectors.groupingBy(PmsDemandDeliveryShipperResp::getPinsideId));
            demandBillResult.setDemandDeliveryShipperMap(demandDeliveryShipperMap);
        }

        List<PmsDemandPurchShipperPO> pmsDemandPurchShipperAll = new ArrayList<>();
        //出货方-采购
        LambdaQueryWrapper<PmsDemandPurchShipperPO> demandPurchShipperWrapper = new LambdaQueryWrapper<PmsDemandPurchShipperPO>()
                .eq(PmsDemandPurchShipperPO::getBillNo, param.getBillNo()).eq(PmsDemandPurchShipperPO::getConvertFlag, 0);
        List<PmsDemandPurchShipperPO> pmsDemandPurchShipperPOS = pmsDemandPurchShipperRepositoryService.getPmsDemandPurchShipperMapper().selectList(demandPurchShipperWrapper);
        pmsDemandPurchShipperAll.addAll(pmsDemandPurchShipperPOS);
        demandBillHandleDataDTO.setDemandPurchShipperPOList(pmsDemandPurchShipperPOS);


        List<PmsDemandPurchShipperResp> demandPurchShipperDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(pmsDemandPurchShipperPOS)){
            demandPurchShipperDTOList = CglibCopier.copy(pmsDemandPurchShipperPOS, PmsDemandPurchShipperResp.class);
            Map<Long, List<PmsDemandPurchShipperResp>> demandPurchShipperMap = demandPurchShipperDTOList.parallelStream().collect(Collectors.groupingBy(PmsDemandPurchShipperResp::getPinsideId));
            demandBillResult.setDemandPurchShipperMap(demandPurchShipperMap);
        }

        //查询关联表
        LambdaUpdateWrapper<PmsDemandDetailSourceRefPO> detailSourceRefWrapper = new LambdaUpdateWrapper<>();
        detailSourceRefWrapper.eq(PmsDemandDetailSourceRefPO::getDemandBillNo,param.getBillNo());
        List<PmsDemandDetailSourceRefPO> pmsDemandDetailSourceRefPOS = pmsDemandDetailSourceRefRepositoryService.getPmsDemandDetailSourceRefMapper().selectList(detailSourceRefWrapper);
        demandBillHandleDataDTO.setDemandDetailSourceRefPOList(pmsDemandDetailSourceRefPOS);

        List<PmsDemandDetailSourceRefResp> detailSourceRefResultDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(pmsDemandDetailSourceRefPOS)){
            Map<Long,List<PmsDemandDetailSourceRefPO>> detailSourceRefPOMap = pmsDemandDetailSourceRefPOS.parallelStream().collect(Collectors.groupingBy(PmsDemandDetailSourceRefPO::getPinsideId));
            demandBillHandleDataDTO.setDetailSourceRefPOMap(detailSourceRefPOMap);
            detailSourceRefResultDTOList = CglibCopier.copy(pmsDemandDetailSourceRefPOS, PmsDemandDetailSourceRefResp.class);
            Map<Long,List<PmsDemandDetailSourceRefResp>> detailSourceRefMap = detailSourceRefResultDTOList.parallelStream().collect(Collectors.groupingBy(PmsDemandDetailSourceRefResp::getPinsideId));
            demandBillResult.setDetailSourceRefMap(detailSourceRefMap);
        }

        demandBillResult.setDetailSourceRefResult(detailSourceRefResultDTOList);

        //部门商品
        LambdaQueryWrapper<PmsDemandDeptGoodsDetailPO> demandDeptGoodsDetailWrapper = new LambdaQueryWrapper<PmsDemandDeptGoodsDetailPO>()
                .eq(PmsDemandDeptGoodsDetailPO::getBillNo, param.getBillNo());
        List<PmsDemandDeptGoodsDetailPO> pmsDemandDeptGoodsDetailPOS = pmsDemandDeptGoodsDetailRepositoryService.getPmsDemandDeptGoodsDetailMapper().selectList(demandDeptGoodsDetailWrapper);
        demandBillHandleDataDTO.setDemandDeptGoodsDetailPOList(pmsDemandDeptGoodsDetailPOS);

        List<PmsDemandDeptGoodsDetailResp> demandDeptGoodsDetailDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(pmsDemandDeptGoodsDetailPOS)){
            demandDeptGoodsDetailDTOList = CglibCopier.copy(pmsDemandDeptGoodsDetailPOS, PmsDemandDeptGoodsDetailResp.class);

            for (PmsDemandDeptGoodsDetailResp pmsDemandDeptGoodsDetailResp : demandDeptGoodsDetailDTOList) {
                pmsDemandDeptGoodsDetailResp.setDemandDeliveryShipperList(demandBillResult.getDemandDeliveryShipperMap().get(pmsDemandDeptGoodsDetailResp.getInsideId()));
                pmsDemandDeptGoodsDetailResp.setDemandPurchShipperList(demandBillResult.getDemandPurchShipperMap().get(pmsDemandDeptGoodsDetailResp.getInsideId()));
                pmsDemandDeptGoodsDetailResp.setDemandDetailSourceList(demandBillResult.getDetailSourceRefMap().get(pmsDemandDeptGoodsDetailResp.getInsideId()));
            }

            Map<Long, List<PmsDemandDeptGoodsDetailResp>> demandDeptGoodsDetailDTOMap = demandDeptGoodsDetailDTOList.parallelStream().collect(Collectors.groupingBy(PmsDemandDeptGoodsDetailResp::getPinsideId));
            demandBillResult.setDemandDeptGoodsDetailMap(demandDeptGoodsDetailDTOMap);
        }

        //商品
        LambdaQueryWrapper<PmsDemandGoodsDetailPO> demandGoodsDetailWrapper = new LambdaQueryWrapper<PmsDemandGoodsDetailPO>()
                .eq(PmsDemandGoodsDetailPO::getBillNo, param.getBillNo());
        List<PmsDemandGoodsDetailPO> pmsDemandGoodsDetailPOS = pmsDemandGoodsDetailRepositoryService.getPmsDemandGoodsDetailMapper().selectList(demandGoodsDetailWrapper);
        demandBillHandleDataDTO.setDemandGoodsDetailPOList(pmsDemandGoodsDetailPOS);

        List<PmsDemandGoodsDetailResp> demandGoodsDetailDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(pmsDemandGoodsDetailPOS)){
            demandGoodsDetailDTOList = CglibCopier.copy(pmsDemandGoodsDetailPOS, PmsDemandGoodsDetailResp.class);
            for (PmsDemandGoodsDetailResp pmsDemandGoodsDetailResp : demandGoodsDetailDTOList) {
                pmsDemandGoodsDetailResp.setDemandDeptGoodsDetailList(demandBillResult.getDemandDeptGoodsDetailMap().get(pmsDemandGoodsDetailResp.getInsideId()));
            }
        }
        demandBillResult.setDemandGoodsDetailList(demandGoodsDetailDTOList);

        Map<Long, PmsDemandGoodsDetailResp> demandGoodsDetailMap = demandGoodsDetailDTOList.parallelStream().collect(Collectors.toMap(PmsDemandGoodsDetailResp::getInsideId, Function.identity()));
        demandBillResult.setDemandGoodsDetailMap(demandGoodsDetailMap);

        //查询配转采信息
        PmsDemandDeliveryToPurchResultDTO pmsDemandDeliveryToPurchResultDTO = new PmsDemandDeliveryToPurchResultDTO();
        demandBillResult.setDeliveryToPurchResult(pmsDemandDeliveryToPurchResultDTO);
        //查询配转采数据列表
        LambdaQueryWrapper<PmsDemandDeliveryToPurchPO> deliveryToPurchWrapper = new LambdaQueryWrapper();
        deliveryToPurchWrapper.eq(PmsDemandDeliveryToPurchPO::getBillNo,param.getBillNo());
        List<PmsDemandDeliveryToPurchPO> pmsDemandDeliveryToPurchPOS = pmsDemandDeliveryToPurchRepositoryService.getPmsDemandDeliveryToPurchMapper().selectList(deliveryToPurchWrapper);
        demandBillHandleDataDTO.setDeliveryToPurchPOList(pmsDemandDeliveryToPurchPOS);

        if(CollectionUtils.isEmpty(pmsDemandDeliveryToPurchPOS)){
            demandBillHandleDataDTO.setDemandPurchShipperPOList(pmsDemandPurchShipperAll);
            return pmsDemandBillResultResp;
        }

        List<PmsDemandDeliveryToPurchResp> deliveryToPurchList = CglibCopier.copy(pmsDemandDeliveryToPurchPOS, PmsDemandDeliveryToPurchResp.class);
        pmsDemandDeliveryToPurchResultDTO.setDeliveryToPurchList(deliveryToPurchList);

        //查询配转采供应商信息
        LambdaUpdateWrapper<PmsDemandPurchShipperPO> purchWrapper = new LambdaUpdateWrapper<>();
        purchWrapper.eq(PmsDemandPurchShipperPO::getBillNo,param.getBillNo());
        purchWrapper.eq(PmsDemandPurchShipperPO::getConvertFlag,1);

        pmsDemandPurchShipperPOS = pmsDemandPurchShipperRepositoryService.getPmsDemandPurchShipperMapper().selectList(purchWrapper);
        pmsDemandPurchShipperAll.addAll(pmsDemandPurchShipperPOS);
        demandBillHandleDataDTO.setDemandPurchShipperPOList(pmsDemandPurchShipperAll);

        List<PmsDemandPurchShipperResp> copy = CglibCopier.copy(pmsDemandPurchShipperPOS, PmsDemandPurchShipperResp.class);
        Map<Long, List<PmsDemandPurchShipperResp>> purchShipperResultMap = copy.stream().collect(Collectors.groupingBy(PmsDemandPurchShipperResp::getDeliveryToPurchInsideId));
        pmsDemandDeliveryToPurchResultDTO.setPurchShipperResultMap(purchShipperResultMap);

        //门店配送来源明细
        //配送转采购供应商信息,key=转采数据.insideId
        Map<Long,List<PmsDemandDeliveryToPurchDeliverySourceResp>> deliveryToPurchDeliverySourceMap = new HashMap<>();
        //订货申请订单号、订货申请行号、订货部门编码、订货部门名称、客户编码、客户名称、配送数
        LambdaQueryWrapper<PmsDemandDeliveryToPurchRefPO> deliveryToPurchRefWrapper = new LambdaQueryWrapper();
        deliveryToPurchRefWrapper.eq(PmsDemandDeliveryToPurchRefPO::getBillNo,param.getBillNo());
        List<PmsDemandDeliveryToPurchRefPO> pmsDemandDeliveryToPurchRefPOS = pmsDemandDeliveryToPurchRefRepositoryService.getPmsDemandDeliveryToPurchRefMapper().selectList(deliveryToPurchRefWrapper);
        demandBillHandleDataDTO.setDeliveryToPurchRefPOList(pmsDemandDeliveryToPurchRefPOS);

        //key:配转才的单内序号
        Map<Long, List<PmsDemandDeliveryToPurchRefPO>> deliveryToPurchRefMap = pmsDemandDeliveryToPurchRefPOS.stream().collect(Collectors.groupingBy(PmsDemandDeliveryToPurchRefPO::getDeliveryToPurchInsideId));

        Map<Long, PmsDemandDeptGoodsDetailResp> demandDeptGoodsDetailMap = demandDeptGoodsDetailDTOList.stream().collect(Collectors.toMap(PmsDemandDeptGoodsDetailResp::getInsideId,Function.identity()));

        Map<Long, List<PmsDemandDetailSourceRefPO>> detailSourceRefMap = pmsDemandDetailSourceRefPOS.stream().collect(Collectors.groupingBy(PmsDemandDetailSourceRefPO::getPinsideId));

        for (List<PmsDemandDeliveryToPurchRefPO> value : deliveryToPurchRefMap.values()) {
            List<PmsDemandDeliveryToPurchDeliverySourceResp> list = new ArrayList<>();
            for (PmsDemandDeliveryToPurchRefPO pmsDemandDeliveryToPurchRefPO : value) {

                if(detailSourceRefMap.containsKey(pmsDemandDeliveryToPurchRefPO.getDeptGoodsInsideId())){
                    BigDecimal responseQty = BigDecimal.ZERO;
                    if(demandDeptGoodsDetailMap.containsKey(pmsDemandDeliveryToPurchRefPO.getDeptGoodsInsideId())){
                        PmsDemandDeptGoodsDetailResp deptGoodsDetailResult = demandDeptGoodsDetailMap.get(pmsDemandDeliveryToPurchRefPO.getDeptGoodsInsideId());

                        responseQty = deptGoodsDetailResult.getResponseQty();
                    }

                    List<PmsDemandDetailSourceRefPO> pmsDemandDetailSourceRefList = detailSourceRefMap.get(pmsDemandDeliveryToPurchRefPO.getDeptGoodsInsideId());
                    for (PmsDemandDetailSourceRefPO pmsDemandDetailSourceRef : pmsDemandDetailSourceRefList) {
                        PmsDemandDeliveryToPurchDeliverySourceResp demandDeliveryToPurchDeliverySourceDTO = new PmsDemandDeliveryToPurchDeliverySourceResp();
                        demandDeliveryToPurchDeliverySourceDTO.setApplyBillNo(pmsDemandDetailSourceRef.getApplyBillNo());
                        demandDeliveryToPurchDeliverySourceDTO.setApplyInsideId(pmsDemandDetailSourceRef.getSrcInsideId());
                        demandDeliveryToPurchDeliverySourceDTO.setDeptCode(pmsDemandDetailSourceRef.getOrderDeptCode());
                        demandDeliveryToPurchDeliverySourceDTO.setDeptName(pmsDemandDetailSourceRef.getOrderDeptName());
                        demandDeliveryToPurchDeliverySourceDTO.setCustomerCode(pmsDemandDetailSourceRef.getSrcCustomerCode());
                        demandDeliveryToPurchDeliverySourceDTO.setCustomerName(pmsDemandDetailSourceRef.getSrcCustomerName());

                        if(responseQty.compareTo(pmsDemandDetailSourceRef.getResponseQty()) == 0){
                            demandDeliveryToPurchDeliverySourceDTO.setDeliveryQty(responseQty);
                            responseQty = BigDecimal.ZERO;
                        }
                        else if(responseQty.compareTo(pmsDemandDetailSourceRef.getResponseQty()) > 0){
                            demandDeliveryToPurchDeliverySourceDTO.setDeliveryQty(pmsDemandDetailSourceRef.getResponseQty());
                            responseQty = responseQty.subtract(pmsDemandDetailSourceRef.getResponseQty());
                        }
                        else{
                            demandDeliveryToPurchDeliverySourceDTO.setDeliveryQty(responseQty);
                            responseQty = BigDecimal.ZERO;
                        }


                        list.add(demandDeliveryToPurchDeliverySourceDTO);
                    }

                }
            }
            if(CollectionUtils.isNotEmpty(value)){
                deliveryToPurchDeliverySourceMap.put(value.get(0).getDeliveryToPurchInsideId(),list);
            }
        }

        pmsDemandDeliveryToPurchResultDTO.setDeliveryToPurchDeliverySourceMap(deliveryToPurchDeliverySourceMap);

        return pmsDemandBillResultResp;
    }

    /**
     * 暂存需求单
     * @param pmsDemandBillReq
     * @return
     */
    @Override
    public DemandBillHandleDataDTO stagingDemandBill(PmsDemandBillReq pmsDemandBillReq) {
        return handlerStagingDemandBill(pmsDemandBillReq,true);
    }


    public DemandBillHandleDataDTO handlerStagingDemandBill(PmsDemandBillReq pmsDemandBillReq,Boolean isTransaction) {
        //根据需求单号和id判断是否修改，修改的话增加redis锁
        Boolean isAdd = false;

        LambdaQueryWrapper<PmsDemandBillPO> demandBillWrapper = new LambdaQueryWrapper<PmsDemandBillPO>()
                .eq(PmsDemandBillPO::getBillNo, pmsDemandBillReq.getBillNo());
        //需求单主表
        PmsDemandBillPO pmsDemandBillResultPO = pmsDemandBillRepositoryService.getOne(demandBillWrapper);

        String billNo = pmsDemandBillReq.getBillNo();
//
//        if(StringUtils.isNotEmpty(billNo)){
        lockDemandBill(billNo);
        if(null != pmsDemandBillResultPO){

        }
        else{
            isAdd = true;
        }

        PmsDemandBillPO pmsDemandBillPO = CglibCopier.copy(pmsDemandBillReq, PmsDemandBillPO.class);

//        if(isAdd){
//            //查询需求单号
//            billNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.PMS_DEMAND,"");
//            pmsDemandBillPO.setBillNo(billNo);
//            pmsDemandBillReq.setBillNo(billNo);
//        }

        //处理数据
        DemandBillHandleDataDTO demandBillHandleDataDTO = null;
        try{
            demandBillHandleDataDTO = handleData(pmsDemandBillReq);
            demandBillHandleDataDTO.setPmsDemandBillPO(pmsDemandBillPO);
            //校验数据
            checkDemandBill(demandBillHandleDataDTO);

        }catch (BizException e){
//            if(!isAdd){
//                unLockDemandBill(billNo);
//            }
            unLockDemandBill(billNo);
            e.printStackTrace();
            Logs.error("PmsDemandDomainServiceImpl.stagingDemandBill.handleData.BizException", e);
            BizExceptions.throwWithErrorCode(e.getError());
        }catch (Exception e){
//            if(!isAdd){
//                unLockDemandBill(billNo);
//            }
            unLockDemandBill(billNo);
            e.printStackTrace();
            Logs.error("PmsDemandDomainServiceImpl.stagingDemandBill.handleData.Exception", e);
            BizExceptions.throwWithCodeAndMsg(PmsErrorCodeEnum.SC_PMS_002_B004.getCode(), PmsErrorCodeEnum.SC_PMS_002_B004.getErrorMsg() + e.getMessage());
        }

        if(ObjectUtils.equals(PmsDemanBillOpTypeEnum.STAGING_SUMIT.getCode(), pmsDemandBillReq.getOpType())){
            LoginUserDTO operator = ClientIdentUtil.getLoginUser();
            pmsDemandBillPO.setSubmitCode(operator.getCode());
            pmsDemandBillPO.setSubmitName(operator.getName());
            pmsDemandBillPO.setSubmitTime(LocalDateTime.now());
            pmsDemandBillPO.setSubmitUid(operator.getUid());
        }

        TransactionStatus transaction = null;
        if(isTransaction){
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            transaction = dstManager.getTransaction(def);
        }


        try {
            //删除商品表
            LambdaUpdateWrapper<PmsDemandGoodsDetailPO> goodsDetailWrapper = new LambdaUpdateWrapper<>();
            goodsDetailWrapper.eq(PmsDemandGoodsDetailPO::getBillNo, pmsDemandBillReq.getBillNo());
            pmsDemandGoodsDetailRepositoryService.getPmsDemandGoodsDetailMapper().delete(goodsDetailWrapper);

            //删除部门商品表
            LambdaUpdateWrapper<PmsDemandDeptGoodsDetailPO> deptGoodsDetailWrapper = new LambdaUpdateWrapper<>();
            deptGoodsDetailWrapper.eq(PmsDemandDeptGoodsDetailPO::getBillNo, pmsDemandBillReq.getBillNo());
            pmsDemandDeptGoodsDetailRepositoryService.getPmsDemandDeptGoodsDetailMapper().delete(deptGoodsDetailWrapper);

            //删除出货方-配送
            LambdaUpdateWrapper<PmsDemandDeliveryShipperPO> delivertWrapper = new LambdaUpdateWrapper<>();
            delivertWrapper.eq(PmsDemandDeliveryShipperPO::getBillNo, pmsDemandBillReq.getBillNo());
            pmsDemandDeliveryShipperRepositoryService.getPmsDemandDeliveryShipperMapper().delete(delivertWrapper);

            //删除出货方-采购
            LambdaUpdateWrapper<PmsDemandPurchShipperPO> purchWrapper = new LambdaUpdateWrapper<>();
            purchWrapper.eq(PmsDemandPurchShipperPO::getBillNo, pmsDemandBillReq.getBillNo());
            pmsDemandPurchShipperRepositoryService.getPmsDemandPurchShipperMapper().delete(purchWrapper);

            //删除配转采数据
            LambdaQueryWrapper<PmsDemandDeliveryToPurchPO> deliveryToPurchWrapper = new LambdaQueryWrapper();
            deliveryToPurchWrapper.eq(PmsDemandDeliveryToPurchPO::getBillNo, pmsDemandBillReq.getBillNo());
            pmsDemandDeliveryToPurchRepositoryService.getPmsDemandDeliveryToPurchMapper().delete(deliveryToPurchWrapper);

            //删除配转采关联关系
            LambdaQueryWrapper<PmsDemandDeliveryToPurchRefPO> deliveryToPurchRefWrapper = new LambdaQueryWrapper();
            deliveryToPurchRefWrapper.eq(PmsDemandDeliveryToPurchRefPO::getBillNo, pmsDemandBillReq.getBillNo());
            pmsDemandDeliveryToPurchRefRepositoryService.getPmsDemandDeliveryToPurchRefMapper().delete(deliveryToPurchRefWrapper);

            List<PmsApplyBillDetailPO> applyBillDetaillist = new ArrayList<>();

            LambdaQueryWrapper<PmsApplyBillDetailPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PmsApplyBillDetailPO::getDemandBillNo, pmsDemandBillReq.getBillNo());
            List<PmsApplyBillDetailPO> applyBillDetailPrelist = pmsApplyBillDetailRepositoryService.list(queryWrapper);
            if(CollectionUtils.isNotEmpty(applyBillDetailPrelist)){
                applyBillDetaillist.addAll(applyBillDetailPrelist);
            }

            //订货申请明细修改响应状态
            PmsApplyBillDetailPO pmsApplyBillDetailPO = new PmsApplyBillDetailPO();
            pmsApplyBillDetailPO.setStatus(1);

            LambdaUpdateWrapper<PmsApplyBillDetailPO> applyBillDetailWrapper = new LambdaUpdateWrapper<>();
            applyBillDetailWrapper.eq(PmsApplyBillDetailPO::getDemandBillNo, pmsDemandBillReq.getBillNo());
            applyBillDetailWrapper.ne(PmsApplyBillDetailPO::getStatus, 3);

            pmsApplyBillDetailRepositoryService.getBaseMapper().update(pmsApplyBillDetailPO,applyBillDetailWrapper);

            //删除关联表
            LambdaUpdateWrapper<PmsDemandDetailSourceRefPO> detailSourceRefWrapper = new LambdaUpdateWrapper<>();
            detailSourceRefWrapper.eq(PmsDemandDetailSourceRefPO::getDemandBillNo, pmsDemandBillReq.getBillNo());
            pmsDemandDetailSourceRefRepositoryService.getPmsDemandDetailSourceRefMapper().delete(detailSourceRefWrapper);

            //组装数据,落库
            if(!isAdd && ObjectUtils.notEqual(PmsDemanBillOpTypeEnum.AUTO_SUBMIT.getCode(),pmsDemandBillReq.getOpType())){
                //修改
                LambdaUpdateWrapper<PmsDemandBillPO> updateWrapper = new LambdaUpdateWrapper();
                updateWrapper.eq(PmsDemandBillPO::getBillNo,pmsDemandBillPO.getBillNo());
                pmsDemandBillRepositoryService.update(pmsDemandBillPO,updateWrapper);
            }
            else{
                //保存
                pmsDemandBillRepositoryService.getPmsDemandBillMapper().insert(pmsDemandBillPO);
            }

            //写入数据
            if(CollectionUtils.isNotEmpty(demandBillHandleDataDTO.getDemandGoodsDetailPOList())){
                pmsDemandGoodsDetailRepositoryService.saveBatch(demandBillHandleDataDTO.getDemandGoodsDetailPOList());
            }


            if(CollectionUtils.isNotEmpty(demandBillHandleDataDTO.getDemandDeptGoodsDetailPOList())){
                pmsDemandDeptGoodsDetailRepositoryService.saveBatch(demandBillHandleDataDTO.getDemandDeptGoodsDetailPOList());
            }


            if(CollectionUtils.isNotEmpty(demandBillHandleDataDTO.getDemandDetailSourceRefPOList())){
                pmsDemandDetailSourceRefRepositoryService.saveBatch(demandBillHandleDataDTO.getDemandDetailSourceRefPOList());
            }


            if(CollectionUtils.isNotEmpty(demandBillHandleDataDTO.getDemandDeliveryShipperPOList())){
                pmsDemandDeliveryShipperRepositoryService.saveBatch(demandBillHandleDataDTO.getDemandDeliveryShipperPOList());
            }


            if(CollectionUtils.isNotEmpty(demandBillHandleDataDTO.getDemandPurchShipperPOList())){
                pmsDemandPurchShipperRepositoryService.saveBatch(demandBillHandleDataDTO.getDemandPurchShipperPOList());
            }

            //配转采处理
            if(CollectionUtils.isNotEmpty(demandBillHandleDataDTO.getDeliveryToPurchPOList())){
                pmsDemandDeliveryToPurchRepositoryService.saveBatch(demandBillHandleDataDTO.getDeliveryToPurchPOList());
            }

            if(CollectionUtils.isNotEmpty(demandBillHandleDataDTO.getDeliveryToPurchRefPOList())){
                pmsDemandDeliveryToPurchRefRepositoryService.saveBatch(demandBillHandleDataDTO.getDeliveryToPurchRefPOList());
            }


            //订货申请明细修改响应状态
            UpdateRespDemandDTO updateRespDemandDTO = new UpdateRespDemandDTO();
            updateRespDemandDTO.setDemandBillNo(pmsDemandBillReq.getBillNo());
            updateRespDemandDTO.setStatus(2);

            MdDemandStrategyStatusMapping4DeptQueryReq demandStrategyStatusMapping4DeptQueryReq = new MdDemandStrategyStatusMapping4DeptQueryReq();
            List<String> demandTransferConditionList = new ArrayList<>();
            demandTransferConditionList.add(MdDemandStrategyStatusConditionEnum.AUTO_DEMAND_ORDER_APPLY_FAIL_RULE.getCode());
            demandStrategyStatusMapping4DeptQueryReq.setDemandTransferConditionList(demandTransferConditionList);

            List<String> demandTransferConditionBizList = new ArrayList<>();
            demandTransferConditionBizList.add(MdDemandStrategyBizConditionEnum.PROCESS_RULE.getCode());
            demandStrategyStatusMapping4DeptQueryReq.setDemandTransferConditionBizList(demandTransferConditionBizList);
            List<MdDemandStrategyStatusMapping4DeptDTO> mdDemandStrategyStatusMapping4DeptDTOS = mdDemandStrategyDomainService.queryMdDemandStrategyStatusMappingData(demandStrategyStatusMapping4DeptQueryReq);
            Boolean isCancel = false;
            //1订货申请行更新为已作废状态
            //2订货申请行更新为待提单状态
            if(CollectionUtils.isNotEmpty(mdDemandStrategyStatusMapping4DeptDTOS)){
                if(ObjectUtils.equals(MdOrderApplyFailRuleEnum.UPDATE_TO_CANCELED,mdDemandStrategyStatusMapping4DeptDTOS.get(0).getVal())){
                    isCancel = true;
                }
            }

            List<CancelApplyDetail4DemandDTO> cancelApplyBillDTOs = new ArrayList<>();
            List<UpdateRespDemandDTO.ApplyInfo> applyInfoList = new ArrayList<>();
            for (PmsDemandDetailSourceRefPO pmsDemandDetailSourceRefPO : demandBillHandleDataDTO.getDemandDetailSourceRefPOList()) {
                if(ObjectUtils.equals(1,pmsDemandDetailSourceRefPO.getType()) &&
                        ObjectUtils.equals(1,pmsDemandDetailSourceRefPO.getStatus())){
                    UpdateRespDemandDTO.ApplyInfo applyInfo = new UpdateRespDemandDTO.ApplyInfo();
                    applyInfo.setBillNo(pmsDemandDetailSourceRefPO.getApplyBillNo());
                    applyInfo.setInsideId(pmsDemandDetailSourceRefPO.getSrcInsideId());
                    applyInfoList.add(applyInfo);
                }
                else if(ObjectUtils.equals(1,pmsDemandDetailSourceRefPO.getType()) && ObjectUtils.equals(0,pmsDemandDetailSourceRefPO.getStatus())){
                    //自动需求单
                    if(ObjectUtils.equals(1,pmsDemandBillPO.getBillSource()) && isCancel){
                        CancelApplyDetail4DemandDTO cancelApplyBillDTO = new CancelApplyDetail4DemandDTO();
                        cancelApplyBillDTO.setBillNo(pmsDemandDetailSourceRefPO.getApplyBillNo());
                        cancelApplyBillDTO.setInsideId(pmsDemandDetailSourceRefPO.getSrcInsideId());
                        cancelApplyBillDTOs.add(cancelApplyBillDTO);
                    }
                    else{
                        UpdateRespDemandDTO.ApplyInfo applyInfo = new UpdateRespDemandDTO.ApplyInfo();
                        applyInfo.setBillNo(pmsDemandDetailSourceRefPO.getApplyBillNo());
                        applyInfo.setInsideId(pmsDemandDetailSourceRefPO.getSrcInsideId());
                        applyInfoList.add(applyInfo);
                    }
                }
                else if(ObjectUtils.equals(2,pmsDemandDetailSourceRefPO.getStatus())){
                    CancelApplyDetail4DemandDTO cancelApplyBillDTO = new CancelApplyDetail4DemandDTO();
                    cancelApplyBillDTO.setBillNo(pmsDemandDetailSourceRefPO.getApplyBillNo());
                    cancelApplyBillDTO.setInsideId(pmsDemandDetailSourceRefPO.getSrcInsideId());

                    cancelApplyBillDTOs.add(cancelApplyBillDTO);
                }
            }
            //自动需求单失败，或者手工需求单失效的，需要作废订货申请明细行
            if(CollectionUtils.isNotEmpty(cancelApplyBillDTOs)){
                Result<ApplyBillResp> applyBillRespResult = getPmsOrderApplyApplicationService().cancelOrderApply4Demand(cancelApplyBillDTOs);
//                if(ObjectUtils.notEqual(0,applyBillRespResult.getCode())){
//                    BizExceptions.throwWithCodeAndMsg(applyBillRespResult.getCode(),applyBillRespResult.getMsg());
//                }
            }
            updateRespDemandDTO.setApplyInfoList(applyInfoList);

            if(CollectionUtils.isNotEmpty(applyInfoList)){
                pmsApplyBillDetailRepositoryService.updateRespDemand(updateRespDemandDTO);
            }


            List<PmsApplyBillDetailPO> applyBillDetailCurlist = pmsApplyBillDetailRepositoryService.list(queryWrapper);
            if(CollectionUtils.isNotEmpty(applyBillDetailCurlist)){
                applyBillDetaillist.addAll(applyBillDetailCurlist);
            }

            if(CollectionUtils.isNotEmpty(applyBillDetaillist)){
                pmsOrderApplyDomainService.syncApplyDetailState(applyBillDetaillist);
            }

            if(isTransaction){
                dstManager.commit(transaction);
            }

        } catch (BizException e) {
            Logs.error("PmsDemandDomainServiceImpl.stagingDemandBill.bizException", e);
            if(isTransaction){
                dstManager.rollback(transaction);
            }
            BizExceptions.throwWithCodeAndMsg(e.getError().getErrorCode(),e.getMessage());
        } catch (Exception e) {
            Logs.error("PmsDemandDomainServiceImpl.stagingDemandBill.error", e);
            if(isTransaction){
                dstManager.rollback(transaction);
            }
            BizExceptions.throwWithCodeAndMsg(PmsErrorCodeEnum.SC_PMS_002_B004.getCode(), PmsErrorCodeEnum.SC_PMS_002_B004.getErrorMsg() + e.getMessage());
        } finally {
            unLockDemandBill(billNo);
        }

        return demandBillHandleDataDTO;
    }

    /**
     * 校验需求单数据
     * @param
     */
    private void checkDemandBill(DemandBillHandleDataDTO demandBillHandleDataDTO) {
        //判断订货申请是否被其他单据使用
        UpdateRespDemandDTO param = new UpdateRespDemandDTO();
        param.setDemandBillNo(demandBillHandleDataDTO.getPmsDemandBillPO().getBillNo());

        List<UpdateRespDemandDTO.ApplyInfo> applyInfoList = new ArrayList<>();
        for (PmsDemandDetailSourceRefPO pmsDemandDetailSourceRefPO : demandBillHandleDataDTO.getDemandDetailSourceRefPOList()) {
            if(ObjectUtils.equals(1,pmsDemandDetailSourceRefPO.getStatus()) && ObjectUtils.equals(1,pmsDemandDetailSourceRefPO.getType())){
                UpdateRespDemandDTO.ApplyInfo applyInfo = new UpdateRespDemandDTO.ApplyInfo();
                applyInfo.setBillNo(pmsDemandDetailSourceRefPO.getApplyBillNo());
                applyInfo.setInsideId(pmsDemandDetailSourceRefPO.getSrcInsideId());
                applyInfoList.add(applyInfo);
            }

        }
        param.setApplyInfoList(applyInfoList);

        if(CollectionUtils.isNotEmpty(applyInfoList)){
            List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = pmsApplyBillDetailRepositoryService.checkApplyBillDetail(param);
            if(CollectionUtils.isNotEmpty(pmsApplyBillDetailPOS)){
                StringBuffer sb = new StringBuffer();
                for (PmsApplyBillDetailPO pmsApplyBillDetailPO : pmsApplyBillDetailPOS) {
                    sb.append(pmsApplyBillDetailPO.getBillNo()).append("[").append(pmsApplyBillDetailPO.getInsideId()).append("],");
                }
                BizExceptions.throwWithCodeAndMsg(PmsErrorCodeEnum.SC_PMS_002_B008.getCode(),PmsErrorCodeEnum.SC_PMS_002_B008.getErrorMsg() + sb.toString());
            }
        }

        if(CollectionUtils.isEmpty(demandBillHandleDataDTO.getDemandDetailSourceRefPOList())){
            BizExceptions.throwWithCodeAndMsg(PmsErrorCodeEnum.SC_PMS_002_B035.getCode(),PmsErrorCodeEnum.SC_PMS_002_B035.getErrorMsg());
        }


        String goodsSizeStr = iSupplychainBizSysParamRuleService.getValue(PMSSystemParamEnum.DEMAND_GOODS_SIZE);
        int goodsSize = NumberUtil.getInteger(goodsSizeStr,500);
        if(demandBillHandleDataDTO.getDemandGoodsDetailPOList().size() > goodsSize){
            String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B019.getErrorMsg(), new String[]{goodsSize + "",demandBillHandleDataDTO.getDemandGoodsDetailPOList().size() + ""}).getMessage();
            BizExceptions.throwWithCodeAndMsg(PmsErrorCodeEnum.SC_PMS_002_B019.getCode(),message);
        }
    }

    /**
     * 需求单加redis锁
     * @param billNo
     */
    private void lockDemandBill(String billNo) {
        String redisKey = "demand_bill_no:" + billNo;
        if (!redisUtil.tryLock(redisKey, billNo, EXPIRE_TIME)){
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B005);
        }
    }

    /**
     * 释放需求单的redis操作锁
     * @param billNo
     */
    private void unLockDemandBill(String billNo) {
        String redisKey = "demand_bill_no:" + billNo;
        redisUtil.unlock(redisKey);
    }

    /**
     * 查询需求单的配转采数据
     * @param param
     * @return
     */
    @Override
    public PmsDemandDeliveryToPurchResultDTO listDeliveryToPurch(QueryDemandDeliveryToPurchReq param) {
        PmsDemandDeliveryToPurchResultDTO pmsDemandDeliveryToPurchResultDTO = new PmsDemandDeliveryToPurchResultDTO();

        //查询配转采数据列表
        LambdaQueryWrapper<PmsDemandDeliveryToPurchPO> deliveryToPurchWrapper = new LambdaQueryWrapper();
        deliveryToPurchWrapper.eq(PmsDemandDeliveryToPurchPO::getBillNo,param.getBillNo());
        List<PmsDemandDeliveryToPurchPO> pmsDemandDeliveryToPurchPOS = pmsDemandDeliveryToPurchRepositoryService.getPmsDemandDeliveryToPurchMapper().selectList(deliveryToPurchWrapper);
        List<PmsDemandDeliveryToPurchResp> deliveryToPurchList = CglibCopier.copy(pmsDemandDeliveryToPurchPOS, PmsDemandDeliveryToPurchResp.class);
        pmsDemandDeliveryToPurchResultDTO.setDeliveryToPurchList(deliveryToPurchList);

        //查询配转采供应商信息
        LambdaUpdateWrapper<PmsDemandPurchShipperPO> purchWrapper = new LambdaUpdateWrapper<>();
        purchWrapper.eq(PmsDemandPurchShipperPO::getBillNo,param.getBillNo());
        purchWrapper.eq(PmsDemandPurchShipperPO::getConvertFlag,1);

        List<PmsDemandPurchShipperPO> pmsDemandPurchShipperPOS = pmsDemandPurchShipperRepositoryService.getPmsDemandPurchShipperMapper().selectList(purchWrapper);
        List<PmsDemandPurchShipperResp> copy = CglibCopier.copy(pmsDemandPurchShipperPOS, PmsDemandPurchShipperResp.class);
        Map<Long, List<PmsDemandPurchShipperResp>> purchShipperResultMap = copy.stream().collect(Collectors.groupingBy(PmsDemandPurchShipperResp::getDeliveryToPurchInsideId));
        pmsDemandDeliveryToPurchResultDTO.setPurchShipperResultMap(purchShipperResultMap);

        //门店配送来源明细
        //配送转采购供应商信息,key=转采数据.insideId
        Map<Long,List<PmsDemandDeliveryToPurchDeliverySourceResp>> deliveryToPurchDeliverySourceMap = new HashMap<>();
        //订货申请订单号、订货申请行号、订货部门编码、订货部门名称、客户编码、客户名称、配送数
        LambdaQueryWrapper<PmsDemandDeliveryToPurchRefPO> deliveryToPurchRefWrapper = new LambdaQueryWrapper();
        deliveryToPurchRefWrapper.eq(PmsDemandDeliveryToPurchRefPO::getBillNo,param.getBillNo());
        List<PmsDemandDeliveryToPurchRefPO> pmsDemandDeliveryToPurchRefPOS = pmsDemandDeliveryToPurchRefRepositoryService.getPmsDemandDeliveryToPurchRefMapper().selectList(deliveryToPurchRefWrapper);

        //key:配转才的单内序号
        Map<Long, List<PmsDemandDeliveryToPurchRefPO>> deliveryToPurchRefMap = pmsDemandDeliveryToPurchRefPOS.stream().collect(Collectors.groupingBy(PmsDemandDeliveryToPurchRefPO::getDeliveryToPurchInsideId));

        LambdaQueryWrapper<PmsDemandDeptGoodsDetailPO> demandDeptGoodsDetailWrapper = new LambdaQueryWrapper<PmsDemandDeptGoodsDetailPO>()
                .eq(PmsDemandDeptGoodsDetailPO::getBillNo, param.getBillNo());
        List<PmsDemandDeptGoodsDetailPO> pmsDemandDeptGoodsDetailPOS = pmsDemandDeptGoodsDetailRepositoryService.getPmsDemandDeptGoodsDetailMapper().selectList(demandDeptGoodsDetailWrapper);
        List<PmsDemandDeptGoodsDetailResp> demandDeptGoodsDetailDTOList = CglibCopier.copy(pmsDemandDeptGoodsDetailPOS, PmsDemandDeptGoodsDetailResp.class);
        Map<Long, PmsDemandDeptGoodsDetailResp> demandDeptGoodsDetailDTOMap = demandDeptGoodsDetailDTOList.stream().collect(Collectors.toMap(PmsDemandDeptGoodsDetailResp::getInsideId,Function.identity()));

        //查询关联表
        LambdaUpdateWrapper<PmsDemandDetailSourceRefPO> detailSourceRefWrapper = new LambdaUpdateWrapper<>();
        detailSourceRefWrapper.eq(PmsDemandDetailSourceRefPO::getDemandBillNo,param.getBillNo());
        List<PmsDemandDetailSourceRefPO> pmsDemandDetailSourceRefPOS = pmsDemandDetailSourceRefRepositoryService.getPmsDemandDetailSourceRefMapper().selectList(detailSourceRefWrapper);

        Map<Long, PmsDemandDetailSourceRefPO> detailSourceRefMap = pmsDemandDetailSourceRefPOS.stream().collect(Collectors.toMap(PmsDemandDetailSourceRefPO::getPinsideId, Function.identity()));

        for (List<PmsDemandDeliveryToPurchRefPO> value : deliveryToPurchRefMap.values()) {
            List<PmsDemandDeliveryToPurchDeliverySourceResp> list = new ArrayList<>();
            for (PmsDemandDeliveryToPurchRefPO pmsDemandDeliveryToPurchRefPO : value) {
                PmsDemandDeliveryToPurchDeliverySourceResp demandDeliveryToPurchDeliverySourceDTO = new PmsDemandDeliveryToPurchDeliverySourceResp();
                if(detailSourceRefMap.containsKey(pmsDemandDeliveryToPurchRefPO.getDeptGoodsInsideId())){
                    PmsDemandDetailSourceRefPO pmsDemandDetailSourceRef = detailSourceRefMap.get(pmsDemandDeliveryToPurchRefPO.getDeptGoodsInsideId());

                    demandDeliveryToPurchDeliverySourceDTO.setApplyBillNo(pmsDemandDetailSourceRef.getApplyBillNo());
                    demandDeliveryToPurchDeliverySourceDTO.setApplyInsideId(pmsDemandDetailSourceRef.getSrcInsideId());
                    demandDeliveryToPurchDeliverySourceDTO.setDeptCode(pmsDemandDetailSourceRef.getOrderDeptCode());
                    demandDeliveryToPurchDeliverySourceDTO.setDeptName(pmsDemandDetailSourceRef.getOrderDeptName());
                    demandDeliveryToPurchDeliverySourceDTO.setCustomerCode(pmsDemandDetailSourceRef.getSrcCustomerCode());
                    demandDeliveryToPurchDeliverySourceDTO.setCustomerName(pmsDemandDetailSourceRef.getSrcCustomerName());

                    if(demandDeptGoodsDetailDTOMap.containsKey(pmsDemandDeliveryToPurchRefPO.getDeptGoodsInsideId())){
                        PmsDemandDeptGoodsDetailResp deptGoodsDetailResult = demandDeptGoodsDetailDTOMap.get(pmsDemandDeliveryToPurchRefPO.getDeptGoodsInsideId());

                        demandDeliveryToPurchDeliverySourceDTO.setDeliveryQty(deptGoodsDetailResult.getResponseQty());
                    }

                    list.add(demandDeliveryToPurchDeliverySourceDTO);
                }
            }
            if(CollectionUtils.isNotEmpty(value)){
                deliveryToPurchDeliverySourceMap.put(value.get(0).getDeliveryToPurchInsideId(),list);
            }
        }

        pmsDemandDeliveryToPurchResultDTO.setDeliveryToPurchDeliverySourceMap(deliveryToPurchDeliverySourceMap);

        return pmsDemandDeliveryToPurchResultDTO;
    }


    /**
     * 查询需求单与订货申请关联关系
     * @param param
     * @return
     */
    @Override
    public List<PmsDemandDetailSourceRefResp> listOrderDetails(ListOrderDetailsReq param) {
        LambdaUpdateWrapper<PmsDemandDetailSourceRefPO> detailSourceRefWrapper = new LambdaUpdateWrapper<>();
        detailSourceRefWrapper.eq(PmsDemandDetailSourceRefPO::getDemandBillNo,param.getBillNo());
        detailSourceRefWrapper.eq(Objects.nonNull(param.getPinsideId()),  PmsDemandDetailSourceRefPO::getPinsideId,param.getBillNo());
        List<PmsDemandDetailSourceRefPO> pmsDemandDetailSourceRefPOS = pmsDemandDetailSourceRefRepositoryService.getPmsDemandDetailSourceRefMapper().selectList(detailSourceRefWrapper);

        List<PmsDemandDetailSourceRefResp> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(pmsDemandDetailSourceRefPOS)){
            list = CglibCopier.copy(pmsDemandDetailSourceRefPOS, PmsDemandDetailSourceRefResp.class);
        }

        return list;
    }

    /**
     * 提交需求单
     * @param pmsDemandBillReq
     * @return
     */
    @Override
    public PmsSubmitBillResp submitDemandBill(PmsDemandBillReq pmsDemandBillReq) {
        LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();
        if(null == loginUser){
            loginUser = new LoginUserDTO();
            loginUser.setUid(0L);
            loginUser.setCode("system");
            loginUser.setName("system");
            ThreadLocals.setValue("_login_user_idaas", loginUser);
        }

        PmsDemandBillResultResp pmsDemandBillResultResp = null;
        BillConvertDataDTO billConvertData = null;

        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transaction = dstManager.getTransaction(def);

        //提交操作
        try{
            if(ObjectUtils.equals(PmsDemanBillOpTypeEnum.STAGING_SUMIT.getCode(), pmsDemandBillReq.getOpType()) || ObjectUtils.equals(PmsDemanBillOpTypeEnum.AUTO_SUBMIT.getCode(), pmsDemandBillReq.getOpType())){
                //先暂存
                handlerStagingDemandBill(pmsDemandBillReq,false);
            }

            QueryDemandDetailReq param = new QueryDemandDetailReq();
            param.setBillNo(pmsDemandBillReq.getBillNo());
            pmsDemandBillResultResp = queryDemandDetail(param);

            lockDemandBill(pmsDemandBillReq.getBillNo());

            BillConvertApplyDataDTO billConvertApplyDataDTO = new BillConvertApplyDataDTO();
            if(ObjectUtils.equals(0,pmsDemandBillResultResp.getPmsDemandBillResp().getBillSource())){
                //处理为空的采购批次,主派
                handlerPurchBatchNo(pmsDemandBillResultResp);

                //主派按门店生成订货申请单号
                billConvertApplyDataDTO = handlerApplyBillNo(pmsDemandBillResultResp);
            }

            if(ObjectUtils.equals(0,pmsDemandBillResultResp.getPmsDemandBillResp().getBillSource())
                    || ObjectUtils.equals(1,pmsDemandBillResultResp.getPmsDemandBillResp().getBillSource())
                    || ObjectUtils.equals(2,pmsDemandBillResultResp.getPmsDemandBillResp().getBillSource())){
                //直流的配送生成停靠点数据
                handlerDock(pmsDemandBillResultResp);
            }


            //处理数据转换
            billConvertData = handlerBillToPruchOrDelivery(pmsDemandBillResultResp);
            billConvertData.setApplyBillList(billConvertApplyDataDTO.getApplyBillList());
            billConvertData.setApplyBillDetailList(billConvertApplyDataDTO.getApplyBillDetailList());

            //落库
            saveBill(billConvertData,pmsDemandBillResultResp);

            dstManager.commit(transaction);
        }catch (BizException e){
            e.printStackTrace();
            Logs.error("PmsDemandDomainServiceImpl.submitDemandBill.BizException", e);
            dstManager.rollback(transaction);
            BizExceptions.throwWithErrorCode(e.getError());
        }catch (Exception e){
            e.printStackTrace();
            Logs.error("PmsDemandDomainServiceImpl.submitDemandBill.Exception", e);
            dstManager.rollback(transaction);
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B010);
        } finally {
            unLockDemandBill(pmsDemandBillReq.getBillNo());
        }

        PmsSubmitBillResp pmsSubmitBillResp = new PmsSubmitBillResp();
        try{
            //根据需求策略自动审核
            pmsSubmitBillResp = autoAuditBill(pmsDemandBillResultResp, billConvertData);
        }catch (BizException e){
            Logs.error("PmsDemandDomainServiceImpl.autoAuditBill.BizException", e);
            BizExceptions.throwWithErrorCode(e.getError());
        }catch (Exception e){
            Logs.error("PmsDemandDomainServiceImpl.autoAuditBill.Exception", e);
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B034);
        }

        return pmsSubmitBillResp;
    }

    /**
     * 根据需求策略自动审核
     * @param pmsDemandBillResultResp
     * @param billConvertData
     * @return
     */
    @Override
    public  PmsSubmitBillResp autoAuditBill(PmsDemandBillResultResp pmsDemandBillResultResp, BillConvertDataDTO billConvertData) {
        PmsSubmitBillResp pmsSubmitBillResp = new PmsSubmitBillResp();
        if(null ==  billConvertData){
            return pmsSubmitBillResp;
        }
        MdDemandStrategyStatusMapping4DeptQueryReq query = new MdDemandStrategyStatusMapping4DeptQueryReq();
        List<String> demandTransferConditionList = new ArrayList<>();
        //0手工,1自动,2追加追减
        if(ObjectUtils.equals(0,pmsDemandBillResultResp.getPmsDemandBillResp().getBillSource())){
            demandTransferConditionList.add(MdDemandStrategyStatusConditionEnum.MANUAL_DEMAND_TO_ORDER_STATUS.getCode());
        }
        else if(ObjectUtils.equals(1,pmsDemandBillResultResp.getPmsDemandBillResp().getBillSource())){
            demandTransferConditionList.add(MdDemandStrategyStatusConditionEnum.AUTO_DEMAND_TO_ORDER_STATUS.getCode());
        }
        else{
            demandTransferConditionList.add(MdDemandStrategyStatusConditionEnum.APPEND_REDUCE_TO_ORDER_STATUS.getCode());
        }

        Map<String,String> deptMap = new HashMap<>();
        if(billConvertData.getPurchBillList().size() > 0 && billConvertData.getPurchBillDetailList().size() > 0){
            for (PmsPurchaseOrderPO pmsPurchaseOrderPO : billConvertData.getPurchBillList()) {
                deptMap.put(pmsPurchaseOrderPO.getDeptCode(),pmsPurchaseOrderPO.getDeptCode());
            }
        }

        if(billConvertData.getDeliveryBillList().size() > 0 && billConvertData.getDeliveryBillDetailList().size() > 0){
            for (WdDeliveryBillPO wdDeliveryBillPO : billConvertData.getDeliveryBillList()) {
                deptMap.put(wdDeliveryBillPO.getInDeptCode(),wdDeliveryBillPO.getInDeptCode());
            }
        }

        if(deptMap.size() == 0){
            return pmsSubmitBillResp;
        }

        List<String> deptCodeList = new ArrayList<>();

        List<String> deptGroupCodeList = new ArrayList<>();

        // 获取部门上级店组群
        QueryBatchDeptListReq req = QueryBatchDeptListReq.builder()
                .classCode(GroupDeptEnum.CONTROL_GROUP.getCode())
                .deptCodeList(new ArrayList<>(deptMap.keySet()))
                .build();
        List<QueryBatchDeptListResp.Rows> groupList = baseDataSystemFeignClient.queryUpDeptListBatch(req).getRows();
        Map<String,List<String>> deptGroupMap = new HashMap<>();
        Map<String,String> groupMap = new HashMap<>();
        for (QueryBatchDeptListResp.Rows row : groupList) {

            deptCodeList.add(row.getCode());
            List<String> groupCodelist = new ArrayList<>();
            for (QueryBatchDeptListResp.DeptGroup deptGroup : row.getDeptGroupList()) {
                groupMap.put(deptGroup.getCode(),deptGroup.getCode());
                groupCodelist.add(deptGroup.getCode());
            }
            deptGroupMap.put(row.getCode(),groupCodelist);
        }
        deptGroupCodeList.addAll(new ArrayList<>(deptGroupMap.keySet()));
        query.setDemandTransferConditionList(demandTransferConditionList);
//        query.setDeptCodeList(deptCodeList);
//        query.setDeptGroupCodeList(deptGroupCodeList);
        List<MdDemandStrategyStatusMapping4DeptDTO> mdDemandStrategyStatusMapping4DeptDTOS = mdDemandStrategyDomainService.queryMdDemandStrategyStatusMappingData(query);

        Map<String, MdDemandStrategyStatusMapping4DeptDTO> demandStrategyMap = mdDemandStrategyStatusMapping4DeptDTOS.parallelStream().collect(Collectors.toMap(MdDemandStrategyStatusMapping4DeptDTO::getDemandTransferConditionBiz, Function.identity()));

        Logs.info("PmsDemandDomainServiceImpl.autoAuditBill.demandStrategyMap:" + JSON.toJSONString(demandStrategyMap));

        List<String> purchAuditBillNoList = new ArrayList<>();
        List<String> deliveryAuditBillNoList = new ArrayList<>();

        Map<String, PmsDemandPruchDeliveryRefPO> pruchDeliveryRefPOMap = billConvertData.getPruchDeliveryRefList().stream().collect(Collectors.toMap(PmsDemandPruchDeliveryRefPO::getRefBillNo, Function.identity(),(value1,value2)->value2));

        //配送采购订货
        //门店采购订货
        //直流采购订货
        //配送转采购
        if(billConvertData.getPurchBillList().size() > 0 && billConvertData.getPurchBillDetailList().size() > 0){
            for (PmsPurchaseOrderPO pmsPurchaseOrderPO : billConvertData.getPurchBillList()) {
                //采购类型	0-门店采购，1-配送采购

                MdDemandStrategyStatusMapping4DeptDTO mdDemandStrategyStatusMapping4DeptDTO = new MdDemandStrategyStatusMapping4DeptDTO();
                if(ObjectUtils.equals(1,pmsPurchaseOrderPO.getTransferPurchSign())){
                    //配送转采购
                    mdDemandStrategyStatusMapping4DeptDTO = demandStrategyMap.get(MdDemandStrategyBizConditionEnum.DIST_TO_PURCHASE.getCode());

                }
                else if(ObjectUtils.equals(0,pmsPurchaseOrderPO.getBillType()) && ObjectUtils.equals(0,pmsPurchaseOrderPO.getDirectSign())){
                    //门店 非直流
                    mdDemandStrategyStatusMapping4DeptDTO = demandStrategyMap.get(MdDemandStrategyBizConditionEnum.STORE_PURCHASE_ORDER.getCode());

                }else if(ObjectUtils.equals(1,pmsPurchaseOrderPO.getBillType()) && ObjectUtils.equals(1,pmsPurchaseOrderPO.getDirectSign())){
                    //直流采购订单
                    mdDemandStrategyStatusMapping4DeptDTO = demandStrategyMap.get(MdDemandStrategyBizConditionEnum.DIRECT_PURCHASE_ORDER.getCode());

                }else{
                    //配送采购订货
                    mdDemandStrategyStatusMapping4DeptDTO = demandStrategyMap.get(MdDemandStrategyBizConditionEnum.DIST_PURCHASE_ORDER.getCode());
                }
                if(null == mdDemandStrategyStatusMapping4DeptDTO){
                    continue;
                }
                //-1:采退，1:采购
                if((MdBillTransferStatusEnum.TRANSFERRED.getCode() + "").equals(mdDemandStrategyStatusMapping4DeptDTO.getVal())){
                    String billDirection = pmsPurchaseOrderPO.getBillDirection() + "";
                    if("-1".equals(billDirection)){
                        billDirection = "2";
                    }
                    if(mdDemandStrategyStatusMapping4DeptDTO.getDemandBillTypeList().contains(billDirection)){
                        if(CollectionUtils.isEmpty(mdDemandStrategyStatusMapping4DeptDTO.getDeptCodeList())
                            && CollectionUtils.isEmpty(mdDemandStrategyStatusMapping4DeptDTO.getDeptGroupCodeList())){
                            purchAuditBillNoList.add(pmsPurchaseOrderPO.getBillNo());
                            if(pruchDeliveryRefPOMap.containsKey(pmsPurchaseOrderPO.getBillNo())){
                                pruchDeliveryRefPOMap.get(pmsPurchaseOrderPO.getBillNo()).setTransferSign(1);
                                pruchDeliveryRefPOMap.get(pmsPurchaseOrderPO.getBillNo()).setTransferStatus(1);
                            }

                        }
                        else{
                            List<String> groupCodelist  = deptGroupMap.get(pmsPurchaseOrderPO.getDeptCode());
                            if(mdDemandStrategyStatusMapping4DeptDTO.getDeptCodeList().contains(pmsPurchaseOrderPO.getDeptCode())){
                                purchAuditBillNoList.add(pmsPurchaseOrderPO.getBillNo());
                                if(pruchDeliveryRefPOMap.containsKey(pmsPurchaseOrderPO.getBillNo())){
                                    pruchDeliveryRefPOMap.get(pmsPurchaseOrderPO.getBillNo()).setTransferSign(1);
                                    pruchDeliveryRefPOMap.get(pmsPurchaseOrderPO.getBillNo()).setTransferStatus(1);
                                }
                            }
                            else{
                                List<String> newGroupCodelist = new ArrayList<>(groupCodelist);
                                newGroupCodelist.retainAll(mdDemandStrategyStatusMapping4DeptDTO.getDeptGroupCodeList());
                                if(CollectionUtils.isNotEmpty(newGroupCodelist)){
                                    purchAuditBillNoList.add(pmsPurchaseOrderPO.getBillNo());
                                    if(pruchDeliveryRefPOMap.containsKey(pmsPurchaseOrderPO.getBillNo())){
                                        pruchDeliveryRefPOMap.get(pmsPurchaseOrderPO.getBillNo()).setTransferSign(1);
                                        pruchDeliveryRefPOMap.get(pmsPurchaseOrderPO.getBillNo()).setTransferStatus(1);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Logs.info("PmsDemandDomainServiceImpl.autoAuditBill.purchAuditBillNoList:" + JSON.toJSONString(purchAuditBillNoList));

        //配送转采购//
        //门店配送订货（非直流）
        //门店配送订货（直流）
        if(billConvertData.getDeliveryBillList().size() > 0 && billConvertData.getDeliveryBillDetailList().size() > 0){
            for (WdDeliveryBillPO wdDeliveryBillPO : billConvertData.getDeliveryBillList()) {
                MdDemandStrategyStatusMapping4DeptDTO mdDemandStrategyStatusMapping4DeptDTO = new MdDemandStrategyStatusMapping4DeptDTO();
                if(ObjectUtils.equals(1,wdDeliveryBillPO.getDirectSign())){
                    //直流
                    mdDemandStrategyStatusMapping4DeptDTO = demandStrategyMap.get(MdDemandStrategyBizConditionEnum.STORE_DIST_ORDER_DIRECT.getCode());
                }
                else {
                    mdDemandStrategyStatusMapping4DeptDTO = demandStrategyMap.get(MdDemandStrategyBizConditionEnum.STORE_DIST_ORDER_NON_DIRECT.getCode());
                }

                if(null == mdDemandStrategyStatusMapping4DeptDTO){
                    continue;
                }

                //-1:采退，1:采购
                if((MdBillTransferStatusEnum.TRANSFERRED.getCode() + "").equals(mdDemandStrategyStatusMapping4DeptDTO.getVal())){
                    String billDirection = wdDeliveryBillPO.getBillDirection() + "";
                    if("-1".equals(billDirection)){
                        billDirection = "2";
                    }
                    if(mdDemandStrategyStatusMapping4DeptDTO.getDemandBillTypeList().contains(billDirection)){
                        if(CollectionUtils.isEmpty(mdDemandStrategyStatusMapping4DeptDTO.getDeptCodeList())
                                && CollectionUtils.isEmpty(mdDemandStrategyStatusMapping4DeptDTO.getDeptGroupCodeList())){
                            deliveryAuditBillNoList.add(wdDeliveryBillPO.getBillNo());

                            if(pruchDeliveryRefPOMap.containsKey(wdDeliveryBillPO.getBillNo())){
                                pruchDeliveryRefPOMap.get(wdDeliveryBillPO.getBillNo()).setTransferSign(1);
                                pruchDeliveryRefPOMap.get(wdDeliveryBillPO.getBillNo()).setTransferStatus(1);
                            }
                        }
                        else{
                            List<String> groupCodelist  = deptGroupMap.get(wdDeliveryBillPO.getInDeptCode());
                            if(mdDemandStrategyStatusMapping4DeptDTO.getDeptCodeList().contains(wdDeliveryBillPO.getInDeptCode())){
                                deliveryAuditBillNoList.add(wdDeliveryBillPO.getBillNo());

                                if(pruchDeliveryRefPOMap.containsKey(wdDeliveryBillPO.getBillNo())){
                                    pruchDeliveryRefPOMap.get(wdDeliveryBillPO.getBillNo()).setTransferSign(1);
                                    pruchDeliveryRefPOMap.get(wdDeliveryBillPO.getBillNo()).setTransferStatus(1);
                                }
                            }
                            else{
                                List<String> newGroupCodelist = new ArrayList<>(groupCodelist);
                                newGroupCodelist.retainAll(mdDemandStrategyStatusMapping4DeptDTO.getDeptGroupCodeList());
                                if(CollectionUtils.isNotEmpty(newGroupCodelist)){
                                    deliveryAuditBillNoList.add(wdDeliveryBillPO.getBillNo());

                                    if(pruchDeliveryRefPOMap.containsKey(wdDeliveryBillPO.getBillNo())){
                                        pruchDeliveryRefPOMap.get(wdDeliveryBillPO.getBillNo()).setTransferSign(1);
                                        pruchDeliveryRefPOMap.get(wdDeliveryBillPO.getBillNo()).setTransferStatus(1);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        Logs.info("PmsDemandDomainServiceImpl.autoAuditBill.deliveryAuditBillNoList:" + JSON.toJSONString(deliveryAuditBillNoList));

        if(CollectionUtils.isNotEmpty(purchAuditBillNoList)){
            //自动审核采购订单
            PurchaseBillOptReq auditReq = new PurchaseBillOptReq();
            auditReq.setBillNoList(purchAuditBillNoList);
            auditReq.setOptTypeEnum(PmsPurchOptTypeEnum.AUDIT);

            Logs.info("PmsDemandDomainServiceImpl.autoAuditBill.purch.req:" + JSON.toJSONString(auditReq));

            try{
                Result<List<PurchBillOptResp>> listResult = getPmsPurchBillApplicationService().batchAuditPurchBill(auditReq);

                Logs.info("PmsDemandDomainServiceImpl.autoAuditBill.purch.resp:" + JSON.toJSONString(listResult));
                List<PmsSubmitBillResp.FailedInfo> purchfailedInfoList = new ArrayList<>();

                if(listResult.isSuccess()){
                    int successCnt = 0;
                    for (PurchBillOptResp datum : listResult.getData()) {
                        if(StringUtils.isEmpty(datum.getFailedReason())){
                            successCnt++;

                            if(pruchDeliveryRefPOMap.containsKey(datum.getBillNo())){
                                pruchDeliveryRefPOMap.get(datum.getBillNo()).setTransferStatus(1);
                            }

                        }
                        else{
                            PmsSubmitBillResp.FailedInfo faild = PmsSubmitBillResp.FailedInfo.builder().billNo(datum.getBillNo()).failReason(datum.getFailedReason()).build();
                            purchfailedInfoList.add(faild);

                            if(pruchDeliveryRefPOMap.containsKey(datum.getBillNo())){
                                pruchDeliveryRefPOMap.get(datum.getBillNo()).setTransferStatus(2);
                                if(StringUtils.isNotEmpty(datum.getFailedReason())){
                                    String failedReason = datum.getFailedReason();
                                    if(failedReason.length() > 1024){
                                        failedReason = failedReason.substring(0,1000);
                                    }
                                    pruchDeliveryRefPOMap.get(datum.getBillNo()).setTransferMsg(failedReason);
                                }

                            }
                        }


                    }
                    pmsSubmitBillResp.setPurchSuccessCnt(successCnt);
                }

                pmsSubmitBillResp.setPurchfailedInfoList(purchfailedInfoList);
            }
            catch (Exception e){

                List<PmsSubmitBillResp.FailedInfo> purchfailedInfoList = new ArrayList<>();
                for (String s : purchAuditBillNoList) {
                    String failedReason = StringUtils.isNotEmpty(e.getMessage()) ? e.getMessage() : PmsErrorCodeEnum.SC_PMS_002_B039.getDesc();
                    if(pruchDeliveryRefPOMap.containsKey(s)){
                        pruchDeliveryRefPOMap.get(s).setTransferStatus(2);
                        if(StringUtils.isNotEmpty(failedReason)){
                            if(failedReason.length() > 1024){
                                failedReason = failedReason.substring(0,1000);
                            }
                        }
                    }
                    pruchDeliveryRefPOMap.get(s).setTransferMsg(failedReason);
                    PmsSubmitBillResp.FailedInfo faild = PmsSubmitBillResp.FailedInfo.builder().billNo(s).failReason(failedReason).build();
                    purchfailedInfoList.add(faild);
                }
                pmsSubmitBillResp.setPurchfailedInfoList(purchfailedInfoList);

                e.printStackTrace();;
                Logs.error("PmsDemandDomainServiceImpl.autoAuditBill.purch.error.",e);
            }


        }


        if(CollectionUtils.isNotEmpty(deliveryAuditBillNoList)){
            //自动审核配送订单
            WdDeliveryBillBatchOptReq batchOptReq = new WdDeliveryBillBatchOptReq();
            batchOptReq.setBillNoList(deliveryAuditBillNoList);
            batchOptReq.setOptTypeEnum(WDDeliveryOptTypeEnum.AUDIT);
            OpInfo opInfo = userUtil.getOpInfoWithThrow();
            batchOptReq.setOpInfo(opInfo);

            Logs.info("PmsDemandDomainServiceImpl.autoAuditBill.delivery.req:" + JSON.toJSONString(batchOptReq));

            pmsSubmitBillResp.setDeliveryCnt(deliveryAuditBillNoList.size());
            try{
                WdBatchOptResp wdBatchOptResp = wdsFeignClient.batchAudit(batchOptReq);

                Logs.info("PmsDemandDomainServiceImpl.autoAuditBill.delivery.resp:" + JSON.toJSONString(wdBatchOptResp));
                List<PmsSubmitBillResp.FailedInfo> deliveryfailedInfoList = new ArrayList<>();
                pmsSubmitBillResp.setDeliverySuccessCnt(wdBatchOptResp.getSuccessCount());
                if(CollectionUtils.isNotEmpty(wdBatchOptResp.getFailedInfoList())){
                    for (WdBatchOptResp.FailedInfo failedInfo : wdBatchOptResp.getFailedInfoList()) {

                        PmsSubmitBillResp.FailedInfo faild = PmsSubmitBillResp.FailedInfo.builder().billNo(failedInfo.getBillNo()).failReason(failedInfo.getFailReason()).build();
                        deliveryfailedInfoList.add(faild);

                        if(pruchDeliveryRefPOMap.containsKey(failedInfo.getBillNo())){
                            pruchDeliveryRefPOMap.get(failedInfo.getBillNo()).setTransferStatus(2);
                            if(StringUtils.isNotEmpty(failedInfo.getFailReason())){
                                String failedReason = failedInfo.getFailReason();
                                if(failedReason.length() > 1024){
                                    failedReason = failedReason.substring(0,1000);
                                }
                                pruchDeliveryRefPOMap.get(failedInfo.getBillNo()).setTransferMsg(failedReason);
                            }
                        }
                    }
                }

                pmsSubmitBillResp.setDeliveryfailedInfoList(deliveryfailedInfoList);
            }
            catch (Exception e){

                List<PmsSubmitBillResp.FailedInfo> deliveryfailedInfoList = new ArrayList<>();
                for (String s : deliveryAuditBillNoList) {
                    String failedReason = StringUtils.isNotEmpty(e.getMessage()) ? e.getMessage() : PmsErrorCodeEnum.SC_PMS_002_B040.getDesc();
                    if(pruchDeliveryRefPOMap.containsKey(s)){
                        pruchDeliveryRefPOMap.get(s).setTransferStatus(2);
                        if(StringUtils.isNotEmpty(failedReason)){
                            if(failedReason.length() > 1024){
                                failedReason = failedReason.substring(0,1000);
                            }
                        }
                    }
                    pruchDeliveryRefPOMap.get(s).setTransferMsg(failedReason);
                    PmsSubmitBillResp.FailedInfo faild = PmsSubmitBillResp.FailedInfo.builder().billNo(s).failReason(failedReason).build();
                    deliveryfailedInfoList.add(faild);
                }
                pmsSubmitBillResp.setDeliveryfailedInfoList(deliveryfailedInfoList);

                e.printStackTrace();;
                Logs.error("PmsDemandDomainServiceImpl.autoAuditBill.delivery.error.",e);
            }

        }

        if(pruchDeliveryRefPOMap.size() > 0){
            List<PmsDemandPruchDeliveryRefPO> pmsDemandPruchDeliveryRefPOS = new ArrayList<>(pruchDeliveryRefPOMap.values());

            pmsDemandPruchDeliveryRefRepositoryService.getPmsDemandPruchDeliveryRefMapper().updateBatch(pmsDemandPruchDeliveryRefPOS);
        }

        return pmsSubmitBillResp;
    }

    /**
     * 直流的配送生成停靠点数据
     * @param pmsDemandBillResultResp
     */
    private void handlerDock(PmsDemandBillResultResp pmsDemandBillResultResp) {
        if(!OrderAttributeEnum.JIN_JI.getCode().equals(pmsDemandBillResultResp.getPmsDemandBillResp().getOrderAttributeCode())
                && PmsBillDirectionEnum.NORMAL.getCode().equals(pmsDemandBillResultResp.getPmsDemandBillResp().getBillDirection())){

            //配送传入停靠点数据
            //批量根据门店查询所有店组群信息
            //上级店组群信息
            Map<String, QueryBatchDeptListResp.Rows> upDeptMap = Maps.newHashMap();

            //key:要货部门编码  value:商品编码
            Map<String,List<String>> deptGoodsMap = new HashMap<>();

            Set<String> categoryCodeSet = new HashSet<>();
            Set<String> skuCodeSet = new HashSet<>();
            Set<String> deliveryDeptCodeSet = new HashSet<>();

            Map<String, PmsDemandGoodsDetailPO> goodsDetailMap = pmsDemandBillResultResp.getDemandBillHandleData().getDemandGoodsDetailPOList().parallelStream()
                    .collect(Collectors.toMap(PmsDemandGoodsDetailPO::getSkuCode, Function.identity()));

            for (PmsDemandDeliveryShipperPO pmsDemandDeliveryShipperPO : pmsDemandBillResultResp.getDemandBillHandleData().getDemandDeliveryShipperPOList()) {
                if(!ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),pmsDemandDeliveryShipperPO.getDirectSign())){
                    continue;
                }

                if(ObjectUtils.equals(0,pmsDemandDeliveryShipperPO.getStatus())){
                    continue;
                }

                PmsDemandGoodsDetailPO pmsDemandGoodsDetailPO = goodsDetailMap.get(pmsDemandDeliveryShipperPO.getSkuCode());
                String categoryCodeAll = pmsDemandGoodsDetailPO.getCategoryCodeAll();

                if(StringUtils.isEmpty(categoryCodeAll)){
                    List<String> categoryCodeList = new ArrayList<>();
                    categoryCodeList.add(pmsDemandGoodsDetailPO.getCategoryCode());
                    List<CategoryCodeAll> categoryCodeAll1 = supplychainPmsBizRuleEngineService.getCategoryCodeAll(categoryCodeList);
                    if(CollectionUtils.isNotEmpty(categoryCodeAll1)){
                        categoryCodeAll = categoryCodeAll1.get(0).getCategoryCodeAll();
                        pmsDemandGoodsDetailPO.setCategoryCodeAll(categoryCodeAll);
                    }
                }

                if(StringUtils.isNotEmpty(categoryCodeAll)){
                    String[] split = categoryCodeAll.split(",");
                    if(split.length > 0){
                        List<String> list = Arrays.asList(split);
                        categoryCodeSet.addAll(list);
                    }
                }
                skuCodeSet.add(pmsDemandDeliveryShipperPO.getSkuCode());
                deliveryDeptCodeSet.add(pmsDemandDeliveryShipperPO.getDistDeptCode());

                if(deptGoodsMap.containsKey(pmsDemandDeliveryShipperPO.getOrderDeptCode())){
                    List<String> skuCodelist = deptGoodsMap.get(pmsDemandDeliveryShipperPO.getOrderDeptCode());
                    skuCodelist.add(pmsDemandDeliveryShipperPO.getSkuCode());
                }
                else{
                    List<String> skuCodelist = new ArrayList<>();
                    skuCodelist.add(pmsDemandDeliveryShipperPO.getSkuCode());
                    deptGoodsMap.put(pmsDemandDeliveryShipperPO.getOrderDeptCode(),skuCodelist);
                }
            }

            if(deptGoodsMap.size() == 0){
                return;
            }

            DeliveryAppointmentStrategyResultDTO deliveryAppointmentStrategyResultDTO = mdDeliveryAppointmentStrategyDomainService.handleDock(upDeptMap,
                    deptGoodsMap, categoryCodeSet, skuCodeSet, deliveryDeptCodeSet);

            //key:停靠点编码 val:停靠点信息
            Map<String, MdDeliveryDockStrategyPO> dockMap = deliveryAppointmentStrategyResultDTO.getDockStrategyPOList().stream().collect(Collectors.toMap(MdDeliveryDockStrategyPO::getDockCode, Function.identity()));

            //key:配送部门编码 + 部门类型 + 部门编码 val:停靠点编码
            Map<String, String> dockDeptMap = new HashMap<>();

            List<MdDeliveryDockDeptPO> dockDeptPOList = deliveryAppointmentStrategyResultDTO.getDockDeptPOList();
            for (MdDeliveryDockDeptPO mdDeliveryDockDeptPO : dockDeptPOList) {
                if(dockMap.containsKey(mdDeliveryDockDeptPO.getDockCode())){
                    MdDeliveryDockStrategyPO mdDeliveryDockStrategyPO = dockMap.get(mdDeliveryDockDeptPO.getDockCode());
                    String key = mdDeliveryDockStrategyPO.getDeptCode() + "_" + mdDeliveryDockDeptPO.getDeptType() + "_" + mdDeliveryDockDeptPO.getDeptCode();
                    dockDeptMap.put(key,mdDeliveryDockDeptPO.getDockCode());
                }
            }

            //key:配送部门编码 + 类型 + 商品/品类编码 val:停靠点编码
            Map<String, String> goodsMap = new HashMap<>();
            for (MdDeliveryDockGoodsCategoryPO mdDeliveryDockGoodsCategoryPO : deliveryAppointmentStrategyResultDTO.getDockGoodsCategoryPOList()) {
                if(dockMap.containsKey(mdDeliveryDockGoodsCategoryPO.getDockCode())){
                    MdDeliveryDockStrategyPO mdDeliveryDockStrategyPO = dockMap.get(mdDeliveryDockGoodsCategoryPO.getDockCode());
                    //定义类型（1商品；2品类）
                    Integer type = mdDeliveryDockGoodsCategoryPO.getType();
                    String key = mdDeliveryDockStrategyPO.getDeptCode() + "_" + mdDeliveryDockGoodsCategoryPO.getType()
                            + "_" + (ObjectUtils.equals(1,type) ? mdDeliveryDockGoodsCategoryPO.getSkuCode() : mdDeliveryDockGoodsCategoryPO.getClassCode());
                    goodsMap.put(key,mdDeliveryDockGoodsCategoryPO.getDockCode());
                }
            }

            Map<Long, List<PmsDemandPurchShipperPO>> purchShipperPOMap = pmsDemandBillResultResp.getDemandBillHandleData().getDemandPurchShipperPOList().stream().collect(Collectors.groupingBy(PmsDemandPurchShipperPO::getPinsideId));

            for (PmsDemandDeliveryShipperPO pmsDemandDeliveryShipperPO : pmsDemandBillResultResp.getDemandBillHandleData().getDemandDeliveryShipperPOList()) {
                if (!ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(), pmsDemandDeliveryShipperPO.getDirectSign())) {
                    continue;
                }

                if(ObjectUtils.equals(0,pmsDemandDeliveryShipperPO.getStatus())){
                    continue;
                }

                //写入停靠点信息
                Set<String> dockSet = new HashSet<>();
                Map<String,String> deliveryCockMap = new HashMap<>();
                //配送部门编码
                String distDeptCode = pmsDemandDeliveryShipperPO.getDistDeptCode();

                //要货部门编码
                String deptCode = pmsDemandDeliveryShipperPO.getOrderDeptCode();
                //1 店组群； 2 部门
                String dockKey = distDeptCode + "_2_" +  deptCode;
                if(dockDeptMap.containsKey(dockKey)){
                    String s = dockDeptMap.get(dockKey);
                    deliveryCockMap.put(s,s);
                }
                //先根据要货部门编码筛选停靠点
                if(upDeptMap.containsKey(deptCode)){
                    QueryBatchDeptListResp.Rows rows = upDeptMap.get(deptCode);
                    for (QueryBatchDeptListResp.DeptGroup deptGroup : rows.getDeptGroupList()) {
                        dockKey = distDeptCode + "_1_" +  deptGroup.getClassCode();
                        if(dockDeptMap.containsKey(dockKey)){
                            String s = dockDeptMap.get(dockKey);
                            deliveryCockMap.put(s,s);

                        }
                    }
                }
                //1商品；2品类
                String skuCode = pmsDemandDeliveryShipperPO.getSkuCode();
                dockKey = distDeptCode + "_1_" +  skuCode;
                if(goodsMap.containsKey(dockKey)){
                    String s = goodsMap.get(dockKey);
                    if(deliveryCockMap.containsKey(s)){
                        dockSet.add(s);
                    }
                }

                PmsDemandGoodsDetailPO pmsDemandGoodsDetailPO = goodsDetailMap.get(pmsDemandDeliveryShipperPO.getSkuCode());
                String categoryCodeAll = pmsDemandGoodsDetailPO.getCategoryCodeAll();

                if(StringUtils.isEmpty(categoryCodeAll)){
                    List<String> categoryCodeList = new ArrayList<>();
                    categoryCodeList.add(pmsDemandGoodsDetailPO.getCategoryCode());
                    List<CategoryCodeAll> categoryCodeAll1 = supplychainPmsBizRuleEngineService.getCategoryCodeAll(categoryCodeList);
                    if(CollectionUtils.isNotEmpty(categoryCodeAll1)){
                        categoryCodeAll = categoryCodeAll1.get(0).getCategoryCodeAll();
                        pmsDemandGoodsDetailPO.setCategoryCodeAll(categoryCodeAll);
                    }
                }

                if(StringUtils.isNotEmpty(categoryCodeAll)){
                    String[] split = categoryCodeAll.split(",");
                    if(split.length > 0){
                        for (String categoryCode : split) {
                            dockKey = distDeptCode + "_2_" +  categoryCode;
                            if(goodsMap.containsKey(dockKey)){
                                String s = goodsMap.get(dockKey);
                                if(deliveryCockMap.containsKey(s)){
                                    dockSet.add(s);
                                }
                            }
                        }
                    }
                }

                if(CollectionUtils.isNotEmpty(dockSet)){
                    ArrayList<String> dockList = new ArrayList<>(dockSet);
                    Collections.sort(dockList);
                    String dockCode = dockList.get(0);
                    pmsDemandDeliveryShipperPO.setDockCode(dockCode);
                    MdDeliveryDockStrategyPO mdDeliveryDockStrategyPO = dockMap.get(dockCode);
                    if(null != mdDeliveryDockStrategyPO){
                        pmsDemandDeliveryShipperPO.setDockName(mdDeliveryDockStrategyPO.getDockName());
                    }
                }
                if(purchShipperPOMap.containsKey(pmsDemandDeliveryShipperPO.getPinsideId())){
                    List<PmsDemandPurchShipperPO> pmsDemandPurchShipperPOS = purchShipperPOMap.get(pmsDemandDeliveryShipperPO.getPinsideId());
                    for (PmsDemandPurchShipperPO pmsDemandPurchShipperPO : pmsDemandPurchShipperPOS) {
                        pmsDemandPurchShipperPO.setDockCode(pmsDemandDeliveryShipperPO.getDockCode());
                        pmsDemandPurchShipperPO.setDockName(pmsDemandDeliveryShipperPO.getDockName());
                    }
                }
            }

        }


    }

    /**
     * 处理需求批次
     * @param pmsDemandBillResultResp
     */
    private void handlerPurchBatchNo(PmsDemandBillResultResp pmsDemandBillResultResp) {
        if(!OrderAttributeEnum.JIN_JI.getCode().equals(pmsDemandBillResultResp.getPmsDemandBillResp().getOrderAttributeCode())
            && PmsBillDirectionEnum.NORMAL.getCode().equals(pmsDemandBillResultResp.getPmsDemandBillResp().getBillDirection())){
            Date curDate = new Date();

//            Map<String, PmsDemandGoodsDetailPO> goodsMap = pmsDemandBillResultResp.getDemandBillHandleData().getDemandGoodsDetailPOList().parallelStream()
//                    .collect(Collectors.toMap(PmsDemandGoodsDetailPO::getSkuCode, Function.identity()));
            Map<String, PmsDemandGoodsDetailPO> goodsMap = new HashMap<>();
            for (PmsDemandGoodsDetailPO pmsDemandGoodsDetailPO : pmsDemandBillResultResp.getDemandBillHandleData().getDemandGoodsDetailPOList()) {
                String key = pmsDemandGoodsDetailPO.getGoodsType() + "_" + pmsDemandGoodsDetailPO.getSkuCode();
                goodsMap.put(key,pmsDemandGoodsDetailPO);
            }

            Map<Long, PmsDemandDeptGoodsDetailPO> deptGoodsDetailMap = pmsDemandBillResultResp.getDemandBillHandleData().getDemandDeptGoodsDetailPOList().stream()
                    .collect(Collectors.toMap(PmsDemandDeptGoodsDetailPO::getInsideId, Function.identity()));

            String deliveryDaysStr = iSupplychainBizSysParamRuleService.getValue(PMSSystemParamEnum.DEMAND_DELIVER_ORDER_DELIVER_DAYS);

            int deliveryDays = NumberUtil.getInteger(deliveryDaysStr,7);
            GeneratePurchBatchDTO generatePurchBatchDTO = new GeneratePurchBatchDTO();
            generatePurchBatchDTO.setDeliverDays(deliveryDays);
            generatePurchBatchDTO.setDate(curDate);
            List<DemandBatchRecordGoodsDTO> demandBatchRecordGoodsList = new ArrayList<>();

            Map<String, DemandBatchRecordGoodsDTO> demandBatchRecordGoodsDTOMap = new HashMap<>();
            for (PmsDemandDeliveryShipperPO pmsDemandDeliveryShipperPO : pmsDemandBillResultResp.getDemandBillHandleData().getDemandDeliveryShipperPOList()) {
                if(StringUtils.isEmpty(pmsDemandDeliveryShipperPO.getPurchBatchNo())
                && ObjectUtils.equals(2,deptGoodsDetailMap.get(pmsDemandDeliveryShipperPO.getPinsideId()).getType())){
                    String key = pmsDemandDeliveryShipperPO.getDistDeptCode() + "_" + pmsDemandDeliveryShipperPO.getSkuCode();

                    if(demandBatchRecordGoodsDTOMap.containsKey(key)){
                        continue;
                    }

                    DemandBatchRecordGoodsDTO demandBatchRecordGoodsDTO = new DemandBatchRecordGoodsDTO();
                    demandBatchRecordGoodsDTO.setDeptCode(pmsDemandDeliveryShipperPO.getDistDeptCode());
                    demandBatchRecordGoodsDTO.setSkuCode(pmsDemandDeliveryShipperPO.getSkuCode());
                    String goodsKey = pmsDemandDeliveryShipperPO.getGoodsType() + "_" + pmsDemandDeliveryShipperPO.getSkuCode();
                    if(!goodsMap.containsKey(goodsKey)){
                        continue;
                    }
                    demandBatchRecordGoodsDTO.setCategoryCode(goodsMap.get(goodsKey).getCategoryCode());

                    demandBatchRecordGoodsDTOMap.put(key,demandBatchRecordGoodsDTO);
                    demandBatchRecordGoodsList.add(demandBatchRecordGoodsDTO);
                }
            }

            generatePurchBatchDTO.setDemandBatchRecordGoodsList(demandBatchRecordGoodsList);
            if(CollectionUtils.isNotEmpty(demandBatchRecordGoodsList)){
                supplychainPmsBizRuleEngineService.generateProcurementBatch(generatePurchBatchDTO);
                for (PmsDemandDeliveryShipperPO pmsDemandDeliveryShipperPO : pmsDemandBillResultResp.getDemandBillHandleData().getDemandDeliveryShipperPOList()) {
                    if(StringUtils.isEmpty(pmsDemandDeliveryShipperPO.getPurchBatchNo())
                            && ObjectUtils.equals(2,deptGoodsDetailMap.get(pmsDemandDeliveryShipperPO.getPinsideId()).getType())){
                        String key = pmsDemandDeliveryShipperPO.getDistDeptCode() + "_" + pmsDemandDeliveryShipperPO.getSkuCode();
                        if(demandBatchRecordGoodsDTOMap.containsKey(key)){
                            DemandBatchRecordGoodsDTO demandBatchRecordGoodsDTO = demandBatchRecordGoodsDTOMap.get(key);
                            pmsDemandDeliveryShipperPO.setPurchBatchNo(demandBatchRecordGoodsDTO.getPurchBatchNo());
                            if(deptGoodsDetailMap.containsKey(pmsDemandDeliveryShipperPO.getPinsideId())){
                                deptGoodsDetailMap.get(pmsDemandDeliveryShipperPO.getPinsideId()).setPurchBatchNo(demandBatchRecordGoodsDTO.getPurchBatchNo());
                            }
                        }
                    }
                }
            }

        }
    }

    /**
     * 主派按门店生成订货申请单号
     * @param pmsDemandBillResultResp
     * @return
     */
    private BillConvertApplyDataDTO handlerApplyBillNo(PmsDemandBillResultResp pmsDemandBillResultResp) {
        BillConvertApplyDataDTO billConvertApplyDataDTO = new BillConvertApplyDataDTO();
        Map<String, List<PmsDemandDetailSourceRefPO>> detailSourceRefMap = new HashMap<>();
        List<BatchGenerateBillNoDTO> list = new ArrayList<>();

        Map<String,Integer> deptTypeMap = new HashMap<>();
        for (PmsDemandDeptGoodsDetailPO pmsDemandDeptGoodsDetailPO : pmsDemandBillResultResp.getDemandBillHandleData().getDemandDeptGoodsDetailPOList()) {
            deptTypeMap.put(pmsDemandDeptGoodsDetailPO.getOrderDeptCode(),pmsDemandDeptGoodsDetailPO.getOrderDeptType());
        }

        for (PmsDemandDetailSourceRefPO pmsDemandDetailSourceRefPO : pmsDemandBillResultResp.getDemandBillHandleData().getDemandDetailSourceRefPOList()) {
            if(ObjectUtils.equals(2,pmsDemandDetailSourceRefPO.getType()) && ObjectUtils.equals(1,deptTypeMap.get(pmsDemandDetailSourceRefPO.getOrderDeptCode()))){
                if(detailSourceRefMap.containsKey(pmsDemandDetailSourceRefPO.getOrderDeptCode())){
                    List<PmsDemandDetailSourceRefPO> pmsDemandDetailSourceRefPOS = detailSourceRefMap.get(pmsDemandDetailSourceRefPO.getOrderDeptCode());
                    pmsDemandDetailSourceRefPO.setSrcInsideId((long) (pmsDemandDetailSourceRefPOS.size() + 1));
                    pmsDemandDetailSourceRefPOS.add(pmsDemandDetailSourceRefPO);
                }else{
                    List<PmsDemandDetailSourceRefPO> pmsDemandDetailSourceRefPOS = new ArrayList<>();
                    pmsDemandDetailSourceRefPO.setSrcInsideId(1L);
                    pmsDemandDetailSourceRefPOS.add(pmsDemandDetailSourceRefPO);
                    detailSourceRefMap.put(pmsDemandDetailSourceRefPO.getOrderDeptCode(),pmsDemandDetailSourceRefPOS);

                    BatchGenerateBillNoDTO batchGenerateBillNoDTO = new BatchGenerateBillNoDTO();
                    batchGenerateBillNoDTO.setDeptCode(pmsDemandDetailSourceRefPO.getOrderDeptCode());
                    if(PmsBillDirectionEnum.NORMAL.getCode().equals(pmsDemandBillResultResp.getPmsDemandBillResp().getBillDirection())){
                        batchGenerateBillNoDTO.setMdBillNoBillTypeEnum(MdBillNoBillTypeEnum.ORDER_REQUEST);
                    }else{
                        batchGenerateBillNoDTO.setMdBillNoBillTypeEnum(MdBillNoBillTypeEnum.RETURN_REQUEST);
                    }

                    batchGenerateBillNoDTO.setQty(1);

                    list.add(batchGenerateBillNoDTO);
                }
            }
        }

        List<PmsApplyBillPO> applyBillList = new ArrayList<>();

        List<PmsApplyBillDetailPO> applyBillDetailList = new ArrayList<>();

        billConvertApplyDataDTO.setApplyBillList(applyBillList);
        billConvertApplyDataDTO.setApplyBillDetailList(applyBillDetailList);

        if(detailSourceRefMap.size() > 0 && list.size() > 0){
            List<BatchGenerateBillNoResp> batchBillNoList = supplychainPmsBizRuleEngineService.getBatchBillNo(list);
            for (BatchGenerateBillNoResp batchGenerateBillNoResp : batchBillNoList) {
                if(detailSourceRefMap.containsKey(batchGenerateBillNoResp.getDeptCode())){
                    for (PmsDemandDetailSourceRefPO pmsDemandDetailSourceRefPO : detailSourceRefMap.get(batchGenerateBillNoResp.getDeptCode())) {
                        pmsDemandDetailSourceRefPO.setApplyBillNo(batchGenerateBillNoResp.getBillNoList().get(0));

                    }
                }
            }

            PmsDemandBillPO pmsDemandBillPO = pmsDemandBillResultResp.getDemandBillHandleData().getPmsDemandBillPO();
            Map<Long, PmsDemandDeptGoodsDetailPO> demandDeptGoodsDetailPOMap = pmsDemandBillResultResp.getDemandBillHandleData().getDemandDeptGoodsDetailPOList().parallelStream().collect(Collectors.toMap(PmsDemandDeptGoodsDetailPO::getInsideId, Function.identity()));
            //生成订货申请数据
            for (List<PmsDemandDetailSourceRefPO> value : detailSourceRefMap.values()) {
                if(CollectionUtils.isEmpty(value)){
                    continue;
                }
                PmsDemandDetailSourceRefPO pmsDemandDetailSourceRef = value.get(0);


                PmsApplyBillPO pmsApplyBillPO = new PmsApplyBillPO();
                applyBillList.add(pmsApplyBillPO);
                pmsApplyBillPO.setBillNo(pmsDemandDetailSourceRef.getApplyBillNo());//申请单号
                pmsApplyBillPO.setDeptCode(pmsDemandDetailSourceRef.getOrderDeptCode());//部门编码
                pmsApplyBillPO.setDeptName(pmsDemandDetailSourceRef.getOrderDeptName());//部门名称
                pmsApplyBillPO.setDeptType(1);//部门类型 0-店组群, 1-具体部门
                Integer billDirection = pmsDemandBillPO.getBillDirection();
                if(ObjectUtils.equals(PmsBillDirectionEnum.NORMAL.getCode(),billDirection)){
                    pmsApplyBillPO.setApplyCate(0);// 申请类别 0-补货，1-退货   2-追加
                }else{
                    pmsApplyBillPO.setApplyCate(1);
                }

                pmsApplyBillPO.setStatus(4);//单据状态 0-草稿，1-待审核，2已审核，3-已审批，4-已分配，5-已发货，6-已验收，7-已过期，8-已作废 9 已装箱
                pmsApplyBillPO.setSendMode(0);//送货方式 0-到店，1-到客户
                pmsApplyBillPO.setAuditTime(LocalDateTime.now());//审核时间
                pmsApplyBillPO.setAuditManCode(pmsDemandBillPO.getSubmitCode());//审核人code
                pmsApplyBillPO.setAuditManName(pmsDemandBillPO.getSubmitName());//审核人姓名
                pmsApplyBillPO.setRefundReason(pmsDemandBillResultResp.getDemandBillHandleData().getPmsDemandBillPO().getRefundReason());//退货原因编码
                pmsApplyBillPO.setRefundReasonDesc(pmsDemandBillResultResp.getDemandBillHandleData().getPmsDemandBillPO().getRefundReasonDesc());//退货原因名称
                pmsApplyBillPO.setOnWayFlag(0);//是否在途 0否 1.是
                pmsApplyBillPO.setTotalOrderQty(BigDecimal.ZERO);//订货数量
                pmsApplyBillPO.setTotalPurchMoney(BigDecimal.ZERO);//订货金额(无税) 单位元
                pmsApplyBillPO.setTotalPurchTax(BigDecimal.ZERO);//订货税金 单位元
                pmsApplyBillPO.setTotalPurchTaxMoney(BigDecimal.ZERO);//订货金额（含税） 单位元
                pmsApplyBillPO.setAttributeCode(pmsDemandBillPO.getOrderAttributeCode().split(",")[0]);//订货属性编码
                pmsApplyBillPO.setAttributeName(pmsDemandBillPO.getOrderAttributeName().split(",")[0]);//订货属性名称
                pmsApplyBillPO.setBillSource(PmsBillSourceEnum.DEFAULT.getBillSource());
                pmsApplyBillPO.setManageCategoryClass(pmsDemandBillPO.getManageCategoryClass());//管理分类项编码
                pmsApplyBillPO.setManageCategoryCode(pmsDemandBillPO.getManageCategoryCode());//管理分类编码
                pmsApplyBillPO.setManageCategoryName(pmsDemandBillPO.getManageCategoryName());//管理分类名称

                Long insideId = 0L;
                Boolean purchWayFlag = false;
                Boolean deliveryWayFlag = false;
                for (PmsDemandDetailSourceRefPO pmsDemandDetailSourceRefPO : value) {
                    //获取商品
                    PmsDemandGoodsDetailResp pmsDemandGoodsDetailResp = pmsDemandBillResultResp.getPmsDemandBillResp().getDemandGoodsDetailMap().get(pmsDemandDetailSourceRefPO.getGoodsInsideId());
                    //获取部门商品
                    PmsDemandDeptGoodsDetailPO pmsDemandDeptGoodsDetailPO = demandDeptGoodsDetailPOMap.get(pmsDemandDetailSourceRefPO.getPinsideId());
                    pmsApplyBillPO.setOperateMode(pmsDemandDeptGoodsDetailPO.getDeptOperateMode());//经营模式 1 直营 2 加盟 detail数据
//                    普通配送： 出货方-配送1个
//                    直流：出货方-配送1个，出货方-采购1个
//                    普通配送：配转采，出货方-配送1个，出货方-采购多个
//                    普通采购：出货方-配送0，出货方-采购多个

                    List<PmsDemandPurchShipperResp> pmsDemandPurchShipperResps = new ArrayList<>();
                    if(pmsDemandBillResultResp.getPmsDemandBillResp().getDemandPurchShipperMap().
                            containsKey(pmsDemandDetailSourceRefPO.getPinsideId())){
                        pmsDemandPurchShipperResps = pmsDemandBillResultResp.getPmsDemandBillResp().getDemandPurchShipperMap().
                                get(pmsDemandDetailSourceRefPO.getPinsideId()).parallelStream().filter(t -> ObjectUtils.equals(1,t.getStatus()) && ObjectUtils.equals(0,t.getConvertFlag())).collect(Collectors.toList());
                    }

                    List<PmsDemandDeliveryShipperResp> pmsDemandDeliveryShipperResps = new ArrayList<>();
                    if(pmsDemandBillResultResp.getPmsDemandBillResp().getDemandDeliveryShipperMap()
                            .containsKey(pmsDemandDetailSourceRefPO.getPinsideId())){
                        pmsDemandDeliveryShipperResps = pmsDemandDeliveryShipperResps = pmsDemandBillResultResp.getPmsDemandBillResp().getDemandDeliveryShipperMap()
                                .get(pmsDemandDetailSourceRefPO.getPinsideId()).parallelStream().filter(t -> ObjectUtils.equals(1,t.getStatus())).collect(Collectors.toList());
                    }



                    //获取出货方
                    PmsApplyBillDetailPO pmsApplyBillDetailPO = new PmsApplyBillDetailPO();

                    applyBillDetailList.add(pmsApplyBillDetailPO);

                    pmsApplyBillDetailPO.setPurchTaxMoney(pmsDemandDeptGoodsDetailPO.getResponseMoney());//订货金额（含税） 单位元
                    pmsApplyBillDetailPO.setPurchTax(pmsDemandDeptGoodsDetailPO.getResponseTax());//订货税金 单位元
                    pmsApplyBillDetailPO.setPurchMoney(pmsDemandDeptGoodsDetailPO.getResponseMoney().subtract(pmsDemandDeptGoodsDetailPO.getResponseTax()));//订货金额(无税) 单位元
                    pmsApplyBillDetailPO.setOrderQty(pmsDemandDeptGoodsDetailPO.getResponseQty());//订货数量
                    pmsApplyBillDetailPO.setAdviseQty(null);//建议数量
                    pmsApplyBillDetailPO.setWholeAmount(pmsDemandDeptGoodsDetailPO.getResponseWholeQty());//箱数量
                    pmsApplyBillDetailPO.setOddQty(pmsDemandDeptGoodsDetailPO.getDemandOddQty());//零头数量
                    pmsApplyBillDetailPO.setEncasementQty(pmsDemandDeptGoodsDetailPO.getResponseWholeQty());//装箱数量
                    //普通采购
                    if(ObjectUtils.equals(ShippingWayEnum.PURCHASE.getCode(),pmsDemandDeptGoodsDetailPO.getShippingWay())
                            && !ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),pmsDemandDeptGoodsDetailPO.getDirectSign())){
                        purchWayFlag = true;
                        if(CollectionUtils.isEmpty(pmsDemandPurchShipperResps)){
                            String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B036.getErrorMsg(), new String[]{pmsDemandDetailSourceRefPO.getOrderDeptCode(),pmsDemandDetailSourceRefPO.getSkuCode()}).getMessage();
                            BizExceptions.throwWithCodeAndMsg(PmsErrorCodeEnum.SC_PMS_002_B036.getCode(), message);
                        }
                        PmsDemandPurchShipperResp pmsDemandPurchShipperResp = pmsDemandPurchShipperResps.get(0);
                        
                        pmsApplyBillDetailPO.setPurchTaxPrice(pmsDemandPurchShipperResp.getPurchPrice());//订货单价 单位元

                        pmsApplyBillDetailPO.setSpecialOfferPrice(pmsDemandPurchShipperResp.getContractSpecialPrice());//特供价 单位元
                        pmsApplyBillDetailPO.setShipperCode(pmsDemandPurchShipperResp.getSupplierCode());//出货方编码 1.申请类型=采购，供应商编码 2-申请类型=配送，部门档案（部门类型=配送中心 且 状态<>停用）
                        pmsApplyBillDetailPO.setShipperName(pmsDemandPurchShipperResp.getSupplierName());//出货方名称
                        pmsApplyBillDetailPO.setContractNo(pmsDemandPurchShipperResp.getContractNo());//合同号

                        //pmsApplyBillDetailPO.setPromotionId(pmsDemandPurchShipperResp.getPromoteActivityCode());//促销价id

                        pmsApplyBillDetailPO.setContractPurchPrice(pmsDemandPurchShipperResp.getContractPurchPrice());//合同商品进价
                        pmsApplyBillDetailPO.setContractSpecialPrice(pmsDemandPurchShipperResp.getContractSpecialPrice());//合同商品特供价
                        pmsApplyBillDetailPO.setUnitRate(pmsDemandPurchShipperResp.getPurchUnitRate());
                    }
                    else{
                        deliveryWayFlag = true;
                        PmsDemandDeliveryShipperResp pmsDemandDeliveryShipperResp = pmsDemandDeliveryShipperResps.get(0);
                        pmsApplyBillDetailPO.setPurchBatchNo(pmsDemandDeliveryShipperResp.getPurchBatchNo());//采购批次 取出货方-配送
                        pmsApplyBillDetailPO.setShipperCode(pmsDemandDeliveryShipperResp.getDistDeptCode());//出货方编码 1.申请类型=采购，供应商编码 2-申请类型=配送，部门档案（部门类型=配送中心 且 状态<>停用）
                        pmsApplyBillDetailPO.setShipperName(pmsDemandDeliveryShipperResp.getDistDeptName());//出货方名称
                        pmsApplyBillDetailPO.setPurchTaxPrice(pmsDemandDeliveryShipperResp.getDistPrice());//订货单价 单位元
                        pmsApplyBillDetailPO.setUnitRate(pmsDemandDeliveryShipperResp.getDeliveryUnitRate());
//                        pmsApplyBillDetailPO.setShipperSupplierCode();//配送供应商编码
//                        pmsApplyBillDetailPO.setShipperSupplierName();//配送供应商名称
                        //配送
                        //直流
                        if(ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),pmsDemandDeptGoodsDetailPO.getDirectSign())){
                            PmsDemandPurchShipperResp pmsDemandPurchShipperResp = pmsDemandPurchShipperResps.get(0);
                            pmsApplyBillDetailPO.setDirectSupplierCode(pmsDemandPurchShipperResp.getSupplierCode());//直流供应商编码
                            pmsApplyBillDetailPO.setDirectSupplierName(pmsDemandPurchShipperResp.getSupplierName());//直流供应商名称
                            pmsApplyBillDetailPO.setDirectContractNo(pmsDemandPurchShipperResp.getContractNo());//直流合同号
                            try{
                                pmsApplyBillDetailPO.setDirectPrice(pmsDemandPurchShipperResp.getPurchPrice());//直流采购单价
                            }catch (Exception e){
                                Logs.error("PmsDemandDomainServiceImpl.handlerApplyBillNo.setDirectPrice.error.",e);
                            }

                        }
                    }

                    pmsApplyBillPO.setTotalOrderQty(pmsApplyBillPO.getTotalOrderQty().add(pmsApplyBillDetailPO.getOrderQty()));//订货数量
                    pmsApplyBillPO.setTotalPurchMoney(pmsApplyBillPO.getTotalPurchMoney().add(pmsApplyBillDetailPO.getPurchMoney()));//订货金额(无税) 单位元
                    pmsApplyBillPO.setTotalPurchTax(pmsApplyBillPO.getTotalPurchTax().add(pmsApplyBillDetailPO.getPurchTax()));//订货税金 单位元
                    pmsApplyBillPO.setTotalPurchTaxMoney(pmsApplyBillPO.getTotalPurchTaxMoney().add(pmsApplyBillDetailPO.getPurchTaxMoney()));//订货金额（含税） 单位元


                    pmsApplyBillDetailPO.setInsideId(insideId);//单内序号
                    pmsApplyBillDetailPO.setDemandBillNo(pmsDemandBillPO.getBillNo());//需求单号,响应时有值
                    pmsApplyBillDetailPO.setStatus(2);//需求响应状态1未提单 ,2已提单 9.已作废
                    pmsApplyBillDetailPO.setBillNo(pmsDemandDetailSourceRefPO.getApplyBillNo());//申请单号
                    pmsApplyBillDetailPO.setDeptCode(pmsDemandDetailSourceRefPO.getOrderDeptCode());//部门编码
                    pmsApplyBillDetailPO.setDeptName(pmsDemandDetailSourceRefPO.getOrderDeptName());//部门名称
                    pmsApplyBillDetailPO.setGoodsType(pmsDemandDeptGoodsDetailPO.getGoodsType());//商品类型,1主品,2赠品
                    pmsApplyBillDetailPO.setSkuCode(pmsDemandDeptGoodsDetailPO.getSkuCode());//商品（分类）编码
                    pmsApplyBillDetailPO.setSkuName(pmsDemandDeptGoodsDetailPO.getSkuName());//商品（分类）名称
                    pmsApplyBillDetailPO.setGoodsNo(pmsDemandGoodsDetailResp.getGoodsNo());//商品货号
                    pmsApplyBillDetailPO.setBarcode(pmsDemandGoodsDetailResp.getBarcode());//商品条码
                    pmsApplyBillDetailPO.setBrandCode(pmsDemandGoodsDetailResp.getBrandCode());//商品品牌
                    pmsApplyBillDetailPO.setSkuModel(pmsDemandGoodsDetailResp.getSkuModel());//商品规格
                    pmsApplyBillDetailPO.setSaleMode(pmsDemandGoodsDetailResp.getSaleMode());//销售模式 1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码
                    pmsApplyBillDetailPO.setIsCategoryCode(0);//是否类别码  0-否，1-是
                    pmsApplyBillDetailPO.setIsDecimal(1);//小数控制 0-否 1-是
                    pmsApplyBillDetailPO.setOrderRoute(pmsDemandDeptGoodsDetailPO.getShippingWay());//订货途径 0-采购，1-配送
                    pmsApplyBillDetailPO.setBasicUnit(pmsDemandGoodsDetailResp.getUnit());//单位，汉字文描，个，斤
//                    pmsApplyBillDetailPO.setUnitRate(pmsDemandGoodsDetailResp.getUnitRate());//单位比率，包装率
                    pmsApplyBillDetailPO.setGoodsUnitRate(pmsDemandGoodsDetailResp.getUnitRate());//单位比率，商品包装率
                    pmsApplyBillDetailPO.setInputTaxRate(pmsDemandGoodsDetailResp.getInputTaxRate());//进项税率
                    pmsApplyBillDetailPO.setOutputTaxRate(pmsDemandGoodsDetailResp.getOutputTaxRate());//销项税率
                    pmsApplyBillDetailPO.setSkuWeight(null);//商品毛重
                    pmsApplyBillDetailPO.setUnitSkuWeight(null);//单个商品毛重
                    pmsApplyBillDetailPO.setShippingWay(pmsDemandDeptGoodsDetailPO.getShippingWay());//出货途径 0.采购 1.配送 ShippingWayEnum
                    pmsApplyBillDetailPO.setShippingWayDesc(ShippingWayEnum.getEnumByCode(pmsDemandDeptGoodsDetailPO.getShippingWay()).getDesc());//出货方式描述
                    pmsApplyBillDetailPO.setRemark(pmsDemandBillPO.getRemark());//申请备注
                    pmsApplyBillDetailPO.setCategoryCode(pmsDemandGoodsDetailResp.getCategoryCode());//商品品类编码
                    pmsApplyBillDetailPO.setCategoryCodeFullPath(pmsDemandGoodsDetailResp.getCategoryCodeAll());//商品品类编码全路径
                    pmsApplyBillDetailPO.setCategoryName(pmsDemandGoodsDetailResp.getCategoryName());//商品分类名称
                    pmsApplyBillDetailPO.setGiftQty(BigDecimal.ZERO);//赠品数量
                    pmsApplyBillDetailPO.setSalePrice(pmsDemandDeptGoodsDetailPO.getSalePrice());//销售单价 单位元
                    pmsApplyBillDetailPO.setSaleMoney(pmsDemandDeptGoodsDetailPO.getSalePrice().multiply(pmsDemandDeptGoodsDetailPO.getResponseQty()));//销售金额 单位元
                    pmsApplyBillDetailPO.setSendStockQty(null);//配送库存
                    pmsApplyBillDetailPO.setStockQty(pmsDemandDeptGoodsDetailPO.getOrderStockQty());//商品库存
                    pmsApplyBillDetailPO.setOnWayStockQty(null);//在途库存
                    pmsApplyBillDetailPO.setCirculationModeCode(pmsDemandDeptGoodsDetailPO.getCirculationModeCode());//流转途径编码
                    pmsApplyBillDetailPO.setCirculationModeName("");//流转途径名称
                    pmsApplyBillDetailPO.setWorkStatusCode(pmsDemandDeptGoodsDetailPO.getWorkStateCode());//经营状态编码
                    pmsApplyBillDetailPO.setWorkStatusName("");//经营状态名称
                    pmsApplyBillDetailPO.setLast1daysSaleNum(null);//1天销量
                    pmsApplyBillDetailPO.setLast7daysSaleNum(null);//7天销量
                    pmsApplyBillDetailPO.setLast30daysSaleNum(null);//30天销量
                    pmsApplyBillDetailPO.setUomAttr(pmsDemandGoodsDetailResp.getUomAttr()+"");//计量属性（0：普通 1：计量 2：称重）

                    pmsApplyBillDetailPO.setBatchCategoryCode("");//采购批次商品品类编码
                    pmsApplyBillDetailPO.setWholeUnit("");//整箱单位
                    pmsApplyBillDetailPO.setManageModel("");//合同经营方式 0-经销 1-代销 2-联营 3-租赁
                    pmsApplyBillDetailPO.setDirectSign(pmsDemandDeptGoodsDetailPO.getDirectSign());//直流标志 0-否，1-是

                    pmsApplyBillDetailPO.setMinOrderQty(null);//起订数量(最小订货数量)
                    pmsApplyBillDetailPO.setSecondDcFlag(0);//第二配送中心标识 0 否 1 是
                    pmsApplyBillDetailPO.setManageCategoryClass(pmsDemandDetailSourceRefPO.getSrcManageCategoryClass());//管理分类项编码
                    pmsApplyBillDetailPO.setManageCategoryCode(pmsDemandDetailSourceRefPO.getSrcManageCategoryCode());//管理分类编码
                    pmsApplyBillDetailPO.setManageCategoryName(pmsDemandDetailSourceRefPO.getSrcManageCategoryName());//管理分类名称
                    pmsApplyBillDetailPO.setImgUrl("");//图片地址
                    pmsApplyBillDetailPO.setAcceptRefundPrice(null);//验收退补价格
                    pmsApplyBillDetailPO.setContractAttributeCode("");//合同属性编码
                    pmsApplyBillDetailPO.setPriceType(null);//取价类型1, 验收退补价格 2, 合同商品进价 3, 合同商品特供价

                    insideId++;
                }
                
                if(deliveryWayFlag && purchWayFlag){
                    pmsApplyBillPO.setApplyType(2);//申请类型 0-采购，1-配送，2-采购+配送  根据出货判断
                }
                else if(purchWayFlag){
                    pmsApplyBillPO.setApplyType(0);
                    pmsApplyBillPO.setValidityDate(pmsDemandBillPO.getPurchValidityDate());//有效日期 根据出货判断
                    pmsApplyBillPO.setAppendValidity(pmsDemandBillPO.getPurchValidityDate().atTime( 23,59,59));//追加截止日期 根据出货判断
                    pmsApplyBillPO.setDeliverDate(pmsDemandBillPO.getPurchDeliverDate());//送货日期 根据出货判断
                }
                else{
                    pmsApplyBillPO.setApplyType(1);
                    pmsApplyBillPO.setValidityDate(pmsDemandBillPO.getDeliverValidityDate());//有效日期 根据出货判断
                    pmsApplyBillPO.setAppendValidity(pmsDemandBillPO.getDeliverValidityDate().atTime( 23,59,59));//追加截止日期 根据出货判断
                    pmsApplyBillPO.setDeliverDate(pmsDemandBillPO.getDeliverDeliverDate());//送货日期 根据出货判断
                }

            }

        }


        return billConvertApplyDataDTO;
    }

    /**
     * 处理加工配转采的PO数据
     * @param pmsDemandBillResultResp
     * @return
     */
    private BillConvertDataDTO handlerBillToPruchOrDelivery(PmsDemandBillResultResp pmsDemandBillResultResp){
        BillConvertDataDTO billConvertData = new BillConvertDataDTO();
        //普通配送： 出货方-配送1个
        //普通配送-直流：出货方-配送1个，出货方-采购1个
        //普通配送-配转采：出货方-配送1个，出货方-采购多个
        //普通采购：出货方-配送0，出货方-采购多个
        DemandBillHandleDataDTO demandBillHandleData = pmsDemandBillResultResp.getDemandBillHandleData();

//        针对紧急订货属性时，需求批次分组时按空处理

        Map<String, DeliveryOrderHandleDTO> deliveryOrderHandleMap = new HashMap<>();
        Map<String, PurchOrderHandleDTO> purchOrderHandleMap = new HashMap<>();

        for (PmsDemandDeptGoodsDetailPO pmsDemandDeptGoodsDetailPO : demandBillHandleData.getDemandDeptGoodsDetailPOList()) {
            if(ObjectUtils.notEqual(1,pmsDemandDeptGoodsDetailPO.getStatus())){
                Logs.info("PmsDemandDomainServiceImpl.handlerBillToPruchOrDelivery.部门商品为响应失败的,不处理:" + JSON.toJSONString(pmsDemandDeptGoodsDetailPO));
                continue;
            }

            if(ObjectUtils.equals(1,pmsDemandDeptGoodsDetailPO.getConvertFlag())){
                Logs.info("PmsDemandDomainServiceImpl.handlerBillToPruchOrDelivery.部门商品为配转采的,不在此处理:" + JSON.toJSONString(pmsDemandDeptGoodsDetailPO));
                continue;
            }

            //出货途径-采购
            if(ShippingWayEnum.PURCHASE.getCode().equals(pmsDemandDeptGoodsDetailPO.getShippingWay())){
                generatePurchOrder(purchOrderHandleMap,pmsDemandDeptGoodsDetailPO,pmsDemandBillResultResp);
            }
            //出货途径-配送(不含配转采)
            else{
                generateDeliveryOrder(deliveryOrderHandleMap,purchOrderHandleMap,pmsDemandDeptGoodsDetailPO,pmsDemandBillResultResp);
            }
        }


        //配转采
        handlerPurchToDeliveryBill(pmsDemandBillResultResp,deliveryOrderHandleMap,purchOrderHandleMap,billConvertData);

        //商品品项数
        for (DeliveryOrderHandleDTO value : deliveryOrderHandleMap.values()) {
            List<String> collect = value.getDeliveryBillDetailList().stream().map(WdDeliveryBillDetailPO::getSkuCode).distinct().collect(Collectors.toList());
            value.getDeliveryBill().setTotalSkuCount(collect.size());
        }

        for (PurchOrderHandleDTO value : purchOrderHandleMap.values()) {
            List<String> collect = value.getPurchBillDetailList().stream().map(PmsPurchaseBillDetailPO::getSkuCode).distinct().collect(Collectors.toList());
            value.getPurchBill().setTotalSkuCount(collect.size());
        }

        Map<String,PmsDist2purchSupplierRecordPO> dist2purchSupplierRecordPOMap = new HashMap<>();
        //采购订单数据
        for (PurchOrderHandleDTO value : purchOrderHandleMap.values()) {
            billConvertData.getPurchBillList().add(value.getPurchBill());
            billConvertData.getPurchBillDetailList().addAll(value.getPurchBillDetailList());
            billConvertData.getPurchBillDetailRefList().addAll(value.getPurchBillDetailRefList());
            billConvertData.getPruchDeliveryRefList().addAll(value.getPruchDeliveryRefList());

            dist2purchSupplierRecordPOMap.putAll(value.getDist2purchSupplierRecordPOMap());
        }

        if(dist2purchSupplierRecordPOMap.size() > 0){
            billConvertData.getDist2purchSupplierRecordList().addAll(new ArrayList<>(dist2purchSupplierRecordPOMap.values()));
        }

        //配送订单数据
        for (DeliveryOrderHandleDTO value : deliveryOrderHandleMap.values()) {
            billConvertData.getDeliveryBillList().add(value.getDeliveryBill());
            billConvertData.getDeliveryBillDetailList().addAll(value.getDeliveryBillDetailList());
            billConvertData.getPruchDeliveryRefList().addAll(value.getPruchDeliveryRefList());
        }

        return billConvertData;
    }

    /**
     * 处理配转采单据
     * @param pmsDemandBillResultResp
     * @param deliveryOrderHandleMap
     * @param purchOrderHandleMap
     * @param billConvertData
     */
    private void handlerPurchToDeliveryBill(PmsDemandBillResultResp pmsDemandBillResultResp,Map<String, DeliveryOrderHandleDTO> deliveryOrderHandleMap,
                                            Map<String, PurchOrderHandleDTO> purchOrderHandleMap,BillConvertDataDTO billConvertData) {
        DemandBillHandleDataDTO demandBillHandleData = pmsDemandBillResultResp.getDemandBillHandleData();
        if(CollectionUtils.isEmpty(demandBillHandleData.getDeliveryToPurchPOList())){
            return;
        }

        PmsDemandBillResp pmsDemandBillResp = pmsDemandBillResultResp.getPmsDemandBillResp();

        //普通配送-配转采：出货方-配送1个，出货方-采购多个

        Map<Long, List<PmsDemandDeliveryToPurchRefPO>> deliveryToPurchRefMap = demandBillHandleData.getDeliveryToPurchRefPOList().parallelStream().
                collect(Collectors.groupingBy(PmsDemandDeliveryToPurchRefPO::getDeliveryToPurchInsideId));

        //配转采的供应商出货方
        Map<Long, List<PmsDemandPurchShipperPO>> purchShipperMap = demandBillHandleData.getDemandPurchShipperPOList().parallelStream()
                .filter(t -> ObjectUtils.equals(1, t.getConvertFlag()) && ObjectUtils.equals(1, t.getStatus()))
                .collect(Collectors.groupingBy(PmsDemandPurchShipperPO::getDeliveryToPurchInsideId));

        //配转采的配送出货方
        Map<Long, PmsDemandDeliveryShipperPO> deliveryShipperMap = demandBillHandleData.getDemandDeliveryShipperPOList().parallelStream().filter(t -> ObjectUtils.equals(1, t.getStatus()))
                .collect(Collectors.toMap(PmsDemandDeliveryShipperPO::getInsideId,Function.identity()));

        Map<Long, PmsDemandDeptGoodsDetailPO> deptGoodsDetailPOMap = demandBillHandleData.getDemandDeptGoodsDetailPOList().parallelStream().collect(Collectors.toMap(PmsDemandDeptGoodsDetailPO::getInsideId, Function.identity()));

        for (PmsDemandDeliveryToPurchPO pmsDemandDeliveryToPurchPO : demandBillHandleData.getDeliveryToPurchPOList()) {
            if(!(ObjectUtils.equals(1,pmsDemandDeliveryToPurchPO.getType()) && ObjectUtils.equals(1,pmsDemandDeliveryToPurchPO.getStatus()))){
                continue;
            }

            List<WdDeliveryBillDetailPO> deliveryBillDetailList = new ArrayList<>();
            List<PmsDemandPruchDeliveryRefPO> pruchDeliveryRefList = new ArrayList<>();

            //            #针对门店要货-出货途径是配送且不是直流的行
//                需要按订货单号+订货部门+配送部门+需求批次号+送货方式 生成配送订单
            for (PmsDemandDeliveryToPurchRefPO pmsDemandDeliveryToPurchRefPO : deliveryToPurchRefMap.get(pmsDemandDeliveryToPurchPO.getInsideId())) {
                PmsDemandDeliveryShipperPO pmsDemandDeliveryShipperPO = deliveryShipperMap.get(pmsDemandDeliveryToPurchRefPO.getDeliveryShipperInsideId());
                PmsDemandDeliveryShipperResp pmsDemandDeliveryShipperResp = CglibCopier.copy(pmsDemandDeliveryShipperPO,PmsDemandDeliveryShipperResp.class);

                int sourceIdx = 0;
                //订货申请响应未生成采购数量
                BigDecimal marginApplyQty = new BigDecimal("0");

                //未生成配送的配送数量
                BigDecimal marginDeliveryShipperQty = pmsDemandDeliveryShipperPO.getResponseQty();

                PmsDemandDeptGoodsDetailPO pmsDemandDeptGoodsDetailPO = deptGoodsDetailPOMap.get(pmsDemandDeliveryShipperPO.getPinsideId());

                List<PmsDemandDetailSourceRefPO> pmsDemandDetailSourceRefPOs= demandBillHandleData.getDetailSourceRefPOMap().get(pmsDemandDeptGoodsDetailPO.getInsideId());
                String deliveryKey = "";
                for(int i = sourceIdx; i < pmsDemandDetailSourceRefPOs.size(); i++) {
                    PmsDemandDetailSourceRefPO pmsDemandDetailSourceRefPO = pmsDemandDetailSourceRefPOs.get(i);
                    // 订货单号+订货部门+配送部门+需求批次号+送货方式
                    deliveryKey = pmsDemandDetailSourceRefPO.getApplyBillNo() + getSplitKey(pmsDemandDeptGoodsDetailPO.getOrderDeptCode()) +
                            getSplitKey(pmsDemandDeliveryShipperPO.getDistDeptCode()) + getSplitKey(pmsDemandDeliveryShipperPO.getPurchBatchNo()) +
                            getSplitKey(pmsDemandDeptGoodsDetailPO.getSendMode() + "");
                    List<String> skuCodeList = new ArrayList<>();
                    skuCodeList.add(pmsDemandDeptGoodsDetailPO.getSkuCode());

                    if(0 == pmsDemandDetailSourceRefPO.getResponseQty().compareTo(new BigDecimal("0"))){
                        //响应数量为0直接continue
                        continue;
                    }

                    if(ObjectUtils.notEqual(1,pmsDemandDetailSourceRefPO.getStatus())){
                        continue;
                    }

                    //未使用数量
                    BigDecimal notUseQty = pmsDemandDetailSourceRefPO.getResponseQty().add(marginApplyQty);

                    Boolean isBreak = false;
                    BigDecimal deliveryOrderQty;
                    //未生成配送订单数量 = 未使用数量
                    if(marginDeliveryShipperQty.compareTo(notUseQty) == 0){
                        sourceIdx = i + 1;
                        marginApplyQty = new BigDecimal("0");
                        deliveryOrderQty = notUseQty;

                        isBreak = true;
                    }
                    //未生成配送订单数量 < 未使用数量
                    else if(marginDeliveryShipperQty.compareTo(notUseQty) < 0){

                        deliveryOrderQty = marginDeliveryShipperQty;
                        //订货申请未生成订货采购响应数量 - 未生成采购的供应商采购数量
                        marginApplyQty = notUseQty.subtract(marginDeliveryShipperQty);

                        isBreak = true;
                    }
                    //未生成配送订单数量 > 未使用数量
                    else{
                        sourceIdx = i + 1;
                        marginApplyQty = new BigDecimal("0");
                        marginDeliveryShipperQty = marginDeliveryShipperQty.subtract(notUseQty);
                        deliveryOrderQty = notUseQty;
                    }

                    buildDeliveryOrder(deliveryKey,deliveryOrderHandleMap,pmsDemandDeptGoodsDetailPO,pmsDemandDeliveryShipperResp,pmsDemandBillResp,
                            deliveryOrderQty, pmsDemandDetailSourceRefPO,"","");

                    DeliveryOrderHandleDTO deliveryOrderHandleDTO = deliveryOrderHandleMap.get(deliveryKey);


                    deliveryBillDetailList.add(deliveryOrderHandleDTO.getDeliveryBillDetailList().get(deliveryOrderHandleDTO.getDeliveryBillDetailList().size() - 1));
                    pruchDeliveryRefList.add(deliveryOrderHandleDTO.getPruchDeliveryRefList().get(deliveryOrderHandleDTO.getPruchDeliveryRefList().size() - 1));

                    if(isBreak){
                        break;
                    }
                }

            }
//            #针对门店配送要货 转采购的行
//                需要按照配送部门+供应商+合同+停靠点+送货方式 分组生成采购订单

            List<PmsPurchaseBillDetailPO> purchBillDetailList = new ArrayList<>();

            Map<String, PmsDemandGoodsDetailPO> demandGoodsDetailMap = pmsDemandBillResultResp.getDemandBillHandleData().getDemandGoodsDetailPOList().parallelStream().collect(Collectors.toMap(PmsDemandGoodsDetailPO::getSkuCode, Function.identity()));
            //配转采 分组方式：配送部门 + 商品类型+商品编码+停靠点+需求批次+送货方式
            for (PmsDemandPurchShipperPO pmsDemandPurchShipperPO : purchShipperMap.get(pmsDemandDeliveryToPurchPO.getInsideId())) {
                //配转采记录里面存了配送部门
                String purchKey = pmsDemandDeliveryToPurchPO.getDistDeptCode() + getSplitKey(pmsDemandPurchShipperPO.getSupplierCode())
                        + getSplitKey(pmsDemandPurchShipperPO.getContractNo()) + getSplitKey(pmsDemandPurchShipperPO.getDockCode())
                        + getSplitKey(pmsDemandDeliveryToPurchPO.getSendMode() + "");

                PmsDemandGoodsDetailPO pmsDemandGoodsDetailPO = demandGoodsDetailMap.get(pmsDemandPurchShipperPO.getSkuCode());
                //配转采
                buildPurchOrder4DeliveryToPurch(purchKey,purchOrderHandleMap,pmsDemandDeliveryToPurchPO,pmsDemandPurchShipperPO
                        ,pmsDemandBillResp,pmsDemandGoodsDetailPO);

                PurchOrderHandleDTO purchOrderHandleDTO = purchOrderHandleMap.get(purchKey);
                purchBillDetailList.add(purchOrderHandleDTO.getPurchBillDetailList().get(purchOrderHandleDTO.getPurchBillDetailList().size() - 1));
            }

            List<PmsPruchDetailRefPO> purchBillDetailRefPruchList = new ArrayList<>();

            List<PmsDemandPruchDeliveryRefPO> pruchDeliveryRefPurchList = new ArrayList<>();

            //采购订单记录配转采关系
            int purchDetaiIdx = 0;
            int pureIdx = 0;
            //子循环里面每次循环剩余的差额
            BigDecimal marginQty = new BigDecimal("0");
            for(int i = 0; i < deliveryBillDetailList.size();i++){
                WdDeliveryBillDetailPO wdDeliveryBillDetailPO = deliveryBillDetailList.get(i);
                PmsDemandPruchDeliveryRefPO pmsDemandPruchDeliveryRefPO = pruchDeliveryRefList.get(i);

                //配送数量
                BigDecimal deliveryQty = wdDeliveryBillDetailPO.getDeliveryQty();

                Boolean isBreak = false;

                BigDecimal qty;
                for(int j = purchDetaiIdx;j < purchBillDetailList.size();j++){
                    pureIdx++;
                    PmsPurchaseBillDetailPO pmsPurchaseBillDetailPO = purchBillDetailList.get(j);
                    //采购数量
                    BigDecimal purchQty = pmsPurchaseBillDetailPO.getPurchQty();

                    ////未使用数量
                    BigDecimal notUseQty = purchQty.add(marginQty);

                    //配送数量 = 采购数量
                    if(deliveryQty.compareTo(notUseQty) == 0){
                        purchDetaiIdx = j + 1;
                        marginQty = new BigDecimal("0");
                        qty = notUseQty;

                        isBreak = true;
                    }
                    //配送数量 < 采购数量
                    else if(deliveryQty.compareTo(notUseQty) < 0){
                        qty = deliveryQty;

                        marginQty = notUseQty.subtract(deliveryQty);

                        isBreak = true;
                    }
                    //配送数量 > 采购数量
                    else{
                        purchDetaiIdx = j + 1;
                        marginQty = new BigDecimal("0");
                        qty = deliveryQty.subtract(notUseQty);
                    }

                    //写入关系
                    //采购订单明细关联
                    PmsPruchDetailRefPO pmsPruchDetailRefPO = new PmsPruchDetailRefPO();
                    pmsPruchDetailRefPO.setCreateTime(LocalDateTime.now());
                    pmsPruchDetailRefPO.setUpdateTime(LocalDateTime.now());
                    pmsPruchDetailRefPO.setBillNo(pmsPurchaseBillDetailPO.getBillNo());
                    pmsPruchDetailRefPO.setInsideId(pmsPurchaseBillDetailPO.getInsideId());
                    pmsPruchDetailRefPO.setSkuCode(pmsPurchaseBillDetailPO.getSkuCode());
                    pmsPruchDetailRefPO.setSkuName(pmsPurchaseBillDetailPO.getSkuName());
                    pmsPruchDetailRefPO.setPurchQty(qty);
                    pmsPruchDetailRefPO.setBillSource(2);
                    pmsPruchDetailRefPO.setSrcBillNo(wdDeliveryBillDetailPO.getBillNo());
                    pmsPruchDetailRefPO.setSrcInsideId(wdDeliveryBillDetailPO.getInsideId());
                    purchBillDetailRefPruchList.add(pmsPruchDetailRefPO);

                    //需求单出货方与采购配送订单关联关系
                    PmsDemandPruchDeliveryRefPO pmsDemandPruchDeliveryRef = new PmsDemandPruchDeliveryRefPO();
                    pmsDemandPruchDeliveryRef.setBillNo(pmsDemandPruchDeliveryRefPO.getBillNo());
                    pmsDemandPruchDeliveryRef.setRefBillNo(pmsPurchaseBillDetailPO.getBillNo());
                    pmsDemandPruchDeliveryRef.setType(2);//配转采的采购
                    pmsDemandPruchDeliveryRef.setPurchShipperInsideId(pmsPurchaseBillDetailPO.getSrcInsideId());
                    pmsDemandPruchDeliveryRef.setDeptGoodsInsideId(0L);
                    pmsDemandPruchDeliveryRef.setQty(qty);
                    pmsDemandPruchDeliveryRef.setRefInsideId(pmsPurchaseBillDetailPO.getInsideId());

                    pmsDemandPruchDeliveryRef.setApplyBillNo(pmsDemandPruchDeliveryRefPO.getApplyBillNo());
                    pmsDemandPruchDeliveryRef.setApplyInsideId(pmsDemandPruchDeliveryRefPO.getApplyInsideId());
                    pmsDemandPruchDeliveryRef.setSkuCode(pmsPurchaseBillDetailPO.getSkuCode());
                    pruchDeliveryRefPurchList.add(pmsDemandPruchDeliveryRef);

                    //如果本循环数据未完成，且外层循环结束了
                    if(marginQty.compareTo(BigDecimal.ZERO) > 0 && i == deliveryBillDetailList.size()){
                        //
                        pmsPruchDetailRefPO = new PmsPruchDetailRefPO();
                        pmsPruchDetailRefPO.setCreateTime(LocalDateTime.now());
                        pmsPruchDetailRefPO.setUpdateTime(LocalDateTime.now());
                        pmsPruchDetailRefPO.setBillNo(pmsPurchaseBillDetailPO.getBillNo());
                        pmsPruchDetailRefPO.setInsideId(pmsPurchaseBillDetailPO.getInsideId());
                        pmsPruchDetailRefPO.setSkuCode(pmsPurchaseBillDetailPO.getSkuCode());
                        pmsPruchDetailRefPO.setSkuName(pmsPurchaseBillDetailPO.getSkuName());
                        pmsPruchDetailRefPO.setPurchQty(marginQty);
                        pmsPruchDetailRefPO.setBillSource(2);
                        pmsPruchDetailRefPO.setSrcBillNo(wdDeliveryBillDetailPO.getBillNo());
                        pmsPruchDetailRefPO.setSrcInsideId(wdDeliveryBillDetailPO.getInsideId());
                        purchBillDetailRefPruchList.add(pmsPruchDetailRefPO);

                        pmsDemandPruchDeliveryRef = new PmsDemandPruchDeliveryRefPO();
                        pmsDemandPruchDeliveryRef.setBillNo(pmsDemandPruchDeliveryRefPO.getBillNo());
                        pmsDemandPruchDeliveryRef.setRefBillNo(pmsPurchaseBillDetailPO.getBillNo());
                        pmsDemandPruchDeliveryRef.setType(3);//3配转采的采购超出的部分
                        pmsDemandPruchDeliveryRef.setPurchShipperInsideId(pmsPurchaseBillDetailPO.getSrcInsideId());
                        pmsDemandPruchDeliveryRef.setDeptGoodsInsideId(0L);
                        pmsDemandPruchDeliveryRef.setQty(qty);
                        pmsDemandPruchDeliveryRef.setRefInsideId(pmsPurchaseBillDetailPO.getInsideId());

                        pmsDemandPruchDeliveryRef.setApplyBillNo(pmsDemandPruchDeliveryRefPO.getApplyBillNo());
                        pmsDemandPruchDeliveryRef.setApplyInsideId(pmsDemandPruchDeliveryRefPO.getApplyInsideId());
                        pmsDemandPruchDeliveryRef.setSkuCode(pmsPurchaseBillDetailPO.getSkuCode());
                        pruchDeliveryRefPurchList.add(pmsDemandPruchDeliveryRef);
                    }
                    if(isBreak){
                        continue;
                    }
                }
            }

            //采购行数大于配送行数的场景  都关联最后一个配送行
            if(pureIdx < purchBillDetailList.size()){
                WdDeliveryBillDetailPO wdDeliveryBillDetailPO = deliveryBillDetailList.get(deliveryBillDetailList.size() - 1);
                PmsDemandPruchDeliveryRefPO pmsDemandPruchDeliveryRefPO = pruchDeliveryRefList.get(pruchDeliveryRefList.size() - 1);
                for(int j = pureIdx;j < purchBillDetailList.size();j++){
                    PmsPurchaseBillDetailPO pmsPurchaseBillDetailPO = purchBillDetailList.get(j);

                    //写入关系
                    //采购订单明细关联
                    PmsPruchDetailRefPO pmsPruchDetailRefPO = new PmsPruchDetailRefPO();
                    pmsPruchDetailRefPO.setCreateTime(LocalDateTime.now());
                    pmsPruchDetailRefPO.setUpdateTime(LocalDateTime.now());
                    pmsPruchDetailRefPO.setBillNo(pmsPurchaseBillDetailPO.getBillNo());
                    pmsPruchDetailRefPO.setInsideId(pmsPurchaseBillDetailPO.getInsideId());
                    pmsPruchDetailRefPO.setSkuCode(pmsPurchaseBillDetailPO.getSkuCode());
                    pmsPruchDetailRefPO.setSkuName(pmsPurchaseBillDetailPO.getSkuName());
                    pmsPruchDetailRefPO.setPurchQty(pmsPurchaseBillDetailPO.getPurchQty());
                    pmsPruchDetailRefPO.setBillSource(2);
                    pmsPruchDetailRefPO.setSrcBillNo(wdDeliveryBillDetailPO.getBillNo());
                    pmsPruchDetailRefPO.setSrcInsideId(wdDeliveryBillDetailPO.getInsideId());
                    purchBillDetailRefPruchList.add(pmsPruchDetailRefPO);

                    //需求单出货方与采购配送订单关联关系
                    PmsDemandPruchDeliveryRefPO pmsDemandPruchDeliveryRef = new PmsDemandPruchDeliveryRefPO();
                    pmsDemandPruchDeliveryRef.setBillNo(pmsDemandPruchDeliveryRefPO.getBillNo());
                    pmsDemandPruchDeliveryRef.setRefBillNo(pmsPurchaseBillDetailPO.getBillNo());
                    pmsDemandPruchDeliveryRef.setType(3);//3配转采的采购超出的部分
                    pmsDemandPruchDeliveryRef.setPurchShipperInsideId(pmsPurchaseBillDetailPO.getSrcInsideId());
                    pmsDemandPruchDeliveryRef.setDeptGoodsInsideId(0L);
                    pmsDemandPruchDeliveryRef.setQty(pmsPurchaseBillDetailPO.getPurchQty());
                    pmsDemandPruchDeliveryRef.setRefInsideId(pmsPurchaseBillDetailPO.getInsideId());

                    pmsDemandPruchDeliveryRef.setApplyBillNo(pmsDemandPruchDeliveryRefPO.getApplyBillNo());
                    pmsDemandPruchDeliveryRef.setApplyInsideId(pmsDemandPruchDeliveryRefPO.getApplyInsideId());
                    pmsDemandPruchDeliveryRef.setSkuCode(wdDeliveryBillDetailPO.getSkuCode());
                    pruchDeliveryRefPurchList.add(pmsDemandPruchDeliveryRef);
                }
            }
            billConvertData.getPurchBillDetailRefList().addAll(purchBillDetailRefPruchList);
            billConvertData.getPruchDeliveryRefList().addAll(pruchDeliveryRefPurchList);
        }
    }

    /**
     * 组装配转采数据
     * @param purchKey
     * @param purchOrderHandleMap
     * @param pmsDemandDeliveryToPurchPO
     * @param pmsDemandPurchShipperPO
     * @param pmsDemandBillResp
     * @param pmsDemandGoodsDetailPO
     */
    private void buildPurchOrder4DeliveryToPurch(String purchKey, Map<String, PurchOrderHandleDTO> purchOrderHandleMap,PmsDemandDeliveryToPurchPO pmsDemandDeliveryToPurchPO
        ,PmsDemandPurchShipperPO pmsDemandPurchShipperPO,PmsDemandBillResp pmsDemandBillResp,PmsDemandGoodsDetailPO pmsDemandGoodsDetailPO){
        PurchOrderHandleDTO purchOrderHandleDTO = new PurchOrderHandleDTO();
        if(purchOrderHandleMap.containsKey(purchKey)){
            purchOrderHandleDTO = purchOrderHandleMap.get(purchKey);
        }
        else{
            purchOrderHandleMap.put(purchKey,purchOrderHandleDTO);
        }

        PmsPurchaseOrderPO purchBill = purchOrderHandleDTO.getPurchBill();
        if(null == purchBill){
            purchBill = new PmsPurchaseOrderPO();
            //获取采购单号
            String billNo = supplychainPmsBizRuleEngineService.getBillNo(MdBillNoBillTypeEnum.PMS_PURCHASE_ORDER,pmsDemandDeliveryToPurchPO.getDistDeptCode());
            purchBill.setBillNo(billNo);
            purchBill.setDeptCode(pmsDemandDeliveryToPurchPO.getDistDeptCode());
            purchBill.setDeptName(pmsDemandDeliveryToPurchPO.getDistDeptCode());
            purchBill.setDeptOperateMode(1);
            purchBill.setSupplierCode(pmsDemandPurchShipperPO.getSupplierCode());
            purchBill.setSupplierName(pmsDemandPurchShipperPO.getSupplierName());
            purchBill.setBillDirection(pmsDemandBillResp.getBillDirection());
            purchBill.setBillType(2);//配转采默认为配送采购
            String[] splitAttributeCode = pmsDemandBillResp.getOrderAttributeCode().split(",");
            String attributeCode = "";
            if(splitAttributeCode.length > 0){
                attributeCode = splitAttributeCode[0];
            }

            String[] splitAttributeName = pmsDemandBillResp.getOrderAttributeName().split(",");
            String attributeName = "";
            if(splitAttributeName.length > 0){
                attributeName = splitAttributeName[0];
            }

            purchBill.setOrderAttributeCode(attributeCode);
            purchBill.setOrderAttributeName(attributeName);
            purchBill.setStatus(99);
            purchBill.setSendMode(0);//配送采购到店


//            String validDaysStr = iSupplychainBizSysParamRuleService.getValue(PMSSystemParamEnum.DEMAND_PURCH_ORDER_VALID_DAYS);
//            String deliveryDaysStr = iSupplychainBizSysParamRuleService.getValue(PMSSystemParamEnum.DEMAND_PURCH_ORDER_DELIVER_DAYS);
//
//            int validDays = NumberUtil.getInteger(validDaysStr,7);
//            int deliveryDays = NumberUtil.getInteger(deliveryDaysStr,7);
//
//            purchBill.setDeliverDate(LocalDate.now().plusDays(deliveryDays));
//            purchBill.setValidityDate(LocalDate.now().plusDays(validDays));

            purchBill.setDeliverDate(pmsDemandBillResp.getPurchDeliverDate());
            purchBill.setValidityDate(pmsDemandBillResp.getPurchValidityDate());

            purchBill.setDockCode(pmsDemandPurchShipperPO.getDockCode());
            purchBill.setDockName(pmsDemandPurchShipperPO.getDockName());
//            purchBill.setContactMan("");
//            purchBill.setContactTel("");
//            purchBill.setContactAddr("");
            purchBill.setPurchRemark(pmsDemandBillResp.getRemark());
            purchBill.setRefundReason(pmsDemandBillResp.getRefundReason());
            purchBill.setRefundReasonDesc(pmsDemandBillResp.getRefundReasonDesc());

            purchBill.setContractNo(pmsDemandPurchShipperPO.getContractNo());
            purchBill.setBillSource(1);
            purchBill.setSrcBillNo(pmsDemandBillResp.getBillNo());
            purchBill.setSrcRemark(pmsDemandBillResp.getRemark());

            purchBill.setTotalSkuCount(0);//商品品项数
            purchBill.setTotalQty(BigDecimal.ZERO);//采购总数量
            purchBill.setTotalTaxMoney(BigDecimal.ZERO);//采购总金额
            purchBill.setTotalTax(BigDecimal.ZERO);//采购总税金

            purchBill.setPurchBatchNo(pmsDemandDeliveryToPurchPO.getPurchBatchNo());//需求批次
            purchBill.setDirectSign(0);//配转采非直流
            purchBill.setTransferPurchSign(1);

            if(ObjectUtils.equals(PmsBillDirectionEnum.REVERSE.getCode(),purchBill.getBillDirection())){
                SupplierByCodeResp supplier = baseStoreUtil.getSupplierCache(purchBill.getSupplierCode());
                if(null != supplier){
                    purchBill.setContactMan(supplier.getLinkMan());
                    purchBill.setContactTel(supplier.getPhone());
                    purchBill.setContactAddr(supplier.getAddress());
                }
            }
            else{
                StoreDetailResp storeDetailResp = baseStoreUtil.getSingleDeptInfo(purchBill.getDeptCode());
                if(null != storeDetailResp){
                    purchBill.setContactMan(storeDetailResp.getManagerUserName());
                    purchBill.setContactTel(storeDetailResp.getServiceHotline());
                    purchBill.setContactAddr(storeDetailResp.getWholeAddress());
                }
            }

            purchOrderHandleDTO.setPurchBill(purchBill);

        }
        //采购订单明细表
        List<PmsPurchaseBillDetailPO> purchBillDetailList = purchOrderHandleDTO.getPurchBillDetailList();

        PmsPurchaseBillDetailPO pmsPurchaseBillDetailPO = new PmsPurchaseBillDetailPO();
        Long insideId = Long.valueOf(purchBillDetailList.size()) + 1;
        purchBillDetailList.add(pmsPurchaseBillDetailPO);


        pmsPurchaseBillDetailPO.setBillNo(purchBill.getBillNo());
        pmsPurchaseBillDetailPO.setDeptCode(purchBill.getDeptCode());
        pmsPurchaseBillDetailPO.setDeptName(purchBill.getDeptName());
        pmsPurchaseBillDetailPO.setSupplierCode(purchBill.getSupplierCode());
        pmsPurchaseBillDetailPO.setSupplierName(purchBill.getSupplierName());
        pmsPurchaseBillDetailPO.setBillType(purchBill.getBillType());
        pmsPurchaseBillDetailPO.setInsideId(insideId);
        pmsPurchaseBillDetailPO.setSkuType(pmsDemandGoodsDetailPO.getGoodsType());
        pmsPurchaseBillDetailPO.setSkuCode(pmsDemandGoodsDetailPO.getSkuCode());
        pmsPurchaseBillDetailPO.setSkuName(pmsDemandGoodsDetailPO.getSkuName());
        pmsPurchaseBillDetailPO.setBarcode(pmsDemandGoodsDetailPO.getBarcode());
        pmsPurchaseBillDetailPO.setGoodsNo(pmsDemandGoodsDetailPO.getGoodsNo());
        pmsPurchaseBillDetailPO.setCategoryCode(pmsDemandGoodsDetailPO.getCategoryCode());
        pmsPurchaseBillDetailPO.setCategoryName(pmsDemandGoodsDetailPO.getCategoryName());
        pmsPurchaseBillDetailPO.setBrandCode(pmsDemandGoodsDetailPO.getBrandCode());
        pmsPurchaseBillDetailPO.setBrandName(pmsDemandGoodsDetailPO.getBrandName());
        pmsPurchaseBillDetailPO.setBasicUnit(pmsDemandGoodsDetailPO.getUnit());
        pmsPurchaseBillDetailPO.setPackageUnit(pmsDemandGoodsDetailPO.getPackageUnit());
        pmsPurchaseBillDetailPO.setSkuModel(pmsDemandGoodsDetailPO.getSkuModel());
        pmsPurchaseBillDetailPO.setSaleMode(pmsDemandGoodsDetailPO.getSaleMode());
        pmsPurchaseBillDetailPO.setInputTaxRate(pmsDemandGoodsDetailPO.getInputTaxRate());
        pmsPurchaseBillDetailPO.setOutputTaxRate(pmsDemandGoodsDetailPO.getOutputTaxRate());
        pmsPurchaseBillDetailPO.setUomAttr(pmsDemandGoodsDetailPO.getUomAttr());
        pmsPurchaseBillDetailPO.setUnitRate(pmsDemandGoodsDetailPO.getUnitRate());
        pmsPurchaseBillDetailPO.setPurchUnitRate(pmsDemandPurchShipperPO.getPurchUnitRate());
        pmsPurchaseBillDetailPO.setPromotePeriodPrice(pmsDemandPurchShipperPO.getPromotePeriodPrice());
        pmsPurchaseBillDetailPO.setPromoteActivityCode(pmsDemandPurchShipperPO.getPromoteActivityCode());
        pmsPurchaseBillDetailPO.setPromoteActivityName(pmsDemandPurchShipperPO.getPromoteActivityName());
        pmsPurchaseBillDetailPO.setContractNo(pmsDemandPurchShipperPO.getContractNo());
        pmsPurchaseBillDetailPO.setContractPrice(pmsDemandPurchShipperPO.getContractPurchPrice());
        pmsPurchaseBillDetailPO.setContractSpecialPrice(pmsDemandPurchShipperPO.getContractSpecialPrice());
        pmsPurchaseBillDetailPO.setContractMaxPrice(pmsDemandPurchShipperPO.getContractMaxPurchPrice());
        pmsPurchaseBillDetailPO.setLastPurchPrice(pmsDemandPurchShipperPO.getLastPurchPrice());
        pmsPurchaseBillDetailPO.setPurchPrice(pmsDemandPurchShipperPO.getPurchPrice());
        pmsPurchaseBillDetailPO.setStockQty(pmsDemandDeliveryToPurchPO.getDistDeptStockRealQty());
        pmsPurchaseBillDetailPO.setAtpQty(pmsDemandDeliveryToPurchPO.getDistDeptStockAtpQty());

        //下面会重算金额，因为取了订货申请行的数量
        pmsPurchaseBillDetailPO.setPurchQty(pmsDemandPurchShipperPO.getPurchQty());
        pmsPurchaseBillDetailPO.setWholeQty(pmsDemandPurchShipperPO.getPurchWholeQty());
        pmsPurchaseBillDetailPO.setOddQty(pmsDemandPurchShipperPO.getPurchOddQty());
        pmsPurchaseBillDetailPO.setPurchMoney(MoneyUtil.round2HalfUp(pmsDemandPurchShipperPO.getPurchMoney()));
        pmsPurchaseBillDetailPO.setPurchTax(MoneyUtil.round2HalfUp(pmsDemandPurchShipperPO.getPurchTax()));

        pmsPurchaseBillDetailPO.setSalePrice(BigDecimal.ZERO);//库存不用，后续再加
        pmsPurchaseBillDetailPO.setSaleMoney(BigDecimal.ZERO);//库存不用，后续再加
//        pmsPurchaseBillDetailPO.setPeriodFlag();
//        pmsPurchaseBillDetailPO.setPeriodBarcode();
//        pmsPurchaseBillDetailPO.setExpireDate();
        pmsPurchaseBillDetailPO.setBillSource(1);
        pmsPurchaseBillDetailPO.setSrcBillType(CommonBillTypeEnum.DMD.getCode());
        pmsPurchaseBillDetailPO.setSrcBillNo(pmsDemandBillResp.getBillNo());
        pmsPurchaseBillDetailPO.setSrcInsideId(pmsDemandDeliveryToPurchPO.getInsideId());

        rerunPmsPurchaseBillDetailPO(pmsPurchaseBillDetailPO);

        purchBill.setTotalQty(purchBill.getTotalQty().add(pmsPurchaseBillDetailPO.getPurchQty()));//采购总数量
        purchBill.setTotalTaxMoney(purchBill.getTotalTaxMoney().add(pmsPurchaseBillDetailPO.getPurchMoney()));//采购总金额
        purchBill.setTotalTax(purchBill.getTotalTax().add(pmsPurchaseBillDetailPO.getPurchTax()));//采购总税金


        //pms_dist2purch_supplier_record
        PmsDist2purchSupplierRecordPO pmsDist2purchSupplierRecordPO = new PmsDist2purchSupplierRecordPO();
        pmsDist2purchSupplierRecordPO.setDistDeptCode(purchBill.getDeptCode());//配送部门编码
        pmsDist2purchSupplierRecordPO.setDistDeptName(purchBill.getDeptName());//配送部门名称
        pmsDist2purchSupplierRecordPO.setDockCode(pmsDemandDeliveryToPurchPO.getDockCode());//停靠点编码
        pmsDist2purchSupplierRecordPO.setDockName(pmsDemandDeliveryToPurchPO.getDockName());//停靠点名称
        pmsDist2purchSupplierRecordPO.setSkuCode(pmsPurchaseBillDetailPO.getSkuCode());//商品编码
        pmsDist2purchSupplierRecordPO.setSkuName(pmsPurchaseBillDetailPO.getSkuName());//商品名称
        pmsDist2purchSupplierRecordPO.setSupplierCode(purchBill.getSupplierCode());//供应商编码
        pmsDist2purchSupplierRecordPO.setSupplierName(purchBill.getSupplierName());//供应商名称
        pmsDist2purchSupplierRecordPO.setLastSign(0);
        pmsDist2purchSupplierRecordPO.setCreateTime(LocalDateTime.now());
        pmsDist2purchSupplierRecordPO.setUpdateTime(LocalDateTime.now());
        purchOrderHandleDTO.getDist2purchSupplierRecordPOMap().put(pmsDist2purchSupplierRecordPO.getUniKey(),pmsDist2purchSupplierRecordPO);
    }

    /**
     * 产生配送订单
     * @param deliveryOrderHandleMap
     * @param purchOrderHandleMap
     * @param pmsDemandDeptGoodsDetailPO
     * @param pmsDemandBillResultResp
     */
    private void generateDeliveryOrder(Map<String, DeliveryOrderHandleDTO> deliveryOrderHandleMap, Map<String, PurchOrderHandleDTO> purchOrderHandleMap,
                                       PmsDemandDeptGoodsDetailPO pmsDemandDeptGoodsDetailPO, PmsDemandBillResultResp pmsDemandBillResultResp) {
        //正向
//        #针对门店要货-出货途径是配送且不是直流的行
//            需要按订货单号+订货部门+配送部门+需求批次号+送货方式 生成配送订单
//        #针对门店要货-出货途径是配送且时直流的行
//            需要按订货单号+订货部门+配送部门+需求批次号+配送部门第一供应商+合同号+停靠点+送货方式  生成配送订单，
//            同时需要根据需求单该供应商档案是否直流合单 判断，如果是直流合单的，需要根据直流采购 供应商+配送部门+合同号+需求批次号+停靠点+送货方式  生成采购订单；
//            如果供应商档案直流不合单，需要按照每笔配送订单行对应的 出货方是直流供应商的行，    订货单号+订货部门+配送部门+需求批次号+配送部门第一供应商+合同号+停靠点+送货方式，
//            且直流采购订单行和配送订单行是一一对应的。
//            注意  需要分供应商判断是否直流合单
//        #针对门店配送要货 转采购的行
//            需要按照配送部门+供应商+合同+停靠点+送货方式 分组生成采购订单

        //逆向
//        #针对门店退货-出货途径是配送的行，需要判断退货属性-退配拆单方式判断：
//        1、按配送第一供应商拆单
//            需要按               订货单号+订货部门+配送部门+配送部门商品第一供应商 生成退配订单
//        2、仅直流商品按直流供应商拆单
//            仅对直流品 按照 需要按  订货单号+订货部门+配送部门+配送部门商品直流供应商（没有直流按第一供应商） 生成退配订单
//            非直流品，则按         订货单号+订货部门+配送部门 分组
//        3、不拆单
//            按订货单号+订货部门+配送部门
        //普通配送： 出货方-配送1个
        //普通配送-直流：出货方-配送1个，出货方-采购1个
        //普通配送-配转采：出货方-配送1个，出货方-采购多个
        PmsDemandBillResp pmsDemandBillResp = pmsDemandBillResultResp.getPmsDemandBillResp();
        Map<String, OrderReturnAttrDTO> orderReturnAttrMap = new HashMap<>();
        //逆向
        if(PmsBillDirectionEnum.REVERSE.getCode().equals(pmsDemandBillResp.getBillDirection())){
            //查询订货属性
            OrderAttrQueryDTO orderAttrQueryDTO = new OrderAttrQueryDTO();
            List<OrderReturnAttrDTO> orderReturnAttrDTOS = supplychainPmsBizRuleEngineService.listOrderReturnAttr(orderAttrQueryDTO);
            orderReturnAttrMap = orderReturnAttrDTOS.parallelStream().collect(Collectors.toMap(OrderReturnAttrDTO::getAttributeCode, Function.identity()));
            //退配拆单方式 枚举：0 不拆单、1 按配送第一供应商拆单；2 仅直流商品按直流供应商拆单

        }

        int sourceIdx = 0;
        //订货申请响应未生成采购数量
        BigDecimal marginApplyQty = new BigDecimal("0");
        for (PmsDemandDeliveryShipperResp pmsDemandDeliveryShipperResp : pmsDemandBillResp.getDemandDeliveryShipperMap().get(pmsDemandDeptGoodsDetailPO.getInsideId())) {
            if(ObjectUtils.notEqual(1,pmsDemandDeliveryShipperResp.getStatus())){
                Logs.info("PmsDemandDomainServiceImpl.generateDeliveryOrder.未选中的配送出货方,不处理" + JSON.toJSONString(pmsDemandDeliveryShipperResp));
                continue;
            }
            String deliveryKey = "";
            String purchKey = "";
            String orderAttributeCode = pmsDemandBillResp.getOrderAttributeCode();

            //未生成配送的配送数量
            BigDecimal marginDeliveryShipperQty = pmsDemandDeliveryShipperResp.getResponseQty();

            List<PmsDemandDetailSourceRefPO> pmsDemandDetailSourceRefPOs = pmsDemandBillResultResp.getDemandBillHandleData().getDetailSourceRefPOMap().get(pmsDemandDeptGoodsDetailPO.getInsideId());
            for(int i = sourceIdx; i < pmsDemandDetailSourceRefPOs.size(); i++) {
                PmsDemandDetailSourceRefPO pmsDemandDetailSourceRefPO = pmsDemandDetailSourceRefPOs.get(i);
                // 订货单号+订货部门+配送部门
                deliveryKey = pmsDemandDetailSourceRefPO.getApplyBillNo() + getSplitKey(pmsDemandDeptGoodsDetailPO.getOrderDeptCode()) + getSplitKey(pmsDemandDeliveryShipperResp.getDistDeptCode());
                List<String> skuCodeList = new ArrayList<>();
                skuCodeList.add(pmsDemandDeptGoodsDetailPO.getSkuCode());


                if(0 == pmsDemandDetailSourceRefPO.getResponseQty().compareTo(new BigDecimal("0"))){
                    //响应数量为0直接continue
                    continue;
                }

                if(ObjectUtils.notEqual(1,pmsDemandDetailSourceRefPO.getStatus())){
                    continue;
                }

                //未使用数量
                BigDecimal notUseQty = pmsDemandDetailSourceRefPO.getResponseQty().add(marginApplyQty);

                Boolean isBreak = false;
                BigDecimal deliveryOrderQty;
                //未生成配送订单数量 = 未使用数量
                if(marginDeliveryShipperQty.compareTo(notUseQty) == 0){
                    sourceIdx = i + 1;
                    marginApplyQty = new BigDecimal("0");
                    deliveryOrderQty = notUseQty;

                    isBreak = true;
                }
                //未生成配送订单数量 < 未使用数量
                else if(marginDeliveryShipperQty.compareTo(notUseQty) < 0){
                    deliveryOrderQty = marginDeliveryShipperQty;
                    //订货申请未生成订货采购响应数量 - 未生成采购的供应商采购数量
                    marginApplyQty = notUseQty.subtract(marginDeliveryShipperQty);

                    isBreak = true;
                }
                //未生成配送订单数量 > 未使用数量
                else{
                    sourceIdx = i + 1;
                    marginApplyQty = new BigDecimal("0");
                    marginDeliveryShipperQty = marginDeliveryShipperQty.subtract(notUseQty);
                    deliveryOrderQty = notUseQty;
                }

                String supplierCode = "";
                String supplierName = "";
                //逆向
                if(PmsBillDirectionEnum.REVERSE.getCode().equals(pmsDemandBillResp.getBillDirection())){
                    OrderReturnAttrDTO orderReturnAttrDTO = orderReturnAttrMap.get(orderAttributeCode);
                    Integer refundSplitType = 0;
                    if(null != orderReturnAttrDTO){
                        refundSplitType = orderReturnAttrDTO.getRefundSplitType();
                    }


                    if(ObjectUtils.equals(1,refundSplitType)){
                        //按配送第一供应商拆单 订货单号+订货部门+配送部门+配送部门商品第一供应商 生成退配订单
                        //主供应商
                        ContractGoodsDeptDTO contractGoodsDeptDTO = getDeliveryMainSuppkuerCode(pmsDemandDeliveryShipperResp, skuCodeList);
                        supplierCode = contractGoodsDeptDTO.getSupplierCode();
                        supplierName = contractGoodsDeptDTO.getSupplierName();
                        deliveryKey = deliveryKey + getSplitKey(supplierCode);
                    }else if(ObjectUtils.equals(2,refundSplitType)){
                        //直流
                        if(ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),pmsDemandDeptGoodsDetailPO.getDirectSign())){
                            //仅对直流品 按照 需要按  订货单号+订货部门+配送部门+配送部门商品直流供应商（没有直流按第一供应商） 生成退配订单
                            GoodsQueryDTO goodsQueryDTO = new GoodsQueryDTO();
                            goodsQueryDTO.setAttributeNameFlag(false);
                            goodsQueryDTO.setDeptCode(pmsDemandDeliveryShipperResp.getDistDeptCode());

                            List<GoodsQueryResp> goodsQueryResps = supplychainPmsBizRuleEngineService.listGoodsInfo(goodsQueryDTO);
                            //配送部门商品直流供应商
                            if(CollectionUtils.isNotEmpty(goodsQueryResps) && StringUtils.isNotEmpty(goodsQueryResps.get(0).getGoodsInfo().getDirectSupplierCode())){
                                supplierCode = goodsQueryResps.get(0).getGoodsInfo().getDirectSupplierCode();
                                supplierName = supplierCode;
                                deliveryKey = deliveryKey + getSplitKey(supplierCode);
                            }
                            else{
                                //主供应商
                                ContractGoodsDeptDTO contractGoodsDeptDTO = getDeliveryMainSuppkuerCode(pmsDemandDeliveryShipperResp, skuCodeList);
                                supplierCode = contractGoodsDeptDTO.getSupplierCode();
                                supplierName = contractGoodsDeptDTO.getSupplierName();

                                deliveryKey = deliveryKey + getSplitKey(supplierCode);
                            }

                        }
                        else{
//                            //非直流品，则按 订货单号+订货部门+配送部门 分组
                            //默认key
                        }
                    }else{
//                        //3、不拆单 按订货单号+订货部门+配送部门 分组
                        //默认key
                    }
                }
                else{
                    //不是配转采
                    if(ObjectUtils.notEqual(1,pmsDemandDeptGoodsDetailPO.getConvertFlag())){
//        #针对门店要货-出货途径是配送且不是直流的行
//            需要按 订货单号+订货部门+配送部门+需求批次号+送货方式 生成配送订单

//        #针对门店要货-出货途径是配送且时直流的行
//            需要按订货单号+订货部门+配送部门+需求批次号+配送部门第一供应商+合同号+停靠点+送货方式  生成配送订单，

//            同时需要根据需求单该供应商档案是否直流合单 判断，如果是直流合单的，需要根据直流采购 供应商+配送部门+合同号+需求批次号+停靠点+送货方式  生成采购订单；
//            如果供应商档案直流不合单，需要按照每笔配送订单行对应的 出货方是直流供应商的行，    订货单号+订货部门+配送部门+需求批次号+配送部门第一供应商+合同号+停靠点+送货方式，
//            且直流采购订单行和配送订单行是一一对应的。
//            注意  需要分供应商判断是否直流合单
                        //如果是紧急  紧急订货属性时，需求批次分组时按空处理
                        String purchBatchNo = pmsDemandDeliveryShipperResp.getPurchBatchNo();
                        if(OrderAttributeEnum.JIN_JI.getCode().equals(pmsDemandBillResp.getOrderAttributeCode())){
                            purchBatchNo = "";
                        }
                        //普通配送： 出货方-配送1个
                        if(ObjectUtils.notEqual(DirectSignEnum.DIRECT.getCode(),pmsDemandDeliveryShipperResp.getDirectSign())){
                            deliveryKey = deliveryKey +  getSplitKey(purchBatchNo) + getSplitKey(pmsDemandDeptGoodsDetailPO.getSendMode() + "");
                        }
                        else{
                            //普通配送-直流：出货方-配送1个，出货方-采购1个
                            ContractGoodsDeptDTO contractGoodsDeptDTO = getDeliveryMainSuppkuerCode(pmsDemandDeliveryShipperResp, skuCodeList);
                            deliveryKey = deliveryKey + getSplitKey(purchBatchNo) + getSplitKey(contractGoodsDeptDTO.getSupplierCode());
                            //直流 - 排除配转采的数据
                            List<PmsDemandPurchShipperResp> pmsDemandPurchShipperResps = pmsDemandBillResp.getDemandPurchShipperMap().get(pmsDemandDeptGoodsDetailPO.getInsideId()).
                                    parallelStream().filter(t -> ObjectUtils.equals(0,t.getConvertFlag())).collect(Collectors.toList());

                            if(CollectionUtils.isEmpty(pmsDemandPurchShipperResps)){
                                Logs.info("PmsDemandDomainServiceImpl.generateDeliveryOrder.直流出货方供应商为空,不处理" + JSON.toJSONString(pmsDemandDeliveryShipperResp));
                                continue;
                            }
                            PmsDemandPurchShipperResp pmsDemandPurchShipperResp = pmsDemandPurchShipperResps.stream().filter(e -> ObjectUtils.equals(1,e.getStatus())).collect(Collectors.toList())
                                    .get(0);
                            deliveryKey = deliveryKey + getSplitKey(pmsDemandPurchShipperResp.getContractNo()) + getSplitKey(pmsDemandDeliveryShipperResp.getDockCode()) +
                                    getSplitKey(pmsDemandDeptGoodsDetailPO.getSendMode()+ "");


                            //TODO 判断供应商是不是直流合并
                            Boolean isMergeBill = false;
                            if(isMergeBill){
                                //供应商+配送部门+合同号+需求批次号+停靠点+送货方式 + 采购包装率
                                purchKey = pmsDemandPurchShipperResp.getSupplierCode() + getSplitKey(pmsDemandDeliveryShipperResp.getDistDeptCode()) +
                                        getSplitKey(pmsDemandPurchShipperResp.getContractNo()) + getSplitKey(purchBatchNo) +
                                        getSplitKey(pmsDemandDeliveryShipperResp.getDockCode()) + getSplitKey(pmsDemandDeptGoodsDetailPO.getSendMode()+ "")
                                        + getSplitKey(pmsDemandPurchShipperResp.getPurchUnitRate().toString());
                            }
                            else{
                                //订货单号+订货部门+配送部门+需求批次号+配送部门第一供应商+合同号+停靠点+送货方式，
                                ContractGoodsDeptDTO deliveryMainSuppkuerCode = getDeliveryMainSuppkuerCode(pmsDemandDeliveryShipperResp, skuCodeList);
                                purchKey = pmsDemandDetailSourceRefPO.getApplyBillNo() + getSplitKey(pmsDemandDeptGoodsDetailPO.getOrderDeptCode()) +
                                        getSplitKey(pmsDemandDeliveryShipperResp.getDistDeptCode()) + getSplitKey(purchBatchNo) + getSplitKey(deliveryMainSuppkuerCode.getSupplierCode())
                                + getSplitKey(pmsDemandPurchShipperResp.getContractNo()) + getSplitKey(pmsDemandDeliveryShipperResp.getDockCode()) + getSplitKey(pmsDemandDeptGoodsDetailPO.getSendMode()+ "");
                            }

                            //先生成采购订单
                            if(StringUtils.isNotEmpty(purchKey)){
                                Logs.info("PmsDemandDomainServiceImpl.generateDeliveryOrder.purchKey:" + purchKey + ",pmsDemandPurchShipperResp:" + JSON.toJSONString(pmsDemandPurchShipperResp)
                                        + ",pmsDemandDetailSourceRefPO:" + JSON.toJSONString(pmsDemandDetailSourceRefPO));
                                buildPurchOrder(purchKey,purchOrderHandleMap,pmsDemandDeptGoodsDetailPO,pmsDemandPurchShipperResp,pmsDemandBillResp,deliveryOrderQty
                                        , pmsDemandDetailSourceRefPO,isMergeBill,pmsDemandDeliveryShipperResp);
                            }
                        }
                    }
                    else{
                        //配转采不在此处理
                    }
                }
                buildDeliveryOrder(deliveryKey,deliveryOrderHandleMap,pmsDemandDeptGoodsDetailPO,pmsDemandDeliveryShipperResp,pmsDemandBillResp,
                        deliveryOrderQty, pmsDemandDetailSourceRefPO,supplierCode,supplierName);

                if(StringUtils.isNotEmpty(purchKey)){
                    //处理直流对应的采购订单的关联数据 -- 采购订单

                    PurchOrderHandleDTO purchOrderHandleDTO = purchOrderHandleMap.get(purchKey);
                    PmsPurchaseOrderPO purchBill = purchOrderHandleDTO.getPurchBill();
                    List<PmsPurchaseBillDetailPO> purchBillDetailList = purchOrderHandleDTO.getPurchBillDetailList();

                    //处理直流对应的配送订单的关联数据 -- 配送订单
                    DeliveryOrderHandleDTO deliveryOrderHandleDTO = deliveryOrderHandleMap.get(deliveryKey);
                    WdDeliveryBillPO deliveryBill = deliveryOrderHandleDTO.getDeliveryBill();
                    List<WdDeliveryBillDetailPO> deliveryBillDetailList = deliveryOrderHandleDTO.getDeliveryBillDetailList();

                    deliveryBill.setDirectPurchaseBillNo(purchBill.getBillNo());// 直流采购单号
                    deliveryBillDetailList.get(deliveryBillDetailList.size() - 1 ).setDirectPurchBillNo(purchBill.getBillNo());//直流采购订单单号 稍后处理
                    deliveryBillDetailList.get(deliveryBillDetailList.size() - 1).setDirectPurchInsideId(purchBillDetailList.get(purchBillDetailList.size() - 1).getInsideId());//直流采购订单行号 稍后处理
                }


                if(isBreak){
                    break;
                }
            }

        }

    }

    /**
     * 组装配送订单
     * @param deliveryKey
     * @param deliveryOrderHandleMap
     * @param pmsDemandDeptGoodsDetailPO
     * @param pmsDemandDeliveryShipperResp
     * @param pmsDemandBillResp
     * @param deliveryOrderQty
     * @param pmsDemandDetailSourceRefPO
     * @param supplierCode
     * @param supplierName
     */
    private void buildDeliveryOrder(String deliveryKey, Map<String, DeliveryOrderHandleDTO> deliveryOrderHandleMap, PmsDemandDeptGoodsDetailPO pmsDemandDeptGoodsDetailPO,
                                    PmsDemandDeliveryShipperResp pmsDemandDeliveryShipperResp, PmsDemandBillResp pmsDemandBillResp, BigDecimal deliveryOrderQty,
                                    PmsDemandDetailSourceRefPO pmsDemandDetailSourceRefPO,String supplierCode,
                                            String supplierName) {
        DeliveryOrderHandleDTO deliveryOrderHandleDTO = new DeliveryOrderHandleDTO();
        if(deliveryOrderHandleMap.containsKey(deliveryKey)){
            deliveryOrderHandleDTO = deliveryOrderHandleMap.get(deliveryKey);
        }
        else{
            deliveryOrderHandleMap.put(deliveryKey,deliveryOrderHandleDTO);
        }

        WdDeliveryBillPO deliveryBill = deliveryOrderHandleDTO.getDeliveryBill();
        if(null == deliveryBill){
            deliveryBill = new WdDeliveryBillPO();
            //获取配送单号
            String billNo = supplychainPmsBizRuleEngineService.getBillNo(MdBillNoBillTypeEnum.WDS_DELIVERY_ORDER,pmsDemandDeptGoodsDetailPO.getOrderDeptCode());
            deliveryBill.setBillNo(billNo);
            deliveryBill.setWhCode(pmsDemandDeliveryShipperResp.getDistDeptCode());
            deliveryBill.setWhName(pmsDemandDeliveryShipperResp.getDistDeptName());
            deliveryBill.setInDeptCode(pmsDemandDeptGoodsDetailPO.getOrderDeptCode());
            deliveryBill.setInDeptName(pmsDemandDeptGoodsDetailPO.getOrderDeptName());
//            deliveryBill.setContactMan(pmsDemandDetailSourceRefPO.getSrcBuyerName());
//            deliveryBill.setContactTel(pmsDemandDetailSourceRefPO.getSrcBuyerTel());
//            deliveryBill.setContactAddr(pmsDemandDetailSourceRefPO.getSrcBuyerAddr());
            deliveryBill.setBillDirection(pmsDemandBillResp.getBillDirection());
            //订货部门类型(1:门店2:配送)
            deliveryBill.setBillType(1);
            deliveryBill.setBillSource(WDDeliveryOrderSourceEnum.DEMAND.getCode());
            deliveryBill.setStatus(WDDeliveryOrderBillStatusEnum.WAIT_CHANGE_FROM_DEMAND.getCode());
//            String validDaysStr = iSupplychainBizSysParamRuleService.getValue(PMSSystemParamEnum.DEMAND_DELIVER_ORDER_VALID_DAYS);
//            String deliveryDaysStr = iSupplychainBizSysParamRuleService.getValue(PMSSystemParamEnum.DEMAND_DELIVER_ORDER_DELIVER_DAYS);
//
//            int validDays = NumberUtil.getInteger(validDaysStr,7);
//            int deliveryDays = NumberUtil.getInteger(deliveryDaysStr,7);
//
//            deliveryBill.setValidDate(LocalDate.now().plusDays(validDays));
//            deliveryBill.setDeliveryDate(LocalDate.now().plusDays(deliveryDays));

            deliveryBill.setDeliveryDate(pmsDemandBillResp.getDeliverDeliverDate());
            deliveryBill.setValidDate(pmsDemandBillResp.getDeliverValidityDate());

            deliveryBill.setSendMode(pmsDemandDetailSourceRefPO.getSrcSendMode());
            deliveryBill.setCauseCode(StringUtils.isNotEmpty(pmsDemandDetailSourceRefPO.getSrcRefundReason()) ? pmsDemandDetailSourceRefPO.getSrcRefundReason() : pmsDemandBillResp.getRefundReason());
            deliveryBill.setCauseName(StringUtils.isNotEmpty(pmsDemandDetailSourceRefPO.getSrcRefundReasonDesc()) ? pmsDemandDetailSourceRefPO.getSrcRefundReasonDesc() : pmsDemandBillResp.getRefundReasonDesc());

            if(ObjectUtils.equals(2,pmsDemandDetailSourceRefPO.getType())){
                String orderAttributeCode = pmsDemandBillResp.getOrderAttributeCode();
                String orderAttributeName = pmsDemandBillResp.getOrderAttributeName();
                if(StringUtils.isNotEmpty(orderAttributeCode)){
                    deliveryBill.setOrderAttributeCode(orderAttributeCode.split(",")[0]);
                    deliveryBill.setOrderAttributeName(orderAttributeName.split(",")[0]);
                }
            }else{
                deliveryBill.setOrderAttributeCode(pmsDemandDetailSourceRefPO.getAttributeCode());
                deliveryBill.setOrderAttributeName(pmsDemandDetailSourceRefPO.getAttributeName());
            }

            deliveryBill.setSrcBillNo(pmsDemandBillResp.getBillNo());
            deliveryBill.setSrcBillRemark(pmsDemandBillResp.getRemark());
            deliveryBill.setDirectSign(pmsDemandDeliveryShipperResp.getDirectSign());
            deliveryBill.setDirectPurchaseBillNo("");// 直接采购单号 后续执行
            deliveryBill.setRemark(pmsDemandDetailSourceRefPO.getRemark());

            deliveryBill.setPurchBatchNo(pmsDemandDeliveryShipperResp.getPurchBatchNo());
            deliveryBill.setCustomerCode(pmsDemandDetailSourceRefPO.getSrcCustomerCode());
            deliveryBill.setCustomerName(pmsDemandDetailSourceRefPO.getSrcCustomerName());
            deliveryBill.setSupplierCode(supplierCode);
            deliveryBill.setSupplierName(supplierName);

            deliveryBill.setTotalQty(BigDecimal.ZERO);//配送订单总数量 父方法尾部处理
            deliveryBill.setTotalSkuCount(0);//商品品项数
            deliveryBill.setTotalTaxMoney(BigDecimal.ZERO);//配送订单总金额
            deliveryBill.setTotalTax(BigDecimal.ZERO);//配送订单总税金

            if(ObjectUtils.equals(PmsBillDirectionEnum.REVERSE.getCode(),deliveryBill.getBillDirection())){
                StoreDetailResp storeDetailResp = baseStoreUtil.getSingleDeptInfo(deliveryBill.getWhCode());
                if(null != storeDetailResp){
                    deliveryBill.setContactMan(storeDetailResp.getManagerUserName());
                    deliveryBill.setContactTel(storeDetailResp.getServiceHotline());
                    deliveryBill.setContactAddr(storeDetailResp.getWholeAddress());
                }
            }
            else{
                StoreDetailResp storeDetailResp = baseStoreUtil.getSingleDeptInfo(deliveryBill.getInDeptCode());
                if(null != storeDetailResp){
                    deliveryBill.setContactMan(storeDetailResp.getManagerUserName());
                    deliveryBill.setContactTel(storeDetailResp.getServiceHotline());
                    deliveryBill.setContactAddr(storeDetailResp.getWholeAddress());
                }
            }

            deliveryOrderHandleDTO.setDeliveryBill(deliveryBill);
        }

        WdDeliveryBillDetailPO wdDeliveryBillDetailPO = new WdDeliveryBillDetailPO();

        Long insideId = Long.valueOf(deliveryOrderHandleDTO.getDeliveryBillDetailList().size()) + 1;
        deliveryOrderHandleDTO.getDeliveryBillDetailList().add(wdDeliveryBillDetailPO);


        PmsDemandGoodsDetailResp pmsDemandGoodsDetailResp = pmsDemandBillResp.getDemandGoodsDetailMap().get(pmsDemandDeptGoodsDetailPO.getPinsideId());

        wdDeliveryBillDetailPO.setBillNo(deliveryBill.getBillNo());
        wdDeliveryBillDetailPO.setWhCode(deliveryBill.getWhCode());
        wdDeliveryBillDetailPO.setWhName(deliveryBill.getWhName());
        wdDeliveryBillDetailPO.setInDeptCode(deliveryBill.getInDeptCode());
        wdDeliveryBillDetailPO.setInDeptName(deliveryBill.getInDeptName());
        wdDeliveryBillDetailPO.setBillType(deliveryBill.getBillType());
        wdDeliveryBillDetailPO.setInsideId(insideId);
        wdDeliveryBillDetailPO.setSkuType(pmsDemandDeptGoodsDetailPO.getGoodsType());
        wdDeliveryBillDetailPO.setSkuCode(pmsDemandDeptGoodsDetailPO.getSkuCode());
        wdDeliveryBillDetailPO.setSkuName(pmsDemandDeptGoodsDetailPO.getSkuName());
        wdDeliveryBillDetailPO.setBarcode(pmsDemandGoodsDetailResp.getBarcode());
        wdDeliveryBillDetailPO.setGoodsNo(pmsDemandGoodsDetailResp.getGoodsNo());
        wdDeliveryBillDetailPO.setCategoryCode(pmsDemandGoodsDetailResp.getCategoryCode());
        wdDeliveryBillDetailPO.setCategoryName(pmsDemandGoodsDetailResp.getCategoryName());
        wdDeliveryBillDetailPO.setBrandCode(pmsDemandGoodsDetailResp.getBrandCode());
        wdDeliveryBillDetailPO.setBrandName(pmsDemandGoodsDetailResp.getBrandName());

        wdDeliveryBillDetailPO.setUnit(BigDecimal.ZERO);//计量单位;商品计量单位
        wdDeliveryBillDetailPO.setBasicUnit(pmsDemandGoodsDetailResp.getUnit());
        wdDeliveryBillDetailPO.setPackageUnit(pmsDemandGoodsDetailResp.getPackageUnit());
        wdDeliveryBillDetailPO.setUnitRate(pmsDemandGoodsDetailResp.getUnitRate());
        wdDeliveryBillDetailPO.setUomAttr(pmsDemandGoodsDetailResp.getUomAttr());
        wdDeliveryBillDetailPO.setSkuModel(pmsDemandGoodsDetailResp.getSkuModel());
        wdDeliveryBillDetailPO.setInputTaxRate(pmsDemandGoodsDetailResp.getInputTaxRate());
        wdDeliveryBillDetailPO.setOutputTaxRate(pmsDemandGoodsDetailResp.getOutputTaxRate());
        wdDeliveryBillDetailPO.setPgoodsCode("");
        wdDeliveryBillDetailPO.setPgoodsName("");
        wdDeliveryBillDetailPO.setPgoodsNo("");
        wdDeliveryBillDetailPO.setPbarcode("");
        wdDeliveryBillDetailPO.setDirectSign(pmsDemandDeliveryShipperResp.getDirectSign());
        wdDeliveryBillDetailPO.setDockCode(pmsDemandDeliveryShipperResp.getDockCode());
        wdDeliveryBillDetailPO.setDirectPurchBillNo("");//直流采购订单单号 稍后处理
        wdDeliveryBillDetailPO.setDirectPurchInsideId(0L);//直流采购订单行号 稍后处理

        wdDeliveryBillDetailPO.setDeptDistPrice(pmsDemandDeliveryShipperResp.getGoodsDistPrice());
        wdDeliveryBillDetailPO.setSkuDistPrice(pmsDemandDeliveryShipperResp.getGoodsDistPrice());

        wdDeliveryBillDetailPO.setDeptCostPrice(pmsDemandDeliveryShipperResp.getDistDeptCostPrice());
        wdDeliveryBillDetailPO.setLastPurchPrice(pmsDemandDeliveryShipperResp.getLastPurchPrice());
        wdDeliveryBillDetailPO.setDeptSkuPurchPrice(pmsDemandDeliveryShipperResp.getLastPurchPrice());
        wdDeliveryBillDetailPO.setSkuPurchPrice(pmsDemandDeliveryShipperResp.getLastPurchPrice());

        wdDeliveryBillDetailPO.setDeliveryPrice(pmsDemandDeliveryShipperResp.getDistPrice());
        wdDeliveryBillDetailPO.setDeliveryQty(deliveryOrderQty);

        //重算，因为取了订货申请行的数量
        wdDeliveryBillDetailPO.setWholeQty(pmsDemandDeliveryShipperResp.getDeliveryWholeQty());//箱数
        wdDeliveryBillDetailPO.setOddQty(pmsDemandDeliveryShipperResp.getDeliveryOddQty());//零头数量
        wdDeliveryBillDetailPO.setDeliveryTaxMoney(MoneyUtil.round2HalfUp(pmsDemandDeliveryShipperResp.getDistMoney()));//配送金额
        wdDeliveryBillDetailPO.setDeliveryTax(MoneyUtil.round2HalfUp(pmsDemandDeliveryShipperResp.getDistTax()));//配送税金


        rerunPmsDeliveryBillDetailPO(wdDeliveryBillDetailPO);

        wdDeliveryBillDetailPO.setOutDeptStkQty(pmsDemandDeliveryShipperResp.getDistDeptStockRealQty());//发货部门库存数量
        wdDeliveryBillDetailPO.setOutDeptStkQty(pmsDemandDeliveryShipperResp.getDistDeptStockAtpQty());//发货部门库存可用数量
        wdDeliveryBillDetailPO.setInDeptStkQty(pmsDemandDeptGoodsDetailPO.getOrderStockQty());//入货部门库存数量
        wdDeliveryBillDetailPO.setInDeptAtpQty(pmsDemandDeptGoodsDetailPO.getOrderAtpQty());//入货部门库存可用数量

        wdDeliveryBillDetailPO.setBillSource(2);//单据来源 1手工 2需求单
        wdDeliveryBillDetailPO.setBusinessStatusCode(pmsDemandDeptGoodsDetailPO.getWorkStateCode());
        wdDeliveryBillDetailPO.setCirculationRouteCode(pmsDemandDeptGoodsDetailPO.getCirculationModeCode());

        wdDeliveryBillDetailPO.setPackageRate(pmsDemandGoodsDetailResp.getUnitRate());
        wdDeliveryBillDetailPO.setOrderUnitRate(pmsDemandDeliveryShipperResp.getDeliveryUnitRate());

        wdDeliveryBillDetailPO.setPromotePeriodPrice(pmsDemandDeliveryShipperResp.getPromotePrice());
        wdDeliveryBillDetailPO.setPromoteActivityCode(pmsDemandDeliveryShipperResp.getPromoteCode());
        wdDeliveryBillDetailPO.setPromoteActivityName(pmsDemandDeliveryShipperResp.getPromoteName());
        wdDeliveryBillDetailPO.setTransferPurchSign(pmsDemandDeliveryShipperResp.getConvertFlag());//采购转配送标记 是否转采[0 否 1 是 ]

        wdDeliveryBillDetailPO.setSrcBillType(CommonBillTypeEnum.DMD.getCode());//来源单据类型
        wdDeliveryBillDetailPO.setSrcBillNo(pmsDemandDeptGoodsDetailPO.getBillNo());//来源单据号
        wdDeliveryBillDetailPO.setSrcInsideId(pmsDemandDeptGoodsDetailPO.getInsideId());//来源单据行号
        wdDeliveryBillDetailPO.setSalePrice(pmsDemandDeptGoodsDetailPO.getSalePrice());//零售单价
        wdDeliveryBillDetailPO.setSaleMoney(pmsDemandDeptGoodsDetailPO.getSalePrice().multiply(wdDeliveryBillDetailPO.getDeliveryQty()));//零售金额
        wdDeliveryBillDetailPO.setPurchBatchNo(pmsDemandDeliveryShipperResp.getPurchBatchNo());

        deliveryBill.setTotalQty(deliveryBill.getTotalQty().add(wdDeliveryBillDetailPO.getDeliveryQty()));//总数量
        deliveryBill.setTotalTaxMoney(deliveryBill.getTotalTaxMoney().add(wdDeliveryBillDetailPO.getDeliveryTaxMoney()));//总金额
        deliveryBill.setTotalTax(deliveryBill.getTotalTax().add(wdDeliveryBillDetailPO.getDeliveryTax()));//总税金


        //需求单出货方与采购配送订单关联关系
        List<PmsDemandPruchDeliveryRefPO> pruchDeliveryRefList = deliveryOrderHandleDTO.getPruchDeliveryRefList();

        PmsDemandPruchDeliveryRefPO pmsDemandPruchDeliveryRefPO = new PmsDemandPruchDeliveryRefPO();
        pmsDemandPruchDeliveryRefPO.setBillNo(pmsDemandDeptGoodsDetailPO.getBillNo());
        pmsDemandPruchDeliveryRefPO.setRefBillNo(deliveryBill.getBillNo());
        pmsDemandPruchDeliveryRefPO.setType(1);
        pmsDemandPruchDeliveryRefPO.setDeliveryShipperInsideId(pmsDemandDeliveryShipperResp.getInsideId());
        pmsDemandPruchDeliveryRefPO.setDeptGoodsInsideId(pmsDemandDeptGoodsDetailPO.getInsideId());
        pmsDemandPruchDeliveryRefPO.setGoodsInsideId(pmsDemandDeptGoodsDetailPO.getPinsideId());
        pmsDemandPruchDeliveryRefPO.setQty(deliveryOrderQty);
        pmsDemandPruchDeliveryRefPO.setRefInsideId(insideId);
        pmsDemandPruchDeliveryRefPO.setApplyBillNo(pmsDemandDetailSourceRefPO.getApplyBillNo());
        pmsDemandPruchDeliveryRefPO.setApplyInsideId(pmsDemandDetailSourceRefPO.getSrcInsideId());
        pmsDemandPruchDeliveryRefPO.setSkuCode(pmsDemandDeptGoodsDetailPO.getSkuCode());

        pruchDeliveryRefList.add(pmsDemandPruchDeliveryRefPO);
    }

    /**
     * 下划线拼接字符串
     * @param target
     * @return
     */
    private String getSplitKey(String target){
        return "_" + target;
    }

    /**
     * 下划线拼接字符串
     * @param target
     * @return
     */
    private String getSplitKey(Integer target){
        if(null == target){
            return "_";
        }
        return "_" + target + "";
    }

    /**
     * 下划线拼接字符串
     * @param target
     * @return
     */
    private String getSplitKey(BigDecimal target){
        if(null == target){
            return "_";
        }
        return "_" + target.toString();
    }

    /**
     * 查询主供应商
     * @param pmsDemandDeliveryShipperResp
     * @param skuCodeList
     * @return
     */
    private ContractGoodsDeptDTO getDeliveryMainSuppkuerCode(PmsDemandDeliveryShipperResp pmsDemandDeliveryShipperResp, List<String> skuCodeList) {
        ContractGoodsDeptQueryDTO contractGoodsDeptQuery = new ContractGoodsDeptQueryDTO();
        contractGoodsDeptQuery.setSkuCodeList(skuCodeList);
        contractGoodsDeptQuery.setDeptCode(pmsDemandDeliveryShipperResp.getDistDeptCode());
        contractGoodsDeptQuery.setMainSupplierMode(1);
        List<ContractGoodsDeptDTO> contractGoodsDeptDTOS = supplychainPmsBizRuleEngineService.listContractGoodsDept(contractGoodsDeptQuery);
        if (CollectionUtils.isNotEmpty(contractGoodsDeptDTOS) && StringUtils.isNotEmpty(contractGoodsDeptDTOS.get(0).getSupplierCode())) {
            return contractGoodsDeptDTOS.get(0);
        }
        return new ContractGoodsDeptDTO();
    }

    /**
     * 产生采购订单
     * @param purchOrderHandleMap
     * @param pmsDemandDeptGoodsDetailPO
     * @param pmsDemandBillResultResp
     */
    private void generatePurchOrder(Map<String, PurchOrderHandleDTO> purchOrderHandleMap, PmsDemandDeptGoodsDetailPO pmsDemandDeptGoodsDetailPO,
                                    PmsDemandBillResultResp pmsDemandBillResultResp) {
//            正向
//        #针对配送要货-出货途径是采购的行
//            按照          配送部门+供应商+合同分组 生成采购订单
//        #针对门店要货-出货途径是采购的行
//            需要按 订货单号+订货部门+供应商+合同 分组生成采购订单

//        逆向
//        #针对配送退货-出货途径是采购的行
//            按照          配送部门+供应商+合同分组 生成采退订单
//        #针对门店退货-出货途径是采购的行
//            需要按 订货单号+订货部门+供应商+合同     分组生成采退订单

        //普通采购：出货方-配送0，出货方-采购多个
        PmsDemandBillResp pmsDemandBillResp = pmsDemandBillResultResp.getPmsDemandBillResp();
        int sourceIdx = 0;
        //订货申请响应未生成采购数量
        BigDecimal marginApplyQty = new BigDecimal("0");

        for (PmsDemandPurchShipperResp pmsDemandPurchShipperResp : pmsDemandBillResp.getDemandPurchShipperMap().get(pmsDemandDeptGoodsDetailPO.getInsideId())) {
            if(ObjectUtils.equals(1,pmsDemandPurchShipperResp.getConvertFlag())){
                Logs.info("PmsDemandDomainServiceImpl.generatePurchOrder.配转采数据,本方法不处理" + JSON.toJSONString(pmsDemandPurchShipperResp));
                continue;
            }

            if(ObjectUtils.equals(0,pmsDemandPurchShipperResp.getStatus())){
                continue;
            }
            //key = 部门+供应商+合同 + 订货单号(门店)
            String purchKey = pmsDemandDeptGoodsDetailPO.getOrderDeptCode() + getSplitKey(pmsDemandPurchShipperResp.getSupplierCode()) +
                    getSplitKey(pmsDemandPurchShipperResp.getContractNo());

            //未生成采购的供应商采购数量
            BigDecimal marginPurchShipperQty = pmsDemandPurchShipperResp.getPurchQty();

            List<PmsDemandDetailSourceRefPO> pmsDemandDetailSourceRefPOs = pmsDemandBillResultResp.getDemandBillHandleData().getDetailSourceRefPOMap().get(pmsDemandDeptGoodsDetailPO.getInsideId());
            //门店
            if(ObjectUtils.equals(1,pmsDemandDeptGoodsDetailPO.getOrderDeptType())){
                for(int i = sourceIdx; i < pmsDemandDetailSourceRefPOs.size(); i++){
                    PmsDemandDetailSourceRefPO pmsDemandDetailSourceRefPO = pmsDemandDetailSourceRefPOs.get(i);

                    purchKey += getSplitKey(pmsDemandDetailSourceRefPO.getApplyBillNo());

                    if(0 == pmsDemandDetailSourceRefPO.getResponseQty().compareTo(new BigDecimal("0"))){
                        //响应数量为0直接continue
                        continue;
                    }

                    if(ObjectUtils.notEqual(1,pmsDemandDetailSourceRefPO.getStatus())){
                        continue;
                    }

                    //未使用数量
                    BigDecimal notUseQty = pmsDemandDetailSourceRefPO.getResponseQty().add(marginApplyQty);

                    Boolean isBreak = false;
                    BigDecimal purchOrderQty;
                    //未生成采购订单数量 = 未使用数量
                    if(marginPurchShipperQty.compareTo(notUseQty) == 0){
                        sourceIdx = i + 1;
                        marginApplyQty = new BigDecimal("0");
                        purchOrderQty = notUseQty;

                        isBreak = true;
                    }
                    //未生成采购订单数量 < 未使用数量
                    else if(marginPurchShipperQty.compareTo(notUseQty) < 0){
//                        sourceIdx = i + 1;
                        purchOrderQty = marginPurchShipperQty;
                        //订货申请未生成订货采购响应数量 - 未生成采购的供应商采购数量
                        marginApplyQty = notUseQty.subtract(marginPurchShipperQty);

                        isBreak = true;
                    }
                    //未生成采购订单数量 > 未使用数量
                    else{
                        sourceIdx = i + 1;
                        marginApplyQty = new BigDecimal("0");
                        marginPurchShipperQty = marginPurchShipperQty.subtract(notUseQty);
                        purchOrderQty = notUseQty;
                    }

                    Logs.info("PmsDemandDomainServiceImpl.generatePurchOrder.purchKey:" + purchKey + ",pmsDemandPurchShipperResp:" + JSON.toJSONString(pmsDemandPurchShipperResp)
                            + ",pmsDemandDetailSourceRefPO:" + JSON.toJSONString(pmsDemandDetailSourceRefPO));
                    buildPurchOrder(purchKey,purchOrderHandleMap,pmsDemandDeptGoodsDetailPO,pmsDemandPurchShipperResp,pmsDemandBillResp,purchOrderQty
                            , pmsDemandDetailSourceRefPO,false,new PmsDemandDeliveryShipperResp());

                    if(isBreak){
                        break;
                    }

                }
            }
            else{
                //配送只有主派，也只有一行来源
                PmsDemandDetailSourceRefPO pmsDemandDetailSourceRefPO = pmsDemandDetailSourceRefPOs.get(0);
                Logs.info("PmsDemandDomainServiceImpl.generatePurchOrder.purchKey:" + purchKey + ",pmsDemandPurchShipperResp:" + JSON.toJSONString(pmsDemandPurchShipperResp)
                        + ",pmsDemandDetailSourceRefResp:" + JSON.toJSONString(pmsDemandDetailSourceRefPO));
                buildPurchOrder(purchKey,purchOrderHandleMap,pmsDemandDeptGoodsDetailPO,pmsDemandPurchShipperResp,pmsDemandBillResp,marginPurchShipperQty
                        , pmsDemandDetailSourceRefPO,false,new PmsDemandDeliveryShipperResp());
            }



        }
    }

    /**
     * 组装采购订单
     * @param purchKey
     * @param purchOrderHandleMap
     * @param pmsDemandDeptGoodsDetailPO
     * @param pmsDemandPurchShipperResp
     * @param pmsDemandBillResp
     */
    private void buildPurchOrder(String purchKey,Map<String, PurchOrderHandleDTO> purchOrderHandleMap, PmsDemandDeptGoodsDetailPO pmsDemandDeptGoodsDetailPO,
                                 PmsDemandPurchShipperResp pmsDemandPurchShipperResp, PmsDemandBillResp pmsDemandBillResp,BigDecimal purchOrderQty,
                                 PmsDemandDetailSourceRefPO pmsDemandDetailSourceRefPO,Boolean isMergeBill,PmsDemandDeliveryShipperResp pmsDemandDeliveryShipperResp) {
        PurchOrderHandleDTO purchOrderHandleDTO = new PurchOrderHandleDTO();
        if(purchOrderHandleMap.containsKey(purchKey)){
            purchOrderHandleDTO = purchOrderHandleMap.get(purchKey);
        }
        else{
            purchOrderHandleMap.put(purchKey,purchOrderHandleDTO);
        }

        PmsPurchaseOrderPO purchBill = purchOrderHandleDTO.getPurchBill();
        if(null == purchBill){
            purchBill = new PmsPurchaseOrderPO();
            //获取采购单号
            String billNo = supplychainPmsBizRuleEngineService.getBillNo(MdBillNoBillTypeEnum.PMS_PURCHASE_ORDER,pmsDemandDeptGoodsDetailPO.getOrderDeptCode());
            purchBill.setBillNo(billNo);
            //如是直流
            if(ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),pmsDemandDeptGoodsDetailPO.getDirectSign())){
                purchBill.setDeptCode(pmsDemandDeliveryShipperResp.getDistDeptCode());
                purchBill.setDeptName(pmsDemandDeliveryShipperResp.getDistDeptName());
                purchBill.setBillType(1);
            }
            else{
                purchBill.setDeptCode(pmsDemandDeptGoodsDetailPO.getOrderDeptCode());
                purchBill.setDeptName(pmsDemandDeptGoodsDetailPO.getOrderDeptName());
                //订货部门类型(1:门店2:配送)
                if(ObjectUtils.equals(1,pmsDemandDeptGoodsDetailPO.getOrderDeptType())){
                    purchBill.setBillType(0);
                }
                else{
                    purchBill.setBillType(1);
                }
            }

            purchBill.setDeptOperateMode(pmsDemandDeptGoodsDetailPO.getDeptOperateMode());
            purchBill.setSupplierCode(pmsDemandPurchShipperResp.getSupplierCode());
            purchBill.setSupplierName(pmsDemandPurchShipperResp.getSupplierName());
            purchBill.setBillDirection(pmsDemandBillResp.getBillDirection());

            if(ObjectUtils.equals(2,pmsDemandDetailSourceRefPO.getType())){
                String orderAttributeCode = pmsDemandBillResp.getOrderAttributeCode();
                String orderAttributeName = pmsDemandBillResp.getOrderAttributeName();
                if(StringUtils.isNotEmpty(orderAttributeCode)){
                    purchBill.setOrderAttributeCode(orderAttributeCode.split(",")[0]);
                    purchBill.setOrderAttributeName(orderAttributeName.split(",")[0]);
                }
            }
            else{
                purchBill.setOrderAttributeCode(pmsDemandDetailSourceRefPO.getAttributeCode());
                purchBill.setOrderAttributeName(pmsDemandDetailSourceRefPO.getAttributeName());
            }

            purchBill.setStatus(99);
            purchBill.setSendMode(pmsDemandDetailSourceRefPO.getSrcSendMode());


//            String validDaysStr = iSupplychainBizSysParamRuleService.getValue(PMSSystemParamEnum.DEMAND_PURCH_ORDER_VALID_DAYS);
//            String deliveryDaysStr = iSupplychainBizSysParamRuleService.getValue(PMSSystemParamEnum.DEMAND_PURCH_ORDER_DELIVER_DAYS);
//
//            int validDays = NumberUtil.getInteger(validDaysStr,7);
//            int deliveryDays = NumberUtil.getInteger(deliveryDaysStr,7);
//
//            purchBill.setDeliverDate(LocalDate.now().plusDays(deliveryDays));
//            purchBill.setValidityDate(LocalDate.now().plusDays(validDays));

            purchBill.setDeliverDate(pmsDemandBillResp.getPurchDeliverDate());
            purchBill.setValidityDate(pmsDemandBillResp.getPurchValidityDate());

            purchBill.setDockCode(pmsDemandPurchShipperResp.getDockCode());
            purchBill.setDockName(pmsDemandPurchShipperResp.getDockName());
//            purchBill.setContactMan(pmsDemandDetailSourceRefPO.getSrcBuyerName());
//            purchBill.setContactTel(pmsDemandDetailSourceRefPO.getSrcBuyerTel());
//            purchBill.setContactAddr(pmsDemandDetailSourceRefPO.getSrcBuyerAddr());

            //purchBill.setPurchRemark(StringUtils.isNotEmpty(pmsDemandDetailSourceRefPO.getRemark()) ? pmsDemandDetailSourceRefPO.getRemark() : pmsDemandBillResp.getRemark());
            purchBill.setPurchRemark(pmsDemandDetailSourceRefPO.getRemark());

            purchBill.setRefundReason(StringUtils.isNotEmpty(pmsDemandDetailSourceRefPO.getSrcRefundReason()) ? pmsDemandDetailSourceRefPO.getSrcRefundReason() : pmsDemandBillResp.getRefundReason());
            purchBill.setRefundReasonDesc(StringUtils.isNotEmpty(pmsDemandDetailSourceRefPO.getSrcRefundReasonDesc()) ? pmsDemandDetailSourceRefPO.getSrcRefundReasonDesc() : pmsDemandBillResp.getRefundReasonDesc());

            purchBill.setContractNo(pmsDemandPurchShipperResp.getContractNo());
            purchBill.setBillSource(1);
            purchBill.setSrcBillNo(pmsDemandBillResp.getBillNo());
            purchBill.setSrcRemark(pmsDemandBillResp.getRemark());

            purchBill.setTotalSkuCount(0);//商品品项数
            purchBill.setTotalQty(BigDecimal.ZERO);//采购总数量
            purchBill.setTotalTaxMoney(BigDecimal.ZERO);//采购总金额
            purchBill.setTotalTax(BigDecimal.ZERO);//采购总税金

            purchBill.setPurchBatchNo("");//需求批次
            purchBill.setDirectSign(pmsDemandDeptGoodsDetailPO.getDirectSign());

            if(ObjectUtils.equals(PmsBillDirectionEnum.REVERSE.getCode(),purchBill.getBillDirection())){
                SupplierByCodeResp supplier = baseStoreUtil.getSupplierCache(purchBill.getSupplierCode());
                if(null != supplier){
                    purchBill.setContactMan(supplier.getLinkMan());
                    purchBill.setContactTel(supplier.getPhone());
                    purchBill.setContactAddr(supplier.getAddress());
                }
            }
            else{
                StoreDetailResp storeDetailResp = baseStoreUtil.getSingleDeptInfo(purchBill.getDeptCode());
                if(null != storeDetailResp){
                    purchBill.setContactMan(storeDetailResp.getManagerUserName());
                    purchBill.setContactTel(storeDetailResp.getServiceHotline());
                    purchBill.setContactAddr(storeDetailResp.getWholeAddress());
                }
            }


            purchOrderHandleDTO.setPurchBill(purchBill);

        }
        //采购订单明细表
        List<PmsPurchaseBillDetailPO> purchBillDetailList = purchOrderHandleDTO.getPurchBillDetailList();
        Map<String, PmsPurchaseBillDetailPO> purchaseBillDetailPOMap = purchOrderHandleDTO.getPurchaseBillDetailPOMap();

        String key = pmsDemandDeptGoodsDetailPO.getSkuCode() + "_" + pmsDemandDeptGoodsDetailPO.getGoodsType();

        PmsPurchaseBillDetailPO pmsPurchaseBillDetailPO = null;
        Long insideId = 1L;
        //如果是直流合单，判断商品明细行是否存在
        if(isMergeBill && purchaseBillDetailPOMap.containsKey(key)){
            pmsPurchaseBillDetailPO = purchaseBillDetailPOMap.get(key);
            //下面会重算金额
            pmsPurchaseBillDetailPO.setPurchQty(pmsPurchaseBillDetailPO.getPurchQty().add(pmsDemandPurchShipperResp.getPurchQty()));
        }
        else{
            pmsPurchaseBillDetailPO = new PmsPurchaseBillDetailPO();
            purchaseBillDetailPOMap.put(key,pmsPurchaseBillDetailPO);
            insideId = Long.valueOf(purchBillDetailList.size()) + 1;
            purchBillDetailList.add(pmsPurchaseBillDetailPO);

            PmsDemandGoodsDetailResp pmsDemandGoodsDetailResp = pmsDemandBillResp.getDemandGoodsDetailMap().get(pmsDemandDeptGoodsDetailPO.getPinsideId());

            pmsPurchaseBillDetailPO.setBillNo(purchBill.getBillNo());
            pmsPurchaseBillDetailPO.setDeptCode(purchBill.getDeptCode());
            pmsPurchaseBillDetailPO.setDeptName(purchBill.getDeptName());
            pmsPurchaseBillDetailPO.setSupplierCode(purchBill.getSupplierCode());
            pmsPurchaseBillDetailPO.setSupplierName(purchBill.getSupplierName());
            pmsPurchaseBillDetailPO.setBillType(purchBill.getBillType());
            pmsPurchaseBillDetailPO.setInsideId(insideId);
            pmsPurchaseBillDetailPO.setSkuType(pmsDemandDeptGoodsDetailPO.getGoodsType());
            pmsPurchaseBillDetailPO.setSkuCode(pmsDemandDeptGoodsDetailPO.getSkuCode());
            pmsPurchaseBillDetailPO.setSkuName(pmsDemandDeptGoodsDetailPO.getSkuName());
            pmsPurchaseBillDetailPO.setBarcode(pmsDemandGoodsDetailResp.getBarcode());
            pmsPurchaseBillDetailPO.setGoodsNo(pmsDemandGoodsDetailResp.getGoodsNo());
            pmsPurchaseBillDetailPO.setCategoryCode(pmsDemandGoodsDetailResp.getCategoryCode());
            pmsPurchaseBillDetailPO.setCategoryName(pmsDemandGoodsDetailResp.getCategoryName());
            pmsPurchaseBillDetailPO.setBrandCode(pmsDemandGoodsDetailResp.getBrandCode());
            pmsPurchaseBillDetailPO.setBrandName(pmsDemandGoodsDetailResp.getBrandName());
            pmsPurchaseBillDetailPO.setBasicUnit(pmsDemandGoodsDetailResp.getUnit());
            pmsPurchaseBillDetailPO.setPackageUnit(pmsDemandGoodsDetailResp.getPackageUnit());
            pmsPurchaseBillDetailPO.setSkuModel(pmsDemandGoodsDetailResp.getSkuModel());
            pmsPurchaseBillDetailPO.setSaleMode(pmsDemandGoodsDetailResp.getSaleMode());
            pmsPurchaseBillDetailPO.setInputTaxRate(pmsDemandGoodsDetailResp.getInputTaxRate());
            pmsPurchaseBillDetailPO.setOutputTaxRate(pmsDemandGoodsDetailResp.getOutputTaxRate());
            pmsPurchaseBillDetailPO.setUomAttr(pmsDemandGoodsDetailResp.getUomAttr());
            pmsPurchaseBillDetailPO.setUnitRate(pmsDemandGoodsDetailResp.getUnitRate());
            pmsPurchaseBillDetailPO.setPurchUnitRate(pmsDemandPurchShipperResp.getPurchUnitRate());
            pmsPurchaseBillDetailPO.setPromotePeriodPrice(pmsDemandPurchShipperResp.getPromotePeriodPrice());
            pmsPurchaseBillDetailPO.setPromoteActivityCode(pmsDemandPurchShipperResp.getPromoteActivityCode());
            pmsPurchaseBillDetailPO.setPromoteActivityName(pmsDemandPurchShipperResp.getPromoteActivityName());
            pmsPurchaseBillDetailPO.setContractNo(pmsDemandPurchShipperResp.getContractNo());
            pmsPurchaseBillDetailPO.setContractPrice(pmsDemandPurchShipperResp.getContractPurchPrice());
            pmsPurchaseBillDetailPO.setContractSpecialPrice(pmsDemandPurchShipperResp.getContractSpecialPrice());
            pmsPurchaseBillDetailPO.setContractMaxPrice(pmsDemandPurchShipperResp.getContractMaxPurchPrice());
            pmsPurchaseBillDetailPO.setLastPurchPrice(pmsDemandPurchShipperResp.getLastPurchPrice());
            pmsPurchaseBillDetailPO.setPurchPrice(pmsDemandPurchShipperResp.getPurchPrice());
            pmsPurchaseBillDetailPO.setStockQty(pmsDemandDeptGoodsDetailPO.getOrderStockQty());
            pmsPurchaseBillDetailPO.setAtpQty(pmsDemandDeptGoodsDetailPO.getOrderAtpQty());

            //下面会重算金额，因为取了订货申请行的数量
            pmsPurchaseBillDetailPO.setPurchQty(pmsDemandPurchShipperResp.getPurchQty());
            pmsPurchaseBillDetailPO.setWholeQty(pmsDemandPurchShipperResp.getPurchWholeQty());
            pmsPurchaseBillDetailPO.setOddQty(pmsDemandPurchShipperResp.getPurchOddQty());
            pmsPurchaseBillDetailPO.setPurchMoney(MoneyUtil.round2HalfUp(pmsDemandPurchShipperResp.getPurchMoney()));
            pmsPurchaseBillDetailPO.setPurchTax(MoneyUtil.round2HalfUp(pmsDemandPurchShipperResp.getPurchTax()));

            pmsPurchaseBillDetailPO.setSalePrice(pmsDemandDeptGoodsDetailPO.getSalePrice());
            pmsPurchaseBillDetailPO.setSaleMoney(pmsDemandDeptGoodsDetailPO.getSalePrice().multiply(pmsDemandPurchShipperResp.getPurchQty()));
//        pmsPurchaseBillDetailPO.setPeriodFlag();
//        pmsPurchaseBillDetailPO.setPeriodBarcode();
//        pmsPurchaseBillDetailPO.setExpireDate();
            pmsPurchaseBillDetailPO.setBillSource(1);
            pmsPurchaseBillDetailPO.setSrcBillType(CommonBillTypeEnum.DMD.getCode());
            pmsPurchaseBillDetailPO.setSrcBillNo(pmsDemandDeptGoodsDetailPO.getBillNo());
            pmsPurchaseBillDetailPO.setSrcInsideId(pmsDemandDeptGoodsDetailPO.getInsideId());



        }
        rerunPmsPurchaseBillDetailPO(pmsPurchaseBillDetailPO);

        purchBill.setTotalQty(purchBill.getTotalQty().add(pmsPurchaseBillDetailPO.getPurchQty()));//采购总数量
        purchBill.setTotalTaxMoney(purchBill.getTotalTaxMoney().add(pmsPurchaseBillDetailPO.getPurchMoney()));//采购总金额
        purchBill.setTotalTax(purchBill.getTotalTax().add(pmsPurchaseBillDetailPO.getPurchTax()));//采购总税金

        //采购订单明细关联
        List<PmsPruchDetailRefPO> purchBillDetailRefList = purchOrderHandleDTO.getPurchBillDetailRefList();

        PmsPruchDetailRefPO pmsPruchDetailRefPO = new PmsPruchDetailRefPO();
        pmsPruchDetailRefPO.setCreateTime(LocalDateTime.now());
        pmsPruchDetailRefPO.setUpdateTime(LocalDateTime.now());
        pmsPruchDetailRefPO.setBillNo(purchBill.getBillNo());
        pmsPruchDetailRefPO.setInsideId(insideId);
        pmsPruchDetailRefPO.setSkuCode(pmsDemandDeptGoodsDetailPO.getSkuCode());
        pmsPruchDetailRefPO.setSkuName(pmsDemandDeptGoodsDetailPO.getSkuName());
        pmsPruchDetailRefPO.setPurchQty(purchOrderQty);
        pmsPruchDetailRefPO.setBillSource(1);
        pmsPruchDetailRefPO.setSrcBillNo(pmsDemandDeptGoodsDetailPO.getBillNo());
        pmsPruchDetailRefPO.setSrcInsideId(pmsDemandDeptGoodsDetailPO.getInsideId());
        purchBillDetailRefList.add(pmsPruchDetailRefPO);

        //需求单出货方与采购配送订单关联关系
        List<PmsDemandPruchDeliveryRefPO> pruchDeliveryRefList = purchOrderHandleDTO.getPruchDeliveryRefList();

        PmsDemandPruchDeliveryRefPO pmsDemandPruchDeliveryRefPO = new PmsDemandPruchDeliveryRefPO();
        pmsDemandPruchDeliveryRefPO.setBillNo(pmsDemandDeptGoodsDetailPO.getBillNo());
        pmsDemandPruchDeliveryRefPO.setRefBillNo(purchBill.getBillNo());
        pmsDemandPruchDeliveryRefPO.setType(0);
        if(ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),pmsDemandDeptGoodsDetailPO.getDirectSign())){
            pmsDemandPruchDeliveryRefPO.setType(4);
        }
        pmsDemandPruchDeliveryRefPO.setPurchShipperInsideId(pmsDemandPurchShipperResp.getInsideId());
        pmsDemandPruchDeliveryRefPO.setDeptGoodsInsideId(pmsDemandDeptGoodsDetailPO.getInsideId());
        pmsDemandPruchDeliveryRefPO.setGoodsInsideId(pmsDemandDeptGoodsDetailPO.getPinsideId());
        pmsDemandPruchDeliveryRefPO.setQty(purchOrderQty);
        pmsDemandPruchDeliveryRefPO.setRefInsideId(insideId);

        pmsDemandPruchDeliveryRefPO.setApplyBillNo(pmsDemandDetailSourceRefPO.getApplyBillNo());
        pmsDemandPruchDeliveryRefPO.setApplyInsideId(pmsDemandDetailSourceRefPO.getSrcInsideId());
        pmsDemandPruchDeliveryRefPO.setSkuCode(pmsDemandDetailSourceRefPO.getSkuCode());

        pruchDeliveryRefList.add(pmsDemandPruchDeliveryRefPO);

//        purchOrderHandleMap.put(key,purchOrderHandleDTO);
    }

    /**
     * 重新计算采购订单主表数据
     */
    private void rerunPmsPurchaseBillDetailPO(PmsPurchaseBillDetailPO pmsPurchaseBillDetailPO){
        //重算，因为取了订货申请行的数量
        //采购税金：该行采购金额/（1+进项税率）*进项税率
        //采购金额
        BigDecimal purchMoney = pmsPurchaseBillDetailPO.getPurchQty().multiply(pmsPurchaseBillDetailPO.getPurchPrice());
        BigDecimal purchTax = purchMoney.divide(pmsPurchaseBillDetailPO.getInputTaxRate().divide(new BigDecimal("100")).add(new BigDecimal("1")),2, RoundingMode.HALF_UP).multiply(pmsPurchaseBillDetailPO.getInputTaxRate().divide(new BigDecimal("100")));
        pmsPurchaseBillDetailPO.setPurchMoney(MoneyUtil.round2HalfUp(purchMoney));
        pmsPurchaseBillDetailPO.setPurchTax(MoneyUtil.round2HalfUp(purchTax));

        BigDecimal purchUnitRate = pmsPurchaseBillDetailPO.getPurchUnitRate();
        if(null == purchUnitRate){
            purchUnitRate = BigDecimal.ONE;
        }
        BigDecimal oddQty = pmsPurchaseBillDetailPO.getPurchQty().remainder(purchUnitRate);

        pmsPurchaseBillDetailPO.setWholeQty(pmsPurchaseBillDetailPO.getPurchQty().subtract(oddQty));
        pmsPurchaseBillDetailPO.setOddQty(oddQty);
    }

    /**
     * 重新计算配送订单主表汇总数据
     * @param wdDeliveryBillDetailPO
     */
    private void rerunPmsDeliveryBillDetailPO(WdDeliveryBillDetailPO wdDeliveryBillDetailPO){
        //配送税金：按照配送金额/（1+进项税率）*进项税率
        BigDecimal deliveryMoney = wdDeliveryBillDetailPO.getDeliveryQty().multiply(wdDeliveryBillDetailPO.getDeliveryQty());
        BigDecimal deliveryTax= deliveryMoney.divide(wdDeliveryBillDetailPO.getInputTaxRate().divide(new BigDecimal("100")).add(new BigDecimal("1")),2, RoundingMode.HALF_UP).multiply(wdDeliveryBillDetailPO.getInputTaxRate().divide(new BigDecimal("100")));

        wdDeliveryBillDetailPO.setDeliveryTaxMoney(MoneyUtil.round2HalfUp(deliveryMoney));//配送金额
        wdDeliveryBillDetailPO.setDeliveryTax(MoneyUtil.round2HalfUp(deliveryTax));//配送税金

        BigDecimal orderUnitRate = wdDeliveryBillDetailPO.getOrderUnitRate();
        if(null == orderUnitRate){
            orderUnitRate = BigDecimal.ONE;
        }
        BigDecimal oddQty = wdDeliveryBillDetailPO.getDeliveryQty().remainder(orderUnitRate);

        wdDeliveryBillDetailPO.setWholeQty(wdDeliveryBillDetailPO.getDeliveryQty().subtract(oddQty));//箱数
        wdDeliveryBillDetailPO.setOddQty(oddQty);//零头数量

    }

    /**
     * 需求单落库
     * @param billConvertDataDTO
     * @param pmsDemandBillResultResp
     */
    private void saveBill(BillConvertDataDTO billConvertDataDTO,PmsDemandBillResultResp pmsDemandBillResultResp){
//        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
//        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
//        TransactionStatus transaction = dstManager.getTransaction(def);

        try {
            //主要是更新停靠点数据 及需求批次
            if(ObjectUtils.equals(0,pmsDemandBillResultResp.getPmsDemandBillResp().getBillSource())){
                //删除部门商品表
                LambdaUpdateWrapper<PmsDemandDeptGoodsDetailPO> deptGoodsDetailWrapper = new LambdaUpdateWrapper<>();
                deptGoodsDetailWrapper.eq(PmsDemandDeptGoodsDetailPO::getBillNo, pmsDemandBillResultResp.getDemandBillHandleData().getPmsDemandBillPO().getBillNo());
                pmsDemandDeptGoodsDetailRepositoryService.getPmsDemandDeptGoodsDetailMapper().delete(deptGoodsDetailWrapper);

                //删除关联表
                LambdaUpdateWrapper<PmsDemandDetailSourceRefPO> detailSourceRefWrapper = new LambdaUpdateWrapper<>();
                detailSourceRefWrapper.eq(PmsDemandDetailSourceRefPO::getDemandBillNo, pmsDemandBillResultResp.getDemandBillHandleData().getPmsDemandBillPO().getBillNo());
                pmsDemandDetailSourceRefRepositoryService.getPmsDemandDetailSourceRefMapper().delete(detailSourceRefWrapper);

                if(CollectionUtils.isNotEmpty(pmsDemandBillResultResp.getDemandBillHandleData().getDemandDeptGoodsDetailPOList())){
                    pmsDemandDeptGoodsDetailRepositoryService.saveBatch(pmsDemandBillResultResp.getDemandBillHandleData().getDemandDeptGoodsDetailPOList());
                }

                if(CollectionUtils.isNotEmpty(pmsDemandBillResultResp.getDemandBillHandleData().getDemandDetailSourceRefPOList())){
                    pmsDemandDetailSourceRefRepositoryService.saveBatch(pmsDemandBillResultResp.getDemandBillHandleData().getDemandDetailSourceRefPOList());
                }
            }


            //删除出货方-配送
            LambdaUpdateWrapper<PmsDemandDeliveryShipperPO> delivertWrapper = new LambdaUpdateWrapper<>();
            delivertWrapper.eq(PmsDemandDeliveryShipperPO::getBillNo, pmsDemandBillResultResp.getDemandBillHandleData().getPmsDemandBillPO().getBillNo());
            pmsDemandDeliveryShipperRepositoryService.getPmsDemandDeliveryShipperMapper().delete(delivertWrapper);

            //删除出货方-采购
            LambdaUpdateWrapper<PmsDemandPurchShipperPO> purchWrapper = new LambdaUpdateWrapper<>();
            purchWrapper.eq(PmsDemandPurchShipperPO::getBillNo, pmsDemandBillResultResp.getDemandBillHandleData().getPmsDemandBillPO().getBillNo());
            pmsDemandPurchShipperRepositoryService.getPmsDemandPurchShipperMapper().delete(purchWrapper);



            if(CollectionUtils.isNotEmpty(pmsDemandBillResultResp.getDemandBillHandleData().getDemandDeliveryShipperPOList())){
                pmsDemandDeliveryShipperRepositoryService.saveBatch(pmsDemandBillResultResp.getDemandBillHandleData().getDemandDeliveryShipperPOList());
            }

            if(CollectionUtils.isNotEmpty(pmsDemandBillResultResp.getDemandBillHandleData().getDemandPurchShipperPOList())){
                pmsDemandPurchShipperRepositoryService.saveBatch(pmsDemandBillResultResp.getDemandBillHandleData().getDemandPurchShipperPOList());
            }

            if(billConvertDataDTO.getPurchBillList().size() > 0 && billConvertDataDTO.getPurchBillDetailList().size() > 0){
                pmsPurchaseOrderRepositoryService.saveBatch(billConvertDataDTO.getPurchBillList());
                pmsPurchaseDetailRepositoryService.saveBatch(billConvertDataDTO.getPurchBillDetailList());
                pmsPruchDetailRefRepositoryService.saveBatch(billConvertDataDTO.getPurchBillDetailRefList());
            }

            //配转采最近供应商
            if(billConvertDataDTO.getDist2purchSupplierRecordList().size() > 0){
                List<PmsDist2purchSupplierRecordPO> pmsDist2purchSupplierRecordPOS = pmsDist2purchSupplierRecordRepositoryService.getPmsDist2purchSupplierRecordMapper().listDist2PurchSupplier(billConvertDataDTO.getDist2purchSupplierRecordList());
                Map<String, PmsDist2purchSupplierRecordPO> pmsDist2purchSupplierRecordMap = pmsDist2purchSupplierRecordPOS.stream().collect(Collectors.toMap(PmsDist2purchSupplierRecordPO::getUniKey, Function.identity()));

                List<PmsDist2purchSupplierRecordPO> dist2purchSupplierRecordList = new ArrayList<>();

                for (PmsDist2purchSupplierRecordPO pmsDist2purchSupplierRecordPO : billConvertDataDTO.getDist2purchSupplierRecordList()) {
                    if(!pmsDist2purchSupplierRecordMap.containsKey(pmsDist2purchSupplierRecordPO.getUniKey())){
                        dist2purchSupplierRecordList.add(pmsDist2purchSupplierRecordPO);
                    }
                }

                if(CollectionUtils.isNotEmpty(dist2purchSupplierRecordList)){
                    //配送部门、停靠点、商品 取消最近供应商的标记
                    pmsDist2purchSupplierRecordRepositoryService.getPmsDist2purchSupplierRecordMapper().clearLastSign(billConvertDataDTO.getDist2purchSupplierRecordList());
                    pmsDist2purchSupplierRecordRepositoryService.saveBatch(dist2purchSupplierRecordList);
                    //配送部门、停靠点、商品、供应商 增加最近供应商的标记
                    pmsDist2purchSupplierRecordRepositoryService.getPmsDist2purchSupplierRecordMapper().modifyLastSign(billConvertDataDTO.getDist2purchSupplierRecordList());

                }
            }

            if(billConvertDataDTO.getDeliveryBillList().size() > 0 && billConvertDataDTO.getDeliveryBillDetailList().size() > 0){
                wdDeliveryBillRepository.saveBatch(billConvertDataDTO.getDeliveryBillList());
                wdDeliveryBillDetailRepository.saveBatch(billConvertDataDTO.getDeliveryBillDetailList());
            }

            if(billConvertDataDTO.getPruchDeliveryRefList().size() > 0){
                pmsDemandPruchDeliveryRefRepositoryService.saveBatch(billConvertDataDTO.getPruchDeliveryRefList());
            }

            //主派生成订货申请单
            if(billConvertDataDTO.getApplyBillList().size() > 0){
                pmsApplyBillRepositoryService.saveBatch(billConvertDataDTO.getApplyBillList());
                pmsApplyBillDetailRepositoryService.saveBatch(billConvertDataDTO.getApplyBillDetailList());
            }

            //回写订货申请明细 配送订单 采购订单
            updateApplyDetailBillRef(billConvertDataDTO);

            //更新状态
            PmsDemandBillPO pmsDemandBillPO = new PmsDemandBillPO();
            pmsDemandBillPO.setBillNo(pmsDemandBillResultResp.getDemandBillHandleData().getPmsDemandBillPO().getBillNo());
            LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();
            pmsDemandBillPO.setSubmitUid(0L);
            pmsDemandBillPO.setSubmitCode("");
            pmsDemandBillPO.setSubmitName("");
            if(null != loginUser){
                pmsDemandBillPO.setSubmitUid(loginUser.getUid());
                pmsDemandBillPO.setSubmitCode(loginUser.getCode());
                pmsDemandBillPO.setSubmitName(loginUser.getName());
            }

            pmsDemandBillPO.setStatus(2);
            pmsDemandBillRepositoryService.getPmsDemandBillMapper().updateBillStatus(pmsDemandBillPO);

//            dstManager.commit(transaction);
        } catch (BizException e) {
            Logs.error("PmsWorkflowManagerServiceImpl.saveBill.bizException", e);
//            dstManager.rollback(transaction);
            BizExceptions.throwWithErrorCode(e.getError());
        } catch (Exception e) {
            Logs.error("PmsWorkflowManagerServiceImpl.saveBill.error", e);
//            dstManager.rollback(transaction);
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B010);
        }
    }


    /**
     * 回写订货申请明细 配送订单 采购订单
     * @param billConvertDataDTO
     */
    private void updateApplyDetailBillRef(BillConvertDataDTO billConvertDataDTO) {
        Logs.info("PmsDemandDomainServiceImpl.updateApplyDetailBillRef.billConvertDataDTO.getPruchDeliveryRefList." + JSON.toJSONString(billConvertDataDTO.getPruchDeliveryRefList()));
        Map<String, PmsApplyBillDetailPO> applyBillDetailPOMap = new HashMap<>();
        for (PmsDemandPruchDeliveryRefPO pmsDemandPruchDeliveryRefPO : billConvertDataDTO.getPruchDeliveryRefList()) {
            if(ObjectUtils.equals(0,pmsDemandPruchDeliveryRefPO.getType()) || ObjectUtils.equals(1,pmsDemandPruchDeliveryRefPO.getType()) ){
                String key = pmsDemandPruchDeliveryRefPO.getApplyBillNo() + "_" + pmsDemandPruchDeliveryRefPO.getApplyInsideId();

                PmsApplyBillDetailPO pmsApplyBillDetailPO;
                if(applyBillDetailPOMap.containsKey(key)){
                    pmsApplyBillDetailPO = applyBillDetailPOMap.get(key);
                }else{
                    pmsApplyBillDetailPO = new PmsApplyBillDetailPO();
                    pmsApplyBillDetailPO.setBillNo(pmsDemandPruchDeliveryRefPO.getApplyBillNo());
                    pmsApplyBillDetailPO.setInsideId(pmsDemandPruchDeliveryRefPO.getApplyInsideId());
                    applyBillDetailPOMap.put(key,pmsApplyBillDetailPO);
                }

                if(ObjectUtils.equals(0,pmsDemandPruchDeliveryRefPO.getType())){
                    if(StringUtils.isNotEmpty(pmsApplyBillDetailPO.getPurchBillNo())){
                        pmsApplyBillDetailPO.setPurchBillNo(pmsApplyBillDetailPO.getPurchBillNo() + "," + pmsDemandPruchDeliveryRefPO.getRefBillNo());
                    }
                    else{
                        pmsApplyBillDetailPO.setPurchBillNo( pmsDemandPruchDeliveryRefPO.getRefBillNo());
                    }
                }else{
                    if(StringUtils.isNotEmpty(pmsApplyBillDetailPO.getPurchBillNo())){
                        pmsApplyBillDetailPO.setDeliveryBillNo(pmsApplyBillDetailPO.getDeliveryBillNo() + "," + pmsDemandPruchDeliveryRefPO.getRefBillNo());
                    }
                    else{
                        pmsApplyBillDetailPO.setDeliveryBillNo(pmsDemandPruchDeliveryRefPO.getRefBillNo());
                    }
                }
            }

        }
        ArrayList<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = new ArrayList<>(applyBillDetailPOMap.values());
        Logs.info("PmsDemandDomainServiceImpl.updateApplyDetailBillRef.pmsApplyBillDetailPOS." + JSON.toJSONString(pmsApplyBillDetailPOS));
        if(CollectionUtils.isNotEmpty(pmsApplyBillDetailPOS)){
            pmsApplyBillDetailRepositoryService.updatePurchAndDeliveryBatch(pmsApplyBillDetailPOS);
        }

    }

    /**
     * 作废需求单
     * @param param
     */
    @Override
    public void cancel(CancelDemandBillReq param) {
        lockDemandBill(param.getBillNo());

        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transaction = dstManager.getTransaction(def);

        try{

            LambdaQueryWrapper<PmsDemandBillPO> demandBillWrapper = new LambdaQueryWrapper<PmsDemandBillPO>()
                    .eq(PmsDemandBillPO::getBillNo, param.getBillNo());
            //需求单主表
            PmsDemandBillPO pmsDemandBill = pmsDemandBillRepositoryService.getOne(demandBillWrapper);

            if(null == pmsDemandBill){
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B001);
            }

            if(ObjectUtils.notEqual(1,pmsDemandBill.getStatus())){
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B011);
            }

            //作废类型,1作废需求单及原订货/退货申请行,2只作废需求单，原订货/退货申请行还原为待提单
            if(ObjectUtils.equals(2,param.getCancelType())){

                LambdaQueryWrapper<PmsApplyBillDetailPO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(PmsApplyBillDetailPO::getDemandBillNo, param.getBillNo());
                List<PmsApplyBillDetailPO> applyBillDetaillist = pmsApplyBillDetailRepositoryService.list(queryWrapper);

                PmsApplyBillDetailPO pmsApplyBillDetailPO = new PmsApplyBillDetailPO();
                pmsApplyBillDetailPO.setStatus(1);

                LambdaUpdateWrapper<PmsApplyBillDetailPO> applyBillDetailWrapper = new LambdaUpdateWrapper<>();
                applyBillDetailWrapper.eq(PmsApplyBillDetailPO::getDemandBillNo,param.getBillNo());

                pmsApplyBillDetailRepositoryService.getBaseMapper().update(pmsApplyBillDetailPO,applyBillDetailWrapper);

                if(CollectionUtils.isNotEmpty(applyBillDetaillist)){
                    pmsOrderApplyDomainService.syncApplyDetailState(applyBillDetaillist);
                }
            }
            else{

                LambdaUpdateWrapper<PmsDemandDetailSourceRefPO> detailSourceRefWrapper = new LambdaUpdateWrapper<>();
                detailSourceRefWrapper.eq(PmsDemandDetailSourceRefPO::getDemandBillNo,param.getBillNo());
                detailSourceRefWrapper.eq(PmsDemandDetailSourceRefPO::getType,  1);
                List<PmsDemandDetailSourceRefPO> pmsDemandDetailSourceRefPOS = pmsDemandDetailSourceRefRepositoryService.getPmsDemandDetailSourceRefMapper().selectList(detailSourceRefWrapper);

                List<CancelApplyDetail4DemandDTO> cancelApplyBillDTOs = new ArrayList<>();
                for (PmsDemandDetailSourceRefPO pmsDemandDetailSourceRefPO : pmsDemandDetailSourceRefPOS) {
                    CancelApplyDetail4DemandDTO cancelApplyBillDTO = new CancelApplyDetail4DemandDTO();
                    cancelApplyBillDTO.setBillNo(pmsDemandDetailSourceRefPO.getApplyBillNo());
                    cancelApplyBillDTO.setInsideId(pmsDemandDetailSourceRefPO.getSrcInsideId());

                    cancelApplyBillDTOs.add(cancelApplyBillDTO);
                }

                if(CollectionUtils.isNotEmpty(pmsDemandDetailSourceRefPOS)){
                    Result<ApplyBillResp> applyBillRespResult = getPmsOrderApplyApplicationService().cancelOrderApply4Demand(cancelApplyBillDTOs);
                    if(!"0".equals(applyBillRespResult.getCode())){
                        BizExceptions.throwWithCodeAndMsg(applyBillRespResult.getCode(),applyBillRespResult.getMsg());
                    }
                }

            }
            PmsDemandBillPO pmsDemandBillPO = new PmsDemandBillPO();
            pmsDemandBillPO.setStatus(3);
            pmsDemandBillPO.setCancelType(param.getCancelType());
            LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();
            if(null != loginUser){
                pmsDemandBillPO.setCancelCode(loginUser.getCode());
                pmsDemandBillPO.setCancelName(loginUser.getName());
                pmsDemandBillPO.setCancelUid(loginUser.getUid());
                pmsDemandBillPO.setCancelTime(LocalDateTime.now());
            }

            LambdaUpdateWrapper<PmsDemandBillPO> billWrapper = new LambdaUpdateWrapper<>();
            billWrapper.eq(PmsDemandBillPO::getBillNo,param.getBillNo());

            pmsDemandBillRepositoryService.getBaseMapper().update(pmsDemandBillPO,billWrapper);

            dstManager.commit(transaction);
        }catch (BizException e){
            dstManager.rollback(transaction);
            e.printStackTrace();
            Logs.error("PmsDemandDomainServiceImpl.cancel.BizException", e);
            BizExceptions.throwWithErrorCode(e.getError());
        }catch (Exception e){
            dstManager.rollback(transaction);
            e.printStackTrace();
            Logs.error("PmsDemandDomainServiceImpl.cancel.Exception", e);
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B009);
        } finally {
            unLockDemandBill(param.getBillNo());
        }

    }

    /**
     * 按照需求单状态分组查询数量
     * @param queryDemandBillListReq
     * @return
     */
    @Override
    public PmsDemandGroupQtyResp queryGroupQty(QueryDemandBillListReq queryDemandBillListReq) {
        List<CountGroupDemandBillResp> countGroupDemandBillResps = pmsDemandBillRepositoryService.getPmsDemandBillMapper().countGroupDemandBill(queryDemandBillListReq);
        PmsDemandGroupQtyResp pmsDemandGroupQtyResp = new PmsDemandGroupQtyResp();

        for (CountGroupDemandBillResp countGroupDemandBillResp : countGroupDemandBillResps) {
            if(ObjectUtils.equals(1, countGroupDemandBillResp.getStatus())){
                pmsDemandGroupQtyResp.setStatus1Qty(countGroupDemandBillResp.getQty());
            }else if(ObjectUtils.equals(2, countGroupDemandBillResp.getStatus())){
                pmsDemandGroupQtyResp.setStatus2Qty(countGroupDemandBillResp.getQty());
            }else if(ObjectUtils.equals(3, countGroupDemandBillResp.getStatus())){
                pmsDemandGroupQtyResp.setStatus3Qty(countGroupDemandBillResp.getQty());
            }
        }

        return pmsDemandGroupQtyResp;
    }

    /**
     * 需求单暂存时DTO转换成po
     * @param pmsDemandBillReq
     * @return
     */
    private DemandBillHandleDataDTO handleData(PmsDemandBillReq pmsDemandBillReq) {
        DemandBillHandleDataDTO demandBillHandleDataDTO = new DemandBillHandleDataDTO();

        List<PmsDemandGoodsDetailPO> demandGoodsDetailPOList = new ArrayList<>();
        List<PmsDemandDeptGoodsDetailPO> demandDeptGoodsDetailPOList = new ArrayList<>();
        List<PmsDemandDetailSourceRefPO> demandDetailSourceRefPOList = new ArrayList<>();
        List<PmsDemandDeliveryShipperPO> demandDeliveryShipperPOList = new ArrayList<>();
        List<PmsDemandPurchShipperPO> demandPurchShipperPOList = new ArrayList<>();
        List<PmsDemandDeliveryToPurchRefPO> deliveryToPurchRefPOList = new ArrayList<>();
        List<PmsDemandDeliveryToPurchPO> deliveryToPurchPOList = new ArrayList<>();


        demandBillHandleDataDTO.setDemandGoodsDetailPOList(demandGoodsDetailPOList);
        demandBillHandleDataDTO.setDemandDeptGoodsDetailPOList(demandDeptGoodsDetailPOList);
        demandBillHandleDataDTO.setDemandDetailSourceRefPOList(demandDetailSourceRefPOList);
        demandBillHandleDataDTO.setDemandDeliveryShipperPOList(demandDeliveryShipperPOList);
        demandBillHandleDataDTO.setDemandPurchShipperPOList(demandPurchShipperPOList);
        demandBillHandleDataDTO.setDeliveryToPurchRefPOList(deliveryToPurchRefPOList);
        demandBillHandleDataDTO.setDeliveryToPurchPOList(deliveryToPurchPOList);

        //商品明细
        for (PmsDemandGoodsDetailReq pmsDemandGoodsDetailReq : pmsDemandBillReq.getDemandGoodsDetailList()) {
            pmsDemandGoodsDetailReq.setBillNo(pmsDemandBillReq.getBillNo());
            //商品部门数据
            for (PmsDemandDeptGoodsDetailReq pmsDemandDeptGoodsDetailReq : pmsDemandGoodsDetailReq.getDemandDeptGoodsDetailList()) {
                pmsDemandDeptGoodsDetailReq.setPmsDemandGoodsDetailReq(pmsDemandGoodsDetailReq);
                pmsDemandDeptGoodsDetailReq.setBillNo(pmsDemandBillReq.getBillNo());
                pmsDemandDeptGoodsDetailReq.setGoodsType(pmsDemandGoodsDetailReq.getGoodsType());
                pmsDemandDeptGoodsDetailReq.setSkuCode(pmsDemandGoodsDetailReq.getSkuCode());
                pmsDemandDeptGoodsDetailReq.setSkuName(pmsDemandGoodsDetailReq.getSkuName());
                //关联表
                if(CollectionUtils.isNotEmpty(pmsDemandDeptGoodsDetailReq.getDemandDetailSourceList())){
                    for (PmsDemandDetailSourceRefReq pmsDemandDetailSourceRefReq : pmsDemandDeptGoodsDetailReq.getDemandDetailSourceList()) {
                        pmsDemandDetailSourceRefReq.setDemandBillNo(pmsDemandBillReq.getBillNo());
                        pmsDemandDetailSourceRefReq.setOrderDeptCode(pmsDemandDeptGoodsDetailReq.getOrderDeptCode());
                        pmsDemandDetailSourceRefReq.setOrderDeptName(pmsDemandDeptGoodsDetailReq.getOrderDeptName());
                        pmsDemandDetailSourceRefReq.setGoodsType(pmsDemandGoodsDetailReq.getGoodsType());
                        pmsDemandDetailSourceRefReq.setSkuCode(pmsDemandGoodsDetailReq.getSkuCode());
                        pmsDemandDetailSourceRefReq.setSkuName(pmsDemandGoodsDetailReq.getSkuName());
                        pmsDemandDetailSourceRefReq.setGoodsType(pmsDemandGoodsDetailReq.getGoodsType());

                        if(ObjectUtils.equals(PmsDemanBillOpTypeEnum.STAGING_SUMIT.getCode(), pmsDemandBillReq.getOpType())){
                            if(ObjectUtils.equals(1,pmsDemandDetailSourceRefReq.getStatus())){
                                if(null == pmsDemandDetailSourceRefReq.getResponseQty() || ObjectUtils.equals(BigDecimal.ZERO,pmsDemandDetailSourceRefReq.getResponseQty())){
                                    String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B037.getErrorMsg(), new String[]{pmsDemandDeptGoodsDetailReq.getOrderDeptCode(), pmsDemandDeptGoodsDetailReq.getSkuCode()}).getMessage();
                                    BizExceptions.throwWithCodeAndMsg(PmsErrorCodeEnum.SC_PMS_002_B037.getCode(), message);
                                }
                            }
                        }
                    }

                    List<PmsDemandDetailSourceRefPO> detailResourceRefCopy = CglibCopier.copy(pmsDemandDeptGoodsDetailReq.getDemandDetailSourceList(), PmsDemandDetailSourceRefPO.class);
                    demandDetailSourceRefPOList.addAll(detailResourceRefCopy);
                }

                //默认为部门商品名称
                String orderDeptCode = pmsDemandDeptGoodsDetailReq.getOrderDeptCode();
                //配送
                if(CollectionUtils.isNotEmpty(pmsDemandDeptGoodsDetailReq.getDemandDeliveryShipperList())){
                    for (PmsDemandDeliveryShipperReq pmsDemandDeliveryShipperReq : pmsDemandDeptGoodsDetailReq.getDemandDeliveryShipperList()) {
                        pmsDemandDeliveryShipperReq.setBillNo(pmsDemandBillReq.getBillNo());
                    }

                    List<PmsDemandDeliveryShipperPO> deliveryShipperCopy = CglibCopier.copy(pmsDemandDeptGoodsDetailReq.getDemandDeliveryShipperList(), PmsDemandDeliveryShipperPO.class);
                    for (PmsDemandDeliveryShipperPO pmsDemandDeliveryShipperPO : deliveryShipperCopy) {
                        pmsDemandDeliveryShipperPO.setPmsDemandDeptGoodsDetailReq(pmsDemandDeptGoodsDetailReq);
                        pmsDemandDeliveryShipperPO.setOrderDeptCode(pmsDemandDeptGoodsDetailReq.getOrderDeptCode());
                        pmsDemandDeliveryShipperPO.setGoodsType(pmsDemandGoodsDetailReq.getGoodsType());
                        pmsDemandDeliveryShipperPO.setSkuCode(pmsDemandGoodsDetailReq.getSkuCode());
                        pmsDemandDeliveryShipperPO.setSkuName(pmsDemandGoodsDetailReq.getSkuName());
                        if(ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),pmsDemandDeliveryShipperPO.getDirectSign())
                            && ObjectUtils.equals(1,pmsDemandDeliveryShipperPO.getStatus())){
                            orderDeptCode = pmsDemandDeliveryShipperPO.getDistDeptCode();
                        }

                        if(ObjectUtils.equals(PmsDemanBillOpTypeEnum.STAGING_SUMIT.getCode(), pmsDemandBillReq.getOpType())){
                            if(ObjectUtils.equals(0,pmsDemandDeptGoodsDetailReq.getGoodsType())){
                                if(ObjectUtils.equals(1,pmsDemandDeliveryShipperPO.getStatus())){
                                    if(null == pmsDemandDeliveryShipperPO.getDistMoney() || ObjectUtils.equals(BigDecimal.ZERO,pmsDemandDeliveryShipperPO.getDistMoney())){
                                        String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B038.getErrorMsg(), new String[]{pmsDemandDeptGoodsDetailReq.getOrderDeptCode(), pmsDemandDeptGoodsDetailReq.getSkuCode()}).getMessage();
                                        BizExceptions.throwWithCodeAndMsg(PmsErrorCodeEnum.SC_PMS_002_B038.getCode(), message);
                                    }
                                }
                            }
                            else{
                                if(ObjectUtils.equals(1,pmsDemandDeliveryShipperPO.getStatus())){
                                    if(null == pmsDemandDeliveryShipperPO.getDistMoney()){
                                        String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B043.getErrorMsg(), new String[]{pmsDemandDeptGoodsDetailReq.getOrderDeptCode(), pmsDemandDeptGoodsDetailReq.getSkuCode()}).getMessage();
                                        BizExceptions.throwWithCodeAndMsg(PmsErrorCodeEnum.SC_PMS_002_B043.getCode(), message);
                                    }
                                }
                            }
                        }
                    }
                    demandDeliveryShipperPOList.addAll(deliveryShipperCopy);
                }

                //采购
                if(CollectionUtils.isNotEmpty(pmsDemandDeptGoodsDetailReq.getDemandPurchShipperList())){
                    for (PmsDemandPurchShipperReq pmsDemandPurchShipperReq : pmsDemandDeptGoodsDetailReq.getDemandPurchShipperList()) {
                        pmsDemandPurchShipperReq.setBillNo(pmsDemandBillReq.getBillNo());
                        pmsDemandPurchShipperReq.setOrderDeptCode(orderDeptCode);
                        pmsDemandPurchShipperReq.setGoodsType(pmsDemandGoodsDetailReq.getGoodsType());
                        pmsDemandPurchShipperReq.setSkuCode(pmsDemandGoodsDetailReq.getSkuCode());
                        pmsDemandPurchShipperReq.setSkuName(pmsDemandGoodsDetailReq.getSkuName());

                        if(pmsDemandPurchShipperReq.getSupplierCode().equals(pmsDemandPurchShipperReq.getLastSupplierCode())
                                && ObjectUtils.equals(0,pmsDemandPurchShipperReq.getLastSupplierSign())){
                            pmsDemandPurchShipperReq.setLastSupplierSign(1);
                        }
                    }
                    List<PmsDemandPurchShipperPO> purchShipperCopy = CglibCopier.copy(pmsDemandDeptGoodsDetailReq.getDemandPurchShipperList(), PmsDemandPurchShipperPO.class);
                    demandPurchShipperPOList.addAll(purchShipperCopy);
                }
                if(ObjectUtils.notEqual(1,pmsDemandDeptGoodsDetailReq.getStatus())){
                    continue;
                }

                if(CollectionUtils.isEmpty(pmsDemandDeptGoodsDetailReq.getDemandDeliveryShipperList()) && CollectionUtils.isEmpty(pmsDemandDeptGoodsDetailReq.getDemandPurchShipperList())){
                    String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B041.getErrorMsg(), new String[]{pmsDemandDeptGoodsDetailReq.getOrderDeptCode(), pmsDemandDeptGoodsDetailReq.getSkuCode()}).getMessage();
                    BizExceptions.throwWithCodeAndMsg(PmsErrorCodeEnum.SC_PMS_002_B041.getCode(), message);
                }

                List<PmsDemandDeliveryShipperReq> deliveryShipperLisst = pmsDemandDeptGoodsDetailReq.getDemandDeliveryShipperList().stream().filter(e -> ObjectUtils.equals(1, e.getStatus())).collect(Collectors.toList());
                List<PmsDemandPurchShipperReq> deliveryShipper = pmsDemandDeptGoodsDetailReq.getDemandPurchShipperList().stream().filter(e -> ObjectUtils.equals(1, e.getStatus())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(deliveryShipperLisst) && CollectionUtils.isEmpty(deliveryShipper)){
                    String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B042.getErrorMsg(), new String[]{pmsDemandDeptGoodsDetailReq.getOrderDeptCode(), pmsDemandDeptGoodsDetailReq.getSkuCode()}).getMessage();
                    BizExceptions.throwWithCodeAndMsg(PmsErrorCodeEnum.SC_PMS_002_B042.getCode(), message);
                }

                //出货方校验

                //普通采购：出货方-配送0，出货方-采购多个
                //出货途径-采购
//                if(ShippingWayEnum.PURCHASE.getCode().equals(pmsDemandDeptGoodsDetailReq.getShippingWay())){
//                    if(CollectionUtils.isEmpty(pmsDemandDeptGoodsDetailReq.getDemandPurchShipperList())
//                            || 0 == pmsDemandDeptGoodsDetailReq.getDemandPurchShipperList().size()){
//                        BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B013, new String[]{pmsDemandDeptGoodsDetailReq.getOrderDeptCode(),pmsDemandDeptGoodsDetailReq.getSkuCode()});
//                    }
//                    if(CollectionUtils.isNotEmpty(pmsDemandDeptGoodsDetailReq.getDemandDeliveryShipperList())
//                        && pmsDemandDeptGoodsDetailReq.getDemandDeliveryShipperList().size() > 0){
//                        BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B014, kpmsDemandDeptGoodsDetailReq.getOrderDeptCode(),pmsDemandDeptGoodsDetailReq.getSkuCode()});
//                    }
//                }
                //出货途径-配送
//                else{
//                    //普通配送： 出货方-配送1个
//                    //普通配送-直流：出货方-配送1个，出货方-采购1个
//                    //普通配送-配转采：出货方-配送1个，出货方-采购多个
//
//                    List<PmsDemandDeliveryShipperReq> filterDelivery = pmsDemandDeptGoodsDetailReq.getDemandDeliveryShipperList().parallelStream().filter(t -> ObjectUtils.equals(1, t.getStatus()))
//                            .collect(Collectors.toList());
//                    if(CollectionUtils.isEmpty(pmsDemandDeptGoodsDetailReq.getDemandDeliveryShipperList())
//                            || 0 == filterDelivery.size()){
//                        BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B015, new String[]{pmsDemandDeptGoodsDetailReq.getOrderDeptCode(),pmsDemandDeptGoodsDetailReq.getSkuCode()});
//                    }
//
//                    if(1 != filterDelivery.size()){
//                        BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B016, new String[]{pmsDemandDeptGoodsDetailReq.getOrderDeptCode(),pmsDemandDeptGoodsDetailReq.getSkuCode()});
//                    }
//
//                    if(ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),pmsDemandDeptGoodsDetailReq.getDirectSign())){
//                        if(CollectionUtils.isEmpty(pmsDemandDeptGoodsDetailReq.getDemandPurchShipperList())
//                                || CollectionUtils.isEmpty(filterDelivery)){
//                            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B017, new String[]{pmsDemandDeptGoodsDetailReq.getOrderDeptCode(),pmsDemandDeptGoodsDetailReq.getSkuCode()});
//                        }
//
//                        if(1 != filterDelivery.size()
//                                && 1 != pmsDemandDeptGoodsDetailReq.getDemandPurchShipperList().size()){
//                            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B017, new String[]{pmsDemandDeptGoodsDetailReq.getOrderDeptCode(),pmsDemandDeptGoodsDetailReq.getSkuCode()});
//                        }
//
//                    }
//
//                    if(ObjectUtils.equals(DemandConvertlEnum.DELIVERY_TO_PURCH.getCode(),pmsDemandDeptGoodsDetailReq.getConvertFlag())){
//                        if(CollectionUtils.isEmpty(pmsDemandDeptGoodsDetailReq.getDemandPurchShipperList())
//                                || CollectionUtils.isEmpty(filterDelivery)){
//                            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B018, new String[]{pmsDemandDeptGoodsDetailReq.getOrderDeptCode(),pmsDemandDeptGoodsDetailReq.getSkuCode()});
//                        }
//
//                        if(1 != filterDelivery.size()
//                                && pmsDemandDeptGoodsDetailReq.getDemandPurchShipperList().size() < 1){
//                            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B018, new String[]{pmsDemandDeptGoodsDetailReq.getOrderDeptCode(),pmsDemandDeptGoodsDetailReq.getSkuCode()});
//                        }
//                    }
//                }
            }

            //部门商品
            List<PmsDemandDeptGoodsDetailPO> deptGoodsDetailCopy = CglibCopier.copy(pmsDemandGoodsDetailReq.getDemandDeptGoodsDetailList(), PmsDemandDeptGoodsDetailPO.class);
            demandDeptGoodsDetailPOList.addAll(deptGoodsDetailCopy);

        }

        //商品
        List<PmsDemandGoodsDetailPO> goodsDetailCopy = CglibCopier.copy(pmsDemandBillReq.getDemandGoodsDetailList(), PmsDemandGoodsDetailPO.class);
        demandGoodsDetailPOList.addAll(goodsDetailCopy);

        //配送转采信息
        for (PmsDemandDeliveryToPurchReq pmsDemandDeliveryToPurchReq : pmsDemandBillReq.getDeliveryToPurchParamDTO()) {
            pmsDemandDeliveryToPurchReq.setBillNo(pmsDemandBillReq.getBillNo());

            for (PmsDemandPurchShipperReq pmsDemandPurchShipperReq : pmsDemandDeliveryToPurchReq.getPurchShipperList()) {
                pmsDemandPurchShipperReq.setOrderDeptCode(pmsDemandDeliveryToPurchReq.getDistDeptCode());
                pmsDemandPurchShipperReq.setSkuName(pmsDemandDeliveryToPurchReq.getSkuName());
                pmsDemandPurchShipperReq.setBillNo(pmsDemandBillReq.getBillNo());
            }

            List<PmsDemandPurchShipperPO> purchShipperList = CglibCopier.copy(pmsDemandDeliveryToPurchReq.getPurchShipperList(), PmsDemandPurchShipperPO.class);

            for (PmsDemandDeliveryToPurchRefReq pmsDemandDeliveryToPurchRefReq : pmsDemandDeliveryToPurchReq.getDeliveryToPurchRefList()) {
                pmsDemandDeliveryToPurchRefReq.setBillNo(pmsDemandBillReq.getBillNo());
            }
            List<PmsDemandDeliveryToPurchRefPO> deliveryToPurchRefList = CglibCopier.copy(pmsDemandDeliveryToPurchReq.getDeliveryToPurchRefList(), PmsDemandDeliveryToPurchRefPO.class);

            demandPurchShipperPOList.addAll(purchShipperList);
            deliveryToPurchRefPOList.addAll(deliveryToPurchRefList);
        }

        List<PmsDemandDeliveryToPurchPO> copy = CglibCopier.copy(pmsDemandBillReq.getDeliveryToPurchParamDTO(), PmsDemandDeliveryToPurchPO.class);
        deliveryToPurchPOList.addAll(copy);

        return demandBillHandleDataDTO;
    }

    /**
     * 订货申请审批后自动生成需求单
     * @param applyBillNoList
     */
    @Override
    public void auditApplyToDemand(List<String> applyBillNoList){
        Logs.info("PmsDemandDomainServiceImpl.auditApplyToDemand.req:" + JSON.toJSONString(applyBillNoList));
        //组装自动生成需求单参数
        AutoApplyToDemandReq autoApplyToDemandReq = new AutoApplyToDemandReq();
        List<PmsApplyBillPO> applyBillPOList = pmsApplyBillRepositoryService.getListByBillNos(applyBillNoList);

        Logs.info("PmsDemandDomainServiceImpl.auditApplyToDemand.applyBillPOList:" + JSON.toJSONString(applyBillPOList));

        Map<String, PmsApplyBillPO> collect = applyBillPOList.stream().collect(Collectors.toMap(PmsApplyBillPO::getBillNo, Function.identity()));

        List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = pmsApplyBillDetailRepositoryService.listNullPurchBatchNo4Demand(applyBillNoList);

        Logs.info("PmsDemandDomainServiceImpl.auditApplyToDemand.pmsApplyBillDetailPOS:" + JSON.toJSONString(pmsApplyBillDetailPOS));

        List<PmsApplyBillPO> applyBillPOResultList = new ArrayList<>();
        for (PmsApplyBillDetailPO pmsApplyBillDetailPO : pmsApplyBillDetailPOS) {
            if(collect.containsKey(pmsApplyBillDetailPO.getBillNo())){
                applyBillPOResultList.add(collect.get(pmsApplyBillDetailPO.getBillNo()));
            }
        }

        autoApplyToDemandReq.getApplyBillDetailList().addAll(pmsApplyBillDetailPOS);
        autoApplyToDemandReq.getApplyBillList().addAll(applyBillPOResultList);

        if(CollectionUtils.isEmpty(pmsApplyBillDetailPOS) || CollectionUtils.isEmpty(applyBillPOResultList)){
            Logs.info("PmsDemandDomainServiceImpl.auditApplyToDemand.订货申请或者订货申请明细为空，不自动生成需求单");
        }

        autoApplyToDemand(autoApplyToDemandReq);
    }

    /**
     * 根据批次自动生成需求单
     * @param taskApplyToDemandReq
     */
    @Override
    public void taskApplyToDemand(TaskApplyToDemandReq taskApplyToDemandReq){
        Logs.info("PmsDemandDomainServiceImpl.taskApplyToDemand.req:" + JSON.toJSONString(taskApplyToDemandReq));

        //已经查询了待提单的订货申请明细行
        PmsApplyBillDetailPO pmsApplyBillDetailPO = new PmsApplyBillDetailPO();
        pmsApplyBillDetailPO.setPurchBatchNo(taskApplyToDemandReq.getPurchBatchNo());
        pmsApplyBillDetailPO.setShipperCode(taskApplyToDemandReq.getDeptCode());
        List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = pmsApplyBillDetailRepositoryService.list4AutoDemand(pmsApplyBillDetailPO);

        Logs.info("PmsDemandDomainServiceImpl.taskApplyToDemand.pmsApplyBillDetailPOS:" + JSON.toJSONString(pmsApplyBillDetailPOS));
        if(CollectionUtils.isEmpty(pmsApplyBillDetailPOS)){
            return;
        }
        Set<String> applyBillNoSet = new HashSet<>();
        for (PmsApplyBillDetailPO applyBillDetailPO : pmsApplyBillDetailPOS) {
            applyBillNoSet.add(applyBillDetailPO.getBillNo());
        }

        Map<String, List<PmsApplyBillDetailPO>> pmsApplyBillDetailPOMap = pmsApplyBillDetailPOS.parallelStream().collect(Collectors.groupingBy(PmsApplyBillDetailPO::getBillNo));

        AutoApplyToDemandReq autoApplyToDemandReq = new AutoApplyToDemandReq();
        List<PmsApplyBillPO> applyBillPOList = pmsApplyBillRepositoryService.getListByBillNos(new ArrayList<>(applyBillNoSet));
        for (PmsApplyBillPO pmsApplyBillPO : applyBillPOList) {
            if(pmsApplyBillDetailPOMap.containsKey(pmsApplyBillPO.getBillNo())){
                autoApplyToDemandReq.getApplyBillList().add(pmsApplyBillPO);
                autoApplyToDemandReq.getApplyBillDetailList().addAll(pmsApplyBillDetailPOMap.get(pmsApplyBillPO.getBillNo()));
            }
        }
        Logs.info("PmsDemandDomainServiceImpl.taskApplyToDemand.autoApplyToDemandReq:" + JSON.toJSONString(autoApplyToDemandReq));

        if(CollectionUtils.isEmpty(autoApplyToDemandReq.getApplyBillList())
                || CollectionUtils.isEmpty(autoApplyToDemandReq.getApplyBillDetailList())){
            Logs.info("PmsDemandDomainServiceImpl.taskApplyToDemand.没有符合条件的可转单数据：" + JSON.toJSONString(autoApplyToDemandReq));
            return;
        }

        autoApplyToDemand(autoApplyToDemandReq);
    }

    /**
     * autoApplyToDemandReq为被调用方进行简单分组。
     * 1.审核时，对应的没有采购批次的单个订货申请及其明细
     * 2.定时任务，固定一个批次的订货申请及其他明细
     * @param autoApplyToDemandReq
     */
    private void autoApplyToDemand(AutoApplyToDemandReq autoApplyToDemandReq){
        CompletableFuture.runAsync(() -> {
            Logs.info("PmsDemandDomainServiceImpl.autoApplyToDemand.start:" + JSON.toJSONString(autoApplyToDemandReq));
            try {
                AutoApplyToDemandDTO autoApplyToDemandDTO = checkFilterAutoApplyToDemand(autoApplyToDemandReq);
                if(CollectionUtils.isEmpty(autoApplyToDemandDTO.getApplyBillList())
                        || CollectionUtils.isEmpty(autoApplyToDemandDTO.getApplyBillDetailList())){
                    Logs.info("PmsDemandDomainServiceImpl.autoApplyToDemand.没有符合条件的可转单数据：" + JSON.toJSONString(autoApplyToDemandReq));
                    return;
                }
                //组装需求单
                hanlerAutoApplyToDemand(autoApplyToDemandDTO);
            } catch (Exception e) {
                e.printStackTrace();
                Logs.error("PmsDemandDomainServiceImpl.autoApplyToDemand.error:" + JSON.toJSONString(autoApplyToDemandReq), e);
            }
            Logs.info("PmsDemandDomainServiceImpl.autoApplyToDemand.end:" + JSON.toJSONString(autoApplyToDemandReq));
        }, applyToDemandProcessThreadPool);

    }

    /**
     * 处理订货申请转换成需求单
     * @param autoApplyToDemandDTO
     */
    @Override
    public void hanlerAutoApplyToDemand(AutoApplyToDemandDTO autoApplyToDemandDTO) {
        Logs.info("PmsDemandDomainServiceImpl.hanlerAutoApplyToDemand.autoApplyToDemandDTO:" + JSON.toJSONString(autoApplyToDemandDTO));
        String orcategorylevel = iSupplychainBizSysParamRuleService.getValue(PMSSystemParamEnum.ORCATEGORYLEVEL);

        checkFilterZhuiJiaApplyToDemand(autoApplyToDemandDTO);
        //订货申请明细进行分组
        //key orcategorylevel_方向_分类编码 ,orcategorylevel=0则没有分类编码
        Map<String,List<PmsApplyBillDetailPO>> applyBillDetailMap = new HashMap<>();

        //key 部门编码
        Map<String,GoodsQueryDTO> goodsQueryDTOMap = new HashMap<>();
        //key 部门编码
        Map<String,List<PmsApplyBillDetailPO>> applyBillDetailDeptMap = new HashMap<>();
        for (PmsApplyBillDetailPO pmsApplyBillDetailPO : autoApplyToDemandDTO.getApplyBillDetailList()) {
            PmsApplyBillPO pmsApplyBillPO = autoApplyToDemandDTO.getApplyBillMap().get(pmsApplyBillDetailPO.getBillNo());
            String groupKey = orcategorylevel + "_" + pmsApplyBillPO.getApplyCate();

            if(goodsQueryDTOMap.containsKey(pmsApplyBillPO.getDeptCode())){
                goodsQueryDTOMap.get(pmsApplyBillPO.getDeptCode()).getSkuCodeList().add(pmsApplyBillDetailPO.getSkuCode());
            }
            else{
                GoodsQueryDTO goodsQueryDTO = new GoodsQueryDTO();
                goodsQueryDTO.setAttributeNameFlag(false);
                goodsQueryDTO.setDeptCode(pmsApplyBillPO.getDeptCode());
                List<String> skuCodeList = new ArrayList<>();
                skuCodeList.add(pmsApplyBillDetailPO.getSkuCode());
                goodsQueryDTO.setSkuCodeList(skuCodeList);
                goodsQueryDTOMap.put(pmsApplyBillPO.getDeptCode(),goodsQueryDTO);

            }

            if(applyBillDetailDeptMap.containsKey(pmsApplyBillPO.getDeptCode())){
                applyBillDetailDeptMap.get(pmsApplyBillPO.getDeptCode()).add(pmsApplyBillDetailPO);
            }
            else{
                List<PmsApplyBillDetailPO> list = new ArrayList<>();
                list.add(pmsApplyBillDetailPO);
                applyBillDetailDeptMap.put(pmsApplyBillPO.getDeptCode(),list);
            }

            Integer level = NumberUtil.getInteger(orcategorylevel, 0);
            if(ObjectUtils.notEqual(0,level)){
                String categoryCodeAll = pmsApplyBillDetailPO.getCategoryCodeFullPath();

                if(StringUtils.isEmpty(categoryCodeAll)){
                    List<String> categoryCodeList = new ArrayList<>();
                    categoryCodeList.add(pmsApplyBillDetailPO.getCategoryCode());
                    List<CategoryCodeAll> categoryCodeAll1 = supplychainPmsBizRuleEngineService.getCategoryCodeAll(categoryCodeList);
                    if(CollectionUtils.isNotEmpty(categoryCodeAll1)){
                        categoryCodeAll = categoryCodeAll1.get(0).getCategoryCodeAll();
                        pmsApplyBillDetailPO.setCategoryCodeFullPath(categoryCodeAll);
                    }
                }

                if(StringUtils.isNotEmpty(categoryCodeAll)){
                    String[] split = categoryCodeAll.split(",");
                    if(split.length > 0 && split.length >= level){
                        groupKey = groupKey + "_" + split[level - 1];

                    }
                }
            }

            //根据批次分组
            if(ObjectUtils.equals(ShippingWayEnum.DELIVERY.getCode(),pmsApplyBillDetailPO.getShippingWay())){
                if(StringUtils.isNotEmpty(pmsApplyBillDetailPO.getPurchBatchNo())){
                    groupKey =  groupKey + "_" + pmsApplyBillDetailPO.getShipperCode() + "_" + pmsApplyBillDetailPO.getPurchBatchNo();
                }
            }

            //追加追减不做分组
            if(ObjectUtils.equals(PmsDemanBillSourceEnum.ZHUI_JIA_JIAN.getCode(),autoApplyToDemandDTO.getBillSource())){
                groupKey = "default";
            }

            if(applyBillDetailMap.containsKey(groupKey)){
                applyBillDetailMap.get(groupKey).add(pmsApplyBillDetailPO);
            }else{
                List<PmsApplyBillDetailPO> list = new ArrayList<>();
                list.add(pmsApplyBillDetailPO);
                applyBillDetailMap.put(groupKey,list);
            }
        }

        //key = 部门编码_商品编码
        Map<String,GoodsQueryResp> goodsQueryRespMap = new HashMap<>();
        //查询部门商品信息
        for (GoodsQueryDTO value : goodsQueryDTOMap.values()) {
            List<GoodsQueryResp> goodsQueryResps = supplychainPmsBizRuleEngineService.listGoodsInfo(value);
            for (GoodsQueryResp goodsQueryResp : goodsQueryResps) {
                String key = value.getDeptCode() + "_" + goodsQueryResp.getGoodsInfo().getSkuCode();
                goodsQueryRespMap.put(key,goodsQueryResp);
            }
        }

        //获取自动需求单配转采门店
        if(ObjectUtils.equals(PmsDemanBillSourceEnum.AUTO.getCode(),autoApplyToDemandDTO.getBillSource())){
            Map<String,List<String>> autoDeliveryToPurchDeptMap = getAutoDeliveryToPurchDept(applyBillDetailDeptMap);
            autoApplyToDemandDTO.setAutoDeliveryToPurchDeptMap(autoDeliveryToPurchDeptMap);
        }


        autoApplyToDemandDTO.setGoodsQueryRespMap(goodsQueryRespMap);
        AssemblyDemandDataDTO assemblyDemandDataDTO = assemblyDemandData(autoApplyToDemandDTO, applyBillDetailMap);

        for (PmsDemandBillReq pmsDemandBillReq : assemblyDemandDataDTO.getPmsDemandBillReqList()) {
            submitDemandBill(pmsDemandBillReq);
        }

    }

    /**
     * 获取自动需求单配转采部门
     * @param applyBillDetailDeptMap
     */
    private Map<String,List<String>> getAutoDeliveryToPurchDept(Map<String,List<PmsApplyBillDetailPO>> applyBillDetailDeptMap) {

        //key部门编码 val商品编码
        Map<String,List<String>> autoDeliveryToPurchDeptMap = new HashMap<>();

        QueryBatchDeptListReq req = QueryBatchDeptListReq.builder()
                .classCode(GroupDeptEnum.CONTROL_GROUP.getCode())
                .deptCodeList(new ArrayList<>(applyBillDetailDeptMap.keySet()))
                .build();
        List<QueryBatchDeptListResp.Rows> groupList = baseDataSystemFeignClient.queryUpDeptListBatch(req).getRows();


        Set<String> deptGroupCodeSet = new HashSet<>();//店组群编码列表
        Map<String, List<String>> upDeptMap = new HashMap<>();
        for (QueryBatchDeptListResp.Rows rows : groupList) {
            List<String> groupCodeList = new ArrayList<>();
            for (QueryBatchDeptListResp.DeptGroup deptGroup : rows.getDeptGroupList()) {
                groupCodeList.add(deptGroup.getCode());
                deptGroupCodeSet.add(deptGroup.getCode());
            }
            upDeptMap.put(rows.getCode(),groupCodeList);
        }

        MdDemandStrategyStatusMapping4DeptQueryReq demandStrategyStatusMapping4DeptQueryReq = new MdDemandStrategyStatusMapping4DeptQueryReq();
        List<String> demandTransferConditionList = new ArrayList<>();
        demandTransferConditionList.add(MdDemandStrategyStatusConditionEnum.AUTO_DEMAND_DIST_TO_PURCHASE_RULE.getCode());
        demandStrategyStatusMapping4DeptQueryReq.setDemandTransferConditionList(demandTransferConditionList);

        List<String> demandTransferConditionBizList = new ArrayList<>();
        demandTransferConditionBizList.add(MdDemandStrategyBizConditionEnum.DIST_TO_PURCHASE_RULE.getCode());
        demandStrategyStatusMapping4DeptQueryReq.setDemandTransferConditionBizList(demandTransferConditionBizList);

//        demandStrategyStatusMapping4DeptQueryReq.setDeptCodeList(new ArrayList<>(upDeptMap.keySet()));
//        demandStrategyStatusMapping4DeptQueryReq.setDeptGroupCodeList(new ArrayList<>(deptGroupCodeSet));

        List<MdDemandStrategyStatusMapping4DeptDTO> mdDemandStrategyStatusMapping4DeptDTOS = mdDemandStrategyDomainService.queryMdDemandStrategyStatusMappingData(demandStrategyStatusMapping4DeptQueryReq);

        if(CollectionUtils.isEmpty(mdDemandStrategyStatusMapping4DeptDTOS)){
            return autoDeliveryToPurchDeptMap;
        }

        MdDemandStrategyStatusMapping4DeptDTO mdDemandStrategyStatusMapping4DeptDTO = mdDemandStrategyStatusMapping4DeptDTOS.get(0);

        if(ObjectUtils.equals(MdDistToPurchRuleEnum.NO_TRANSFER,mdDemandStrategyStatusMapping4DeptDTO.getVal())){
            return autoDeliveryToPurchDeptMap;
        }

        Iterator<String> iterator = upDeptMap.keySet().iterator();
        while(iterator.hasNext()){
            String deptCode = iterator.next();
            List<String> groupCodeList = upDeptMap.get(deptCode);

            Boolean isToPurch = false;
            if(mdDemandStrategyStatusMapping4DeptDTO.getDeptCodeList().contains(deptCode)){
                isToPurch = true;
            }
            groupCodeList.retainAll(mdDemandStrategyStatusMapping4DeptDTO.getDeptGroupCodeList());
            if(CollectionUtils.isNotEmpty(groupCodeList)){
                isToPurch = true;
            }

            if(CollectionUtils.isEmpty(mdDemandStrategyStatusMapping4DeptDTO.getDeptCodeList()) && CollectionUtils.isEmpty(mdDemandStrategyStatusMapping4DeptDTO.getDeptGroupCodeList())){
                isToPurch = true;
            }

            if(isToPurch){
                if(ObjectUtils.equals(MdDistToPurchRuleEnum.ALL_BY_RESPONSE_QTY,mdDemandStrategyStatusMapping4DeptDTO.getVal())){
                    List<String> skuCodeList = new ArrayList<>();
                    List<PmsApplyBillDetailPO> list = applyBillDetailDeptMap.get(deptCode);
                    for (PmsApplyBillDetailPO pmsApplyBillDetailPO : list) {
                        skuCodeList.add(pmsApplyBillDetailPO.getSkuCode());
                    }
                    autoDeliveryToPurchDeptMap.put(deptCode,skuCodeList);
                }
                else{
                    //查询商品订货包装率
                    GoodsStrageReq goodsStrageReq = new GoodsStrageReq();
                    goodsStrageReq.setDeptCode(deptCode);
                    Map<String,String> skuCodeMap = new HashMap<>();
                    List<GoodsStrageReq.GoodsInfo> goodsInfos = new ArrayList<>();
                    for (PmsApplyBillDetailPO pmsApplyBillDetailPO : applyBillDetailDeptMap.get(deptCode)) {
                        GoodsStrageReq.GoodsInfo goodsInfo = new GoodsStrageReq.GoodsInfo();
                        goodsInfo.setCategoryCodeAll(pmsApplyBillDetailPO.getCategoryCodeFullPath());
                        goodsInfo.setSkuCode(pmsApplyBillDetailPO.getSkuCode());

                        goodsInfos.add(goodsInfo);
                        skuCodeMap.put(pmsApplyBillDetailPO.getSkuCode(),pmsApplyBillDetailPO.getSkuCode());
                    }

                    goodsStrageReq.setGoodsInfos(goodsInfos);
                    GoodsStrategyResp purchGoodsStrategy = supplychainPmsBizRuleEngineService.getPurchGoodsStrategy(goodsStrageReq);
                    List<String> skuCodeList = new ArrayList<>();
                    for (GoodsStrategyResp.GoodsStrategy goodsStrategy : purchGoodsStrategy.getGoodsStrategyList()) {
                        if(ObjectUtils.equals(1,goodsStrategy.getTransferPurch()) && skuCodeMap.containsKey(goodsStrategy.getSkuCode())){
                            skuCodeList.add(goodsStrategy.getSkuCode());
                        }
                    }

                    if(CollectionUtils.isNotEmpty(skuCodeList)){
                        autoDeliveryToPurchDeptMap.put(deptCode,skuCodeList);
                    }
                }
            }
        }

        return autoDeliveryToPurchDeptMap;
    }

    private void checkFilterZhuiJiaApplyToDemand(AutoApplyToDemandDTO autoApplyToDemandDTO) {
        if(ObjectUtils.notEqual(2,autoApplyToDemandDTO.getBillSource())){
            return;
        }
        List<PmsApplyBillPO> applyBillList = new ArrayList<>();
        List<PmsApplyBillDetailPO> applyBillDetailList = new ArrayList<>();
        Map<String,PmsApplyBillPO> applyBillMap = new HashMap<>();
        for (PmsApplyBillPO pmsApplyBillPO : autoApplyToDemandDTO.getApplyBillList()) {
            //排除订货属性是紧急 的行，紧急的需要手工调需求生成；排除 追加、追减
            if(pmsApplyBillPO.getAttributeCode().equals(OrderAttributeEnum.ZHUI_JIA.getCode()) || pmsApplyBillPO.getAttributeCode().equals(OrderAttributeEnum.ZHUI_JIAN.getCode())){
                applyBillMap.put(pmsApplyBillPO.getBillNo(),pmsApplyBillPO);
                applyBillList.add(pmsApplyBillPO);
            }
        }

        if(applyBillMap.size() != autoApplyToDemandDTO.getApplyBillList().size()){
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_002_B029);
        }
        for (PmsApplyBillDetailPO pmsApplyBillDetailPO : autoApplyToDemandDTO.getApplyBillDetailList()) {
            if(applyBillMap.containsKey(pmsApplyBillDetailPO.getBillNo())){
                applyBillDetailList.add(pmsApplyBillDetailPO);
            }
        }
        autoApplyToDemandDTO.setApplyBillList(applyBillList);
        autoApplyToDemandDTO.setApplyBillDetailList(applyBillDetailList);

    }

    /**
     * 订货申请组装成需求单
     * @param autoApplyToDemandDTO
     */
    private AssemblyDemandDataDTO assemblyDemandData(AutoApplyToDemandDTO autoApplyToDemandDTO, Map<String,List<PmsApplyBillDetailPO>> applyBillDetailMap) {
        Map<String,PmsApplyBillPO> applyBillMap = autoApplyToDemandDTO.getApplyBillMap();
        Integer billSource = autoApplyToDemandDTO.getBillSource();
        AssemblyDemandDataDTO assemblyDemandDataDTO = new AssemblyDemandDataDTO();

        //送货日期
        //有效日期
        //生成需求单数据

        ManageAndCirculationDTO manageAndCirculation = supplychainPmsBizRuleEngineService.getManageAndCirculation();
        autoApplyToDemandDTO.setManageAndCirculation(manageAndCirculation);

        //处理 追加追减 - 配转采
        if(CollectionUtils.isNotEmpty(autoApplyToDemandDTO.getDeliveryToPurchParamDTO())
                && ObjectUtils.equals(PmsDemanBillSourceEnum.ZHUI_JIA_JIAN.getCode(),autoApplyToDemandDTO.getBillSource())){

            //单据来源基于订货申请单号与单内序号作为key
            Map<String,DeliveryToPurchDeliverySourceResp> deliveryToPurchDeliverySourceRespMap = new HashMap<>();
            for (PmsDemandDeliveryToPurchReq pmsDemandDeliveryToPurchReq : autoApplyToDemandDTO.getDeliveryToPurchParamDTO()) {
                pmsDemandDeliveryToPurchReq.setDeliveryToPurchRefList(new ArrayList<>());
                if(ObjectUtils.equals(1,pmsDemandDeliveryToPurchReq.getType())
                        && ObjectUtils.equals(1,pmsDemandDeliveryToPurchReq.getStatus())){
                    List<DeliveryToPurchDeliverySourceResp> deliveryToPurchDeliverySourceList = pmsDemandDeliveryToPurchReq.getDeliveryToPurchDeliverySourceList();
                    for (DeliveryToPurchDeliverySourceResp deliveryToPurchDeliverySourceResp : deliveryToPurchDeliverySourceList) {
                        //单据来源基于订货申请单号与单内序号作为key
                        String detailSourceKey = deliveryToPurchDeliverySourceResp.getApplyBillNo() + "_" + deliveryToPurchDeliverySourceResp.getApplyInsideId();
                        deliveryToPurchDeliverySourceRespMap.put(detailSourceKey,deliveryToPurchDeliverySourceResp);
                    }
                }
            }
            autoApplyToDemandDTO.setDeliveryToPurchDeliverySourceRespMap(deliveryToPurchDeliverySourceRespMap);
        }

        for (List<PmsApplyBillDetailPO> applyBillDetailPOList : applyBillDetailMap.values()) {

            PmsDemandBillReq pmsDemandBillReq = new PmsDemandBillReq();
            pmsDemandBillReq.setOpType(PmsDemanBillOpTypeEnum.AUTO_SUBMIT.getCode());
            String billNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.PMS_DEMAND,"");
            pmsDemandBillReq.setBillNo(billNo);

            Set<String> orderAttributeCodeSet = new HashSet<>();
            Set<String> orderAttributeNameSet = new HashSet<>();
            int i=0;
            LocalDate validityDate = LocalDate.now();//有效日期
            LocalDate deliverDate = LocalDate.now();//送货日期

            AutoApplyToDemandInsideIdDTO autoDemandInsideIdDTO = new AutoApplyToDemandInsideIdDTO();
            autoDemandInsideIdDTO.setBillNo(billNo);

            //需求单商品明细
            List<PmsDemandGoodsDetailReq> demandGoodsDetailList = new ArrayList<>();
            Map<String,PmsDemandGoodsDetailReq> demandGoodsDetailReqMap = new HashMap<>();

            Map<String,PmsDemandDeptGoodsDetailReq> demandDeptGoodsDetailReqMap = new HashMap<>();
            for (PmsApplyBillDetailPO pmsApplyBillDetailPO : applyBillDetailPOList) {
                PmsApplyBillPO pmsApplyBillPO = applyBillMap.get(pmsApplyBillDetailPO.getBillNo());

                if(validityDate.isBefore(pmsApplyBillPO.getValidityDate())){
                    validityDate = pmsApplyBillPO.getValidityDate();
                }

                if(deliverDate.isBefore(pmsApplyBillPO.getValidityDate())){
                    deliverDate = pmsApplyBillPO.getValidityDate();
                }

                if(i ==0){
                    applyCateToDemandBillDirection(pmsDemandBillReq,pmsApplyBillPO);

                    pmsDemandBillReq.setBillSource(billSource);
                    pmsDemandBillReq.setManageCategoryClass(pmsApplyBillPO.getManageCategoryClass());
                    pmsDemandBillReq.setManageCategoryCode(pmsApplyBillPO.getManageCategoryCode());
                    pmsDemandBillReq.setManageCategoryName(pmsApplyBillPO.getManageCategoryName());
                    pmsDemandBillReq.setRefundReason(pmsApplyBillPO.getRefundReason());
                    pmsDemandBillReq.setRefundReasonDesc(pmsApplyBillPO.getRefundReasonDesc());
                }
                orderAttributeCodeSet.add(pmsApplyBillPO.getAttributeCode());
                orderAttributeNameSet.add(pmsApplyBillPO.getAttributeName());

                //组装商品数据
                String goodsKey = pmsApplyBillDetailPO.getSkuCode() + "_" + pmsApplyBillDetailPO.getGoodsType();
                PmsDemandGoodsDetailReq pmsDemandGoodsDetailReq;
                if(demandGoodsDetailReqMap.containsKey(goodsKey)){
                    pmsDemandGoodsDetailReq = demandGoodsDetailReqMap.get(goodsKey);
                    pmsDemandGoodsDetailReq.setResponseMoney(pmsDemandGoodsDetailReq.getResponseMoney().add(pmsApplyBillDetailPO.getPurchMoney()));//响应金额
                    pmsDemandGoodsDetailReq.setResponseTax(pmsDemandGoodsDetailReq.getResponseTax().add(pmsApplyBillDetailPO.getPurchTax()));//响应金额税金
                    pmsDemandGoodsDetailReq.setResponseQty(pmsDemandGoodsDetailReq.getResponseQty().add(pmsApplyBillDetailPO.getOrderQty()));//响应数量

                    pmsDemandGoodsDetailReq.setDemandMoney(pmsDemandGoodsDetailReq.getDemandMoney().add(pmsApplyBillDetailPO.getPurchMoney()));//需求金额
                    pmsDemandGoodsDetailReq.setDemandTax(pmsDemandGoodsDetailReq.getDemandTax().add(pmsApplyBillDetailPO.getPurchTax()));//需求金额税金
                    pmsDemandGoodsDetailReq.setDemandQty(pmsDemandGoodsDetailReq.getDemandQty().add(pmsApplyBillDetailPO.getOrderQty()));//需求数量

                    //部门商品、出货方、关联关系
                    assemblyDemandDeptGoodsDetail(pmsDemandGoodsDetailReq,pmsApplyBillDetailPO,demandDeptGoodsDetailReqMap,pmsApplyBillPO,autoDemandInsideIdDTO,autoApplyToDemandDTO);
                }
                else{
                    pmsDemandGoodsDetailReq = new PmsDemandGoodsDetailReq();
                    demandGoodsDetailReqMap.put(goodsKey,pmsDemandGoodsDetailReq);
                    pmsDemandGoodsDetailReq.setInsideId((long) demandGoodsDetailList.size());
                    pmsDemandGoodsDetailReq.setBillNo(billNo);

                    demandGoodsDetailList.add(pmsDemandGoodsDetailReq);

                    List<PmsDemandDeptGoodsDetailReq> demandDeptGoodsDetailList = new ArrayList<>();
                    pmsDemandGoodsDetailReq.setDemandDeptGoodsDetailList(demandDeptGoodsDetailList);

                    assemblyDemandGoodsDetail(pmsDemandGoodsDetailReq,pmsApplyBillDetailPO);
                    //部门商品、出货方、关联关系
                    assemblyDemandDeptGoodsDetail(pmsDemandGoodsDetailReq,pmsApplyBillDetailPO,demandDeptGoodsDetailReqMap,pmsApplyBillPO,autoDemandInsideIdDTO,autoApplyToDemandDTO);
                }

                i++;

            }
            pmsDemandBillReq.setGoodCirculationRule(1);
            pmsDemandBillReq.getDemandGoodsDetailList().addAll(demandGoodsDetailList);
            pmsDemandBillReq.setDeliverDeliverDate(deliverDate);
            pmsDemandBillReq.setDeliverValidityDate(validityDate);
            pmsDemandBillReq.setPurchDeliverDate(deliverDate);
            pmsDemandBillReq.setPurchValidityDate(validityDate);

            pmsDemandBillReq.setOrderAttributeCode(String.join(",", orderAttributeCodeSet));
            pmsDemandBillReq.setOrderAttributeName(String.join(",", orderAttributeNameSet));

            //处理 追加追减的 配转采 出货方-供应商来源
            if(CollectionUtils.isNotEmpty(autoApplyToDemandDTO.getDeliveryToPurchParamDTO())
                    && ObjectUtils.equals(PmsDemanBillSourceEnum.ZHUI_JIA_JIAN.getCode(),autoApplyToDemandDTO.getBillSource())){

                //单内序号重新赋值
                for (PmsDemandDeliveryToPurchReq pmsDemandDeliveryToPurchReq : autoApplyToDemandDTO.getDeliveryToPurchParamDTO()) {
                    for (PmsDemandPurchShipperReq pmsDemandPurchShipperReq : pmsDemandDeliveryToPurchReq.getPurchShipperList()) {
                        pmsDemandPurchShipperReq.setInsideId(autoDemandInsideIdDTO.getPurchInsideId());//单内序号,单据内全局唯一,配送转采购供应商与出货方-供应商内唯一
                        autoDemandInsideIdDTO.setPurchInsideId(autoDemandInsideIdDTO.getPurchInsideId() + 1);
                    }
                }

                pmsDemandBillReq.setDeliveryToPurchParamDTO(autoApplyToDemandDTO.getDeliveryToPurchParamDTO());
            }

            //重算商品的响应数量
            if(CollectionUtils.isNotEmpty(demandGoodsDetailList)){
                rerunDemandGoodsDetail(demandGoodsDetailList,autoApplyToDemandDTO.getBillSource());
            }

            //自动 需求单的   配转采 TODO
            if(ObjectUtils.equals(PmsDemanBillSourceEnum.AUTO.getCode(),autoApplyToDemandDTO.getBillSource())){
                handlerAutoDemandDeliveryToPurch(autoApplyToDemandDTO, demandGoodsDetailList,pmsDemandBillReq,autoDemandInsideIdDTO);
            }


            assemblyDemandDataDTO.getPmsDemandBillReqList().add(pmsDemandBillReq);
        }


        return assemblyDemandDataDTO;
    }

    /**
     * 处理自动需求单的配转采
     * @param autoApplyToDemandDTO
     * @param demandGoodsDetailList
     */
    private void handlerAutoDemandDeliveryToPurch(AutoApplyToDemandDTO autoApplyToDemandDTO, List<PmsDemandGoodsDetailReq> demandGoodsDetailList
            ,PmsDemandBillReq pmsDemandBillReq,AutoApplyToDemandInsideIdDTO autoDemandInsideIdDTO) {
        DeliveryToPurchReq deliveryToPurchReq = new DeliveryToPurchReq();
        deliveryToPurchReq.setSrcType(1);

        List<DeliveryToPurchReq.DeliveryToPurchDeptGoodsInfo> deptGoodsList = new ArrayList<>();

        deliveryToPurchReq.setDeptGoodsList(deptGoodsList);

        Map<Long,PmsDemandDeptGoodsDetailReq> pmsDemandDeptGoodsDetailReqMap = new HashMap<>();
        Map<Long,PmsDemandDeliveryShipperReq> pmsDemandDeliveryShipperReqMap = new HashMap<>();

        for (PmsDemandGoodsDetailReq pmsDemandGoodsDetailReq : demandGoodsDetailList) {
            for (PmsDemandDeptGoodsDetailReq pmsDemandDeptGoodsDetailReq : pmsDemandGoodsDetailReq.getDemandDeptGoodsDetailList()) {
                if(autoApplyToDemandDTO.getAutoDeliveryToPurchDeptMap().containsKey(pmsDemandDeptGoodsDetailReq.getOrderDeptCode())
                && ObjectUtils.equals(DirectSignEnum.NOT_DIRECT.getCode(),pmsDemandDeptGoodsDetailReq.getDirectSign())
                && autoApplyToDemandDTO.getAutoDeliveryToPurchDeptMap().get(pmsDemandDeptGoodsDetailReq.getOrderDeptCode()).contains(pmsDemandDeptGoodsDetailReq.getSkuCode())){

                    pmsDemandDeptGoodsDetailReqMap.put(pmsDemandDeptGoodsDetailReq.getInsideId(),pmsDemandDeptGoodsDetailReq);

                    DeliveryToPurchReq.DeliveryToPurchDeptGoodsInfo dliveryToPurchDeptGoodsInfo = new DeliveryToPurchReq.DeliveryToPurchDeptGoodsInfo();

                    dliveryToPurchDeptGoodsInfo.setInsideId(pmsDemandDeptGoodsDetailReq.getInsideId());//部门商品的单内序号，前端生成,当前业务唯一
                    dliveryToPurchDeptGoodsInfo.setDirectSign(pmsDemandDeptGoodsDetailReq.getDirectSign());//是否直流 0-非直流 1-直流
                    dliveryToPurchDeptGoodsInfo.setDeptCode(pmsDemandDeptGoodsDetailReq.getOrderDeptCode());//要货部门编码
                    dliveryToPurchDeptGoodsInfo.setDeptName(pmsDemandDeptGoodsDetailReq.getOrderDeptName());//要货部门名称
                    dliveryToPurchDeptGoodsInfo.setGoodsType(pmsDemandDeptGoodsDetailReq.getGoodsType());//商品类型,1主品,2赠品
                    dliveryToPurchDeptGoodsInfo.setSkuCode(pmsDemandDeptGoodsDetailReq.getSkuCode());//商品编码
                    dliveryToPurchDeptGoodsInfo.setSkuName(pmsDemandDeptGoodsDetailReq.getSkuName());//商品名称
                    dliveryToPurchDeptGoodsInfo.setSkuModel(pmsDemandGoodsDetailReq.getSkuModel());//商品规格
                    dliveryToPurchDeptGoodsInfo.setGoodsNo(pmsDemandGoodsDetailReq.getGoodsNo());//商品货号
                    dliveryToPurchDeptGoodsInfo.setCategoryCode(pmsDemandGoodsDetailReq.getCategoryCode());//品类编码
                    dliveryToPurchDeptGoodsInfo.setCategoryName(pmsDemandGoodsDetailReq.getCategoryName());//品类名称
                    dliveryToPurchDeptGoodsInfo.setCategoryCodeAll(pmsDemandGoodsDetailReq.getCategoryCodeAll());//全路径品类编码,英文逗号分割
                    dliveryToPurchDeptGoodsInfo.setBarcode(pmsDemandGoodsDetailReq.getBarcode());//商品条码
                    dliveryToPurchDeptGoodsInfo.setUnit(pmsDemandGoodsDetailReq.getUnit());//单位
                    dliveryToPurchDeptGoodsInfo.setPackageUnit(pmsDemandGoodsDetailReq.getPackageUnit());//整件单位
                    dliveryToPurchDeptGoodsInfo.setInputTaxRate(pmsDemandGoodsDetailReq.getInputTaxRate());//进项税率，13%存13
                    dliveryToPurchDeptGoodsInfo.setOutputTaxRate(pmsDemandGoodsDetailReq.getOutputTaxRate());//销项税率，13%存13
                    dliveryToPurchDeptGoodsInfo.setPurchBatchNo(pmsDemandDeptGoodsDetailReq.getPurchBatchNo());//采购批次
                    dliveryToPurchDeptGoodsInfo.setSendMode(pmsDemandDeptGoodsDetailReq.getSendMode());//送货方式 0-到店，1-到客户

                    if(CollectionUtils.isEmpty(pmsDemandDeptGoodsDetailReq.getDemandDeliveryShipperList())){
                        continue;
                    }

                    //出货方-配送信息
                    List<DeliveryToPurchReq.DeliveryToPurchDeliveryInfo> deliveryList = new ArrayList<>();
                    dliveryToPurchDeptGoodsInfo.setDeliveryUnitRate(pmsDemandDeptGoodsDetailReq.getDemandDeliveryShipperList().get(0).getDeliveryUnitRate());
                    for (PmsDemandDeliveryShipperReq pmsDemandDeliveryShipperReq : pmsDemandDeptGoodsDetailReq.getDemandDeliveryShipperList()) {
                        pmsDemandDeliveryShipperReqMap.put(pmsDemandDeliveryShipperReq.getInsideId(),pmsDemandDeliveryShipperReq);

                        DeliveryToPurchReq.DeliveryToPurchDeliveryInfo deliveryToPurchDeliveryInfo = new DeliveryToPurchReq.DeliveryToPurchDeliveryInfo();

                        deliveryToPurchDeliveryInfo.setBillNo(pmsDemandDeliveryShipperReq.getBillNo());//当配送订单时，必填配送订单号
                        deliveryToPurchDeliveryInfo.setInsideId(pmsDemandDeliveryShipperReq.getInsideId());//配送出货方的单内序号，前端生成,当前业务唯一或者配送订单商品明细行
                        deliveryToPurchDeliveryInfo.setPinsideId(pmsDemandDeliveryShipperReq.getPinsideId());//上一级部门商品单内序号
                        deliveryToPurchDeliveryInfo.setDockCode(pmsDemandDeliveryShipperReq.getDockCode());//停靠点编码
                        deliveryToPurchDeliveryInfo.setDockName(pmsDemandDeliveryShipperReq.getDockName());//停靠点名称
                        deliveryToPurchDeliveryInfo.setDistDeptCode(pmsDemandDeliveryShipperReq.getDistDeptCode());//配送部门编码
                        deliveryToPurchDeliveryInfo.setDistDeptName(pmsDemandDeliveryShipperReq.getDistDeptName());//配送部门名称
                        deliveryToPurchDeliveryInfo.setDeliveryUnitRate(pmsDemandDeliveryShipperReq.getDeliveryUnitRate());//配送包装率
                        deliveryToPurchDeliveryInfo.setDirectSign(pmsDemandDeliveryShipperReq.getDirectSign());// 是否直流 0-非直流 1-直流
                        deliveryToPurchDeliveryInfo.setDeliveryQty(pmsDemandDeliveryShipperReq.getResponseQty());//配送数量
                        deliveryToPurchDeliveryInfo.setDistDeptStockAtpQty(pmsDemandDeliveryShipperReq.getDistDeptStockAtpQty());//配送部门可用库存
                        deliveryToPurchDeliveryInfo.setDistDeptStockRealQty(pmsDemandDeliveryShipperReq.getDistDeptStockRealQty());//配送部门实际库存

                        deliveryList.add(deliveryToPurchDeliveryInfo);
                    }

                    dliveryToPurchDeptGoodsInfo.setDeliveryQty(pmsDemandDeptGoodsDetailReq.getResponseQty());//配送数量

                    //订货申请信息
                    List<DeliveryToPurchReq.OrderApplyInfo> orderApplyInfoList = new ArrayList<>();
                    for (PmsDemandDetailSourceRefReq pmsDemandDetailSourceRefReq : pmsDemandDeptGoodsDetailReq.getDemandDetailSourceList()) {
                        DeliveryToPurchReq.OrderApplyInfo orderApplyInfo = new DeliveryToPurchReq.OrderApplyInfo();
                        orderApplyInfo.setApplyBillNo(pmsDemandDetailSourceRefReq.getApplyBillNo());//订货申请单号
                        orderApplyInfo.setApplyInsideId(pmsDemandDetailSourceRefReq.getSrcInsideId());//订货申请单内序号
                        orderApplyInfo.setCustomerCode(pmsDemandDetailSourceRefReq.getSrcCustomerCode());//客户编码
                        orderApplyInfo.setCustomerName(pmsDemandDetailSourceRefReq.getSrcCustomerName());//客户名称
                        orderApplyInfo.setQty(pmsDemandDetailSourceRefReq.getSrcDemandQty());//要货数量

                        orderApplyInfoList.add(orderApplyInfo);
                    }

                    dliveryToPurchDeptGoodsInfo.setDeliveryList(deliveryList);
                    dliveryToPurchDeptGoodsInfo.setOrderApplyInfoList(orderApplyInfoList);
                    deptGoodsList.add(dliveryToPurchDeptGoodsInfo);
                }
                else{
                    continue;
                }
            }
        }

        if(CollectionUtils.isNotEmpty(deliveryToPurchReq.getDeptGoodsList())){
            //全部可转采与转采购信息回填
            List<PmsDemandDeliveryToPurchReq> deliveryToPurchParamDTO = new ArrayList<>();
            DeliveryToPurchResultResp deliveryToPurchResultResp = getIPmsDeliveryToPurchDomainService().generateDeliveryToPurch(deliveryToPurchReq);
            //组装需求单配转采
            for (DeliveryToPurchResp deliveryToPurchResp : deliveryToPurchResultResp.getDeliveryToPurchRespList()) {
                PmsDemandDeliveryToPurchReq pmsDemandDeliveryToPurchReq = new PmsDemandDeliveryToPurchReq();
                pmsDemandDeliveryToPurchReq.setBillNo(pmsDemandBillReq.getBillNo());//需求单号
                pmsDemandDeliveryToPurchReq.setInsideId(deliveryToPurchResp.getInsideId());//单内序号
                pmsDemandDeliveryToPurchReq.setGoodsType(deliveryToPurchResp.getGoodsType());//商品类型,1主品,2赠品
                pmsDemandDeliveryToPurchReq.setSkuCode(deliveryToPurchResp.getSkuCode());///商品编码
                pmsDemandDeliveryToPurchReq.setSkuName(deliveryToPurchResp.getSkuName());//商品名称
                pmsDemandDeliveryToPurchReq.setSkuModel(deliveryToPurchResp.getSkuModel());//商品规格
                pmsDemandDeliveryToPurchReq.setGoodsNo(deliveryToPurchResp.getGoodsNo());//商品货号
                pmsDemandDeliveryToPurchReq.setCategoryCode(deliveryToPurchResp.getCategoryCode());//品类编码
                pmsDemandDeliveryToPurchReq.setCategoryCodeAll(deliveryToPurchResp.getCategoryCodeAll());//全路径品类编码,英文逗号分割
                pmsDemandDeliveryToPurchReq.setCategoryName(deliveryToPurchResp.getCategoryName());//品类名称
                pmsDemandDeliveryToPurchReq.setUnit(deliveryToPurchResp.getUnit());//单位
                pmsDemandDeliveryToPurchReq.setPackageUnit(deliveryToPurchResp.getPackageUnit());//整件单位
                pmsDemandDeliveryToPurchReq.setInputTaxRate(deliveryToPurchResp.getInputTaxRate());//进项税率，13%存13
                pmsDemandDeliveryToPurchReq.setOutputTaxRate(deliveryToPurchResp.getOutputTaxRate());//销项税率，13%存13
                pmsDemandDeliveryToPurchReq.setDistDeptStockAtpQty(deliveryToPurchResp.getDistDeptStockAtpQty());//配送部门实际库存
                pmsDemandDeliveryToPurchReq.setDistDeptStockRealQty(deliveryToPurchResp.getDistDeptStockRealQty());//配送部门可用库存
                pmsDemandDeliveryToPurchReq.setDockCode(deliveryToPurchResp.getDockCode());//停靠点编码
                pmsDemandDeliveryToPurchReq.setDockName(deliveryToPurchResp.getDockName());//停靠点名称
                pmsDemandDeliveryToPurchReq.setPurchBatchNo(deliveryToPurchResp.getPurchBatchNo());//采购批次
                pmsDemandDeliveryToPurchReq.setSendMode(deliveryToPurchResp.getSendMode());//送货方式 0-到店，1-到客户
                pmsDemandDeliveryToPurchReq.setDeliveryQty(deliveryToPurchResp.getDeliveryQty());//配送数量
                pmsDemandDeliveryToPurchReq.setDeliveryUnitRate(deliveryToPurchResp.getDeliveryUnitRate());//配送包装率
                pmsDemandDeliveryToPurchReq.setDtpWholeQty(deliveryToPurchResp.getDtpWholeQty());//确认转采购整件数量
                pmsDemandDeliveryToPurchReq.setDtpOddQty(deliveryToPurchResp.getDtpOddQty());//确认转采购零头数量
                pmsDemandDeliveryToPurchReq.setDtpQty(deliveryToPurchResp.getDtpQty());//确认转采购数量
                pmsDemandDeliveryToPurchReq.setReason(deliveryToPurchResp.getReason());//不可转采原因
                pmsDemandDeliveryToPurchReq.setDistDeptCode(deliveryToPurchResp.getDistDeptCode());//配送部门编码
                pmsDemandDeliveryToPurchReq.setDistDeptName(deliveryToPurchResp.getDistDeptName());//配送部门名称
                pmsDemandDeliveryToPurchReq.setType(deliveryToPurchResp.getType());//转采类型,0不可转采, 1可转采

                //配送转采购供应商信息
                List<PmsDemandPurchShipperReq> purchShipperList = new ArrayList<>();
                pmsDemandDeliveryToPurchReq.setPurchShipperList(purchShipperList);

                //查询采购价格
                QueryGoodsPurchPriceInfoReq queryGoodsAttrReqParams = new QueryGoodsPurchPriceInfoReq();
                queryGoodsAttrReqParams.setApplyCate(ApplyCateEnum.PURCH.getCode());
                queryGoodsAttrReqParams.setDeptCode(deliveryToPurchResp.getDistDeptCode());
                List<QueryGoodsPurchPriceInfoReq.GoodsInfo> goodsInfoList = new ArrayList<>();
                QueryGoodsPurchPriceInfoReq.GoodsInfo godsInfo = QueryGoodsPurchPriceInfoReq.GoodsInfo.builder().skuCode(deliveryToPurchResp.getSkuCode()).build();
                goodsInfoList.add(godsInfo);
                queryGoodsAttrReqParams.setGoodsInfoList(goodsInfoList);

                GoodsPurchPriceInfoResp goodsPurchPriceInfoResp = null;
                List<GoodsPurchPriceInfoResp> goodsPurchPriceInfoResps = supplychainControlEngineService.getSupplychainBizGoodsRuleService().goodsPurchPriceInfoList(queryGoodsAttrReqParams);
                if(CollectionUtils.isEmpty(goodsPurchPriceInfoResps)){
                    pmsDemandDeliveryToPurchReq.setType(0);//转采类型,0不可转采, 1可转采
                    String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B033.getErrorMsg(), new String[]{deliveryToPurchResp.getDistDeptCode(),
                            deliveryToPurchResp.getSkuCode()}).getMessage();
                    pmsDemandDeliveryToPurchReq.setReason(message);
                }
                else{
                    goodsPurchPriceInfoResp = goodsPurchPriceInfoResps.get(0);
                    for (GoodsPurchPriceInfoResp goodsPurchPriceInfo : goodsPurchPriceInfoResps) {
                        if(ObjectUtils.equals(1,goodsPurchPriceInfo.getMainSupplierMode())){
                            goodsPurchPriceInfoResp = goodsPurchPriceInfo;
                            break;
                        }
                    }
                }

                if(ObjectUtils.equals(1,deliveryToPurchResp.getType()) && CollectionUtils.isNotEmpty(goodsPurchPriceInfoResps)){

                    pmsDemandDeliveryToPurchReq.setStatus(1);//转采状态,0未转采, 1已转采
                    //组装供应商信息
                    PmsDemandPurchShipperReq pmsDemandPurchShipperReq = new PmsDemandPurchShipperReq();
                    pmsDemandPurchShipperReq.setBillNo(pmsDemandBillReq.getBillNo());//需求单号
                    pmsDemandPurchShipperReq.setInsideId(autoDemandInsideIdDTO.getPurchInsideId());//单内序号,单据内全局唯一,配送转采购供应商与出货方-供应商内唯一
                    autoDemandInsideIdDTO.setPurchInsideId(autoDemandInsideIdDTO.getPurchInsideId() + 1);
                    pmsDemandPurchShipperReq.setDeliveryToPurchInsideId(pmsDemandDeliveryToPurchReq.getInsideId());//需求配转采商品表单内序号,pms_demand_delivery_to_purch.inside_id,仅convert_flag=1有效
                    pmsDemandPurchShipperReq.setGoodsType(deliveryToPurchResp.getGoodsType());//商品类型,0主品,1赠品

                    pmsDemandPurchShipperReq.setSkuCode(deliveryToPurchResp.getSkuCode());//商品编码
                    pmsDemandPurchShipperReq.setSkuName(deliveryToPurchResp.getSkuName());//商品名称
                    pmsDemandPurchShipperReq.setDirectSign(DirectSignEnum.NOT_DIRECT.getCode());//直流标志 0-非直流 1-直流
                    pmsDemandPurchShipperReq.setPurchUnitRate(deliveryToPurchResp.getPurchUnitRate());//采购包装率

                    pmsDemandPurchShipperReq.setDockCode(deliveryToPurchResp.getDockCode());//停靠点编码
                    pmsDemandPurchShipperReq.setDockName(deliveryToPurchResp.getDockName());//停靠点名称
                    pmsDemandPurchShipperReq.setConvertFlag(DemandConvertlEnum.DELIVERY_TO_PURCH.getCode());//转单标识,0普通, 1配转采
                    pmsDemandPurchShipperReq.setOrderDeptCode(deliveryToPurchResp.getDistDeptCode());//订货部门编码
                    pmsDemandPurchShipperReq.setStatus(1);//状态,0未选择, 1选中

                    pmsDemandPurchShipperReq.setPromotePeriodPrice(goodsPurchPriceInfoResp.getPromotionPrice());//促销价
                    pmsDemandPurchShipperReq.setPromoteActivityCode(goodsPurchPriceInfoResp.getPromotionId());//促销活动编码
                    pmsDemandPurchShipperReq.setPromoteActivityName(goodsPurchPriceInfoResp.getPromotionName());//促销活动名称
                    pmsDemandPurchShipperReq.setMainSupplierMode(goodsPurchPriceInfoResp.getMainSupplierMode());//是否主供应商,0否,1是
                    pmsDemandPurchShipperReq.setContractNo(goodsPurchPriceInfoResp.getContractNo());//合同号
                    pmsDemandPurchShipperReq.setContractSpecialPrice(goodsPurchPriceInfoResp.getContractSpecialPrice());//合同特供价
                    pmsDemandPurchShipperReq.setContractPurchPrice(goodsPurchPriceInfoResp.getContractPrice());//合同进价
                    pmsDemandPurchShipperReq.setContractMaxPurchPrice(goodsPurchPriceInfoResp.getContractMaxPrice());//合同最高进价
                    pmsDemandPurchShipperReq.setLastSupplierName("");//最近一次供应商名称
                    pmsDemandPurchShipperReq.setLastSupplierCode("");//最近一次供应商编码
                    pmsDemandPurchShipperReq.setLastPurchPrice(goodsPurchPriceInfoResp.getPurchasePrice());//最后进价(最后进价、部门商品档案进价、档案进价,商品一个字段返回)
                    pmsDemandPurchShipperReq.setSupplierCode(goodsPurchPriceInfoResp.getSupplierCode());//供应商编码
                    pmsDemandPurchShipperReq.setSupplierName(goodsPurchPriceInfoResp.getSupplierName());//供应商名称
                    BigDecimal unitRate = deliveryToPurchResp.getPurchUnitRate();
                    if(null == unitRate){
                        unitRate = BigDecimal.ONE;
                    }
                    BigDecimal oddQty = deliveryToPurchResp.getDeliveryQty().remainder(unitRate);
                    BigDecimal wholeQty = deliveryToPurchResp.getDeliveryQty().subtract(oddQty);//箱数

                    pmsDemandPurchShipperReq.setPurchWholeQty(wholeQty);//整件数量
                    pmsDemandPurchShipperReq.setPurchOddQty(oddQty);//零头数量
                    pmsDemandPurchShipperReq.setPurchQty(deliveryToPurchResp.getDeliveryQty());//采购数量

                    //采购税金：该行采购金额/（1+进项税率）*进项税率
                    //采购金额
                    BigDecimal purchMoney = pmsDemandPurchShipperReq.getPurchQty().multiply(pmsDemandPurchShipperReq.getPurchPrice());
                    BigDecimal purchTax= purchMoney.divide(pmsDemandDeliveryToPurchReq.getInputTaxRate().divide(new BigDecimal("100")).add(new BigDecimal("1")),2, RoundingMode.HALF_UP).multiply(pmsDemandDeliveryToPurchReq.getInputTaxRate().divide(new BigDecimal("100")));
                    pmsDemandPurchShipperReq.setPurchMoney(purchMoney);//采购金额
                    pmsDemandPurchShipperReq.setPurchTax(MoneyUtil.round2HalfUp(purchTax));//采购税金
                    pmsDemandPurchShipperReq.setPurchPrice(goodsPurchPriceInfoResp.getFinaPrice());//采购价格

                    purchShipperList.add(pmsDemandPurchShipperReq);
                }
                else{
                    pmsDemandDeliveryToPurchReq.setStatus(0);//转采状态,0未转采, 1已转采
                }

                //配送转采购关系
                List<PmsDemandDeliveryToPurchRefReq> deliveryToPurchRefList = new ArrayList<>();
                pmsDemandDeliveryToPurchReq.setDeliveryToPurchRefList(deliveryToPurchRefList);
                List<DeliveryToPurchRefResp> deliveryToPurchRefResps = deliveryToPurchResultResp.getDeliveryToPurchRefMap().get(deliveryToPurchResp.getInsideId());
                for (DeliveryToPurchRefResp deliveryToPurchRefResp : deliveryToPurchRefResps) {
                    PmsDemandDeliveryToPurchRefReq pmsDemandDeliveryToPurchRefReq = new PmsDemandDeliveryToPurchRefReq();

                    pmsDemandDeliveryToPurchRefReq.setBillNo(pmsDemandBillReq.getBillNo());//需求单号
                    pmsDemandDeliveryToPurchRefReq.setDeliveryToPurchInsideId(deliveryToPurchRefResp.getDeliveryToPurchInsideId());//配转采商品单内序号
                    pmsDemandDeliveryToPurchRefReq.setDeptGoodsInsideId(deliveryToPurchRefResp.getDeptGoodsInsideId());//商品部门单内序号
                    pmsDemandDeliveryToPurchRefReq.setDeliveryShipperInsideId(deliveryToPurchRefResp.getDeliveryShipperInsideId());//配送出货方单内序号
                    pmsDemandDeliveryToPurchRefReq.setType(deliveryToPurchResp.getType());//转采类型,0不可转采, 1可转采

                    deliveryToPurchRefList.add(pmsDemandDeliveryToPurchRefReq);

                    if(ObjectUtils.equals(1,deliveryToPurchResp.getType()) && CollectionUtils.isNotEmpty(goodsPurchPriceInfoResps)){
                        pmsDemandDeptGoodsDetailReqMap.get(deliveryToPurchRefResp.getDeptGoodsInsideId()).setConvertFlag(DemandConvertlEnum.DELIVERY_TO_PURCH.getCode());
                        pmsDemandDeliveryShipperReqMap.get(deliveryToPurchRefResp.getDeliveryShipperInsideId()).setConvertFlag(DemandConvertlEnum.DELIVERY_TO_PURCH.getCode());
                    }

                }

                deliveryToPurchParamDTO.add(pmsDemandDeliveryToPurchReq);
            }

            pmsDemandBillReq.getDeliveryToPurchParamDTO().addAll(deliveryToPurchParamDTO);
        }
    }

    /**
     * 需求单自动组装 部门商品 出货方 关联表
     * @param pmsDemandGoodsDetailReq
     * @param pmsApplyBillDetailPO
     */
    private void assemblyDemandDeptGoodsDetail(PmsDemandGoodsDetailReq pmsDemandGoodsDetailReq, PmsApplyBillDetailPO pmsApplyBillDetailPO
            ,Map<String,PmsDemandDeptGoodsDetailReq> demandDeptGoodsDetailReqMap,PmsApplyBillPO pmsApplyBillPO,AutoApplyToDemandInsideIdDTO autoDemandInsideIdDTO
            ,AutoApplyToDemandDTO autoApplyToDemandDTO) {

        //部门 + 商品类型 + 商品 + 订货包装率 + 价格 + 出货途径 + 出货方 + 是否直流 + 送货方式  unitRate
        String deptGoodsKey = pmsApplyBillDetailPO.getDeptCode() + getSplitKey(pmsApplyBillDetailPO.getGoodsType()) + getSplitKey(pmsApplyBillDetailPO.getSkuCode())
                + getSplitKey(pmsApplyBillDetailPO.getUnitRate()) + getSplitKey(pmsApplyBillDetailPO.getPurchTaxPrice()) + getSplitKey(pmsApplyBillDetailPO.getShippingWay())
                + getSplitKey(pmsApplyBillDetailPO.getShipperCode()) + getSplitKey(pmsApplyBillDetailPO.getDirectSign()) + getSplitKey(pmsApplyBillPO.getSendMode())
                + getSplitKey(pmsApplyBillDetailPO.getUnitRate());

        //追加追减 - 配转采
        //转单标识,0普通, 1配转采,DemandConvertlEnum
        Integer convertFlag = 0;
        DeliveryToPurchDeliverySourceResp deliveryToPurchDeliverySourceResp = new DeliveryToPurchDeliverySourceResp();
        if(ObjectUtils.equals(PmsDemanBillSourceEnum.ZHUI_JIA_JIAN.getCode(),autoApplyToDemandDTO.getBillSource())){
            String sourceKey = pmsApplyBillDetailPO.getBillNo() + "_" + pmsApplyBillDetailPO.getInsideId();
            if(autoApplyToDemandDTO.getDeliveryToPurchDeliverySourceRespMap().containsKey(sourceKey)){
                convertFlag = 1;
                deliveryToPurchDeliverySourceResp = autoApplyToDemandDTO.getDeliveryToPurchDeliverySourceRespMap().get(sourceKey);
            }
        }

        PmsDemandDeptGoodsDetailReq pmsDemandDeptGoodsDetailReq;
        Boolean mergeFlag = false;
        Boolean checkDeptGood = true;
        if(demandDeptGoodsDetailReqMap.containsKey(deptGoodsKey)){
            mergeFlag = true;
            pmsDemandDeptGoodsDetailReq = demandDeptGoodsDetailReqMap.get(deptGoodsKey);

            pmsDemandDeptGoodsDetailReq.setResponseMoney(pmsDemandDeptGoodsDetailReq.getResponseMoney().add(pmsApplyBillDetailPO.getPurchMoney()));//响应金额
            pmsDemandDeptGoodsDetailReq.setResponseTax(pmsDemandDeptGoodsDetailReq.getResponseTax().add(pmsApplyBillDetailPO.getPurchTax()));//响应金额税金
            pmsDemandDeptGoodsDetailReq.setResponseQty(pmsDemandDeptGoodsDetailReq.getResponseQty().add(pmsApplyBillDetailPO.getOrderQty()));//响应数量

            pmsDemandDeptGoodsDetailReq.setDemandMoney(pmsDemandDeptGoodsDetailReq.getDemandMoney().add(pmsApplyBillDetailPO.getPurchMoney()));//需求金额
            pmsDemandDeptGoodsDetailReq.setDemandTax(pmsDemandDeptGoodsDetailReq.getDemandTax().add(pmsApplyBillDetailPO.getPurchTax()));//需求金额税金
            pmsDemandDeptGoodsDetailReq.setDemandQty(pmsDemandDeptGoodsDetailReq.getDemandQty().add(pmsApplyBillDetailPO.getOrderQty()));//需求数量

        }
        else{
            pmsDemandDeptGoodsDetailReq = new PmsDemandDeptGoodsDetailReq();
            pmsDemandDeptGoodsDetailReq.setInsideId(autoDemandInsideIdDTO.getDeptGoodsInsideId());
            autoDemandInsideIdDTO.setDeptGoodsInsideId(autoDemandInsideIdDTO.getDeptGoodsInsideId() + 1);
            pmsDemandDeptGoodsDetailReq.setPinsideId(pmsDemandGoodsDetailReq.getInsideId());

            demandDeptGoodsDetailReqMap.put(deptGoodsKey,pmsDemandDeptGoodsDetailReq);

            pmsDemandDeptGoodsDetailReq.setStatus(1);//0失败 1成功
            pmsDemandDeptGoodsDetailReq.setType(1);//来源类型 1.门店要货(订货申请) 2.主动配货(主派)
            pmsDemandDeptGoodsDetailReq.setDeptOperateMode(pmsApplyBillPO.getOperateMode());//门店经营模式 1 直营 2 加盟
            pmsDemandDeptGoodsDetailReq.setOrderDeptCode(pmsApplyBillPO.getDeptCode());//订货部门编码
            pmsDemandDeptGoodsDetailReq.setOrderDeptName(pmsApplyBillPO.getDeptName());//订货部门名称
            pmsDemandDeptGoodsDetailReq.setOrderDeptType(1);//订货部门类型(1:门店2:配送)

            pmsDemandDeptGoodsDetailReq.setGoodsType(pmsApplyBillDetailPO.getGoodsType());//商品类型,1主品,2赠品
            pmsDemandDeptGoodsDetailReq.setSkuCode(pmsApplyBillDetailPO.getSkuCode());//商品编码
            pmsDemandDeptGoodsDetailReq.setSkuName(pmsApplyBillDetailPO.getSkuName());//商品名称
            pmsDemandDeptGoodsDetailReq.setSalePrice(pmsApplyBillDetailPO.getSalePrice());//销售单价

            pmsDemandDeptGoodsDetailReq.setBillNo(pmsDemandGoodsDetailReq.getBillNo());//需求单号

            pmsDemandDeptGoodsDetailReq.setResponseMoney(pmsApplyBillDetailPO.getPurchMoney());//响应金额
            pmsDemandDeptGoodsDetailReq.setResponseTax(pmsApplyBillDetailPO.getPurchTax());//响应金额税金
            pmsDemandDeptGoodsDetailReq.setResponseQty(pmsApplyBillDetailPO.getOrderQty());//响应数量

            pmsDemandDeptGoodsDetailReq.setResponseOddQty(BigDecimal.ZERO);//响应零头数量
            pmsDemandDeptGoodsDetailReq.setResponseWholeQty(BigDecimal.ZERO);//响应整件数量

            pmsDemandDeptGoodsDetailReq.setDemandMoney(pmsApplyBillDetailPO.getPurchMoney());//需求金额
            pmsDemandDeptGoodsDetailReq.setDemandTax(pmsApplyBillDetailPO.getPurchTax());//需求金额税金
            pmsDemandDeptGoodsDetailReq.setDemandQty(pmsApplyBillDetailPO.getOrderQty());//需求数量

            pmsDemandDeptGoodsDetailReq.setDemandOddQty(BigDecimal.ZERO);//需求零头数量
            pmsDemandDeptGoodsDetailReq.setDemandWholeQty(BigDecimal.ZERO);//需求整件数量


            pmsDemandDeptGoodsDetailReq.setDirectSign(pmsApplyBillDetailPO.getDirectSign());//直流标识,直流标志 0-非直流 1-直流
            pmsDemandDeptGoodsDetailReq.setShippingWay(pmsApplyBillDetailPO.getShippingWay());//出货途径,0采购1配送
            pmsDemandDeptGoodsDetailReq.setSendMode(pmsApplyBillDetailPO.getSaleMode());//来源单据送货方式 0-到店，1-到客户
            pmsDemandDeptGoodsDetailReq.setPurchBatchNo(pmsApplyBillDetailPO.getPurchBatchNo());//采购批次
            pmsDemandDeptGoodsDetailReq.setCirculationModeCode(pmsApplyBillDetailPO.getCirculationModeCode());//流转途径编码
            pmsDemandDeptGoodsDetailReq.setWorkStateCode(pmsApplyBillDetailPO.getWorkStatusCode());//经营状态编码

            pmsDemandDeptGoodsDetailReq.setOrderStockQty(pmsApplyBillDetailPO.getStockQty());//订货部门库存
            pmsDemandDeptGoodsDetailReq.setOrderAtpQty(pmsApplyBillDetailPO.getStockQty());//订货部门可用库存
            pmsDemandDeptGoodsDetailReq.setConvertFlag(convertFlag);
            pmsDemandDeptGoodsDetailReq.setOrderUnitRate(pmsApplyBillDetailPO.getUnitRate());

            //判断并记录失败原因 只有自动需求单校验
            checkDeptGood = true;//checkDeptGood(autoApplyToDemandDTO,pmsApplyBillPO,pmsApplyBillDetailPO,pmsDemandDeptGoodsDetailReq);

            if(!checkDeptGood){
                pmsDemandDeptGoodsDetailReq.setStatus(0);
                //TODO 根据需求策略判断是否作废
                autoApplyToDemandDTO.getFailedApplyBillDetailList().add(pmsApplyBillDetailPO);
            }
            else{
                autoApplyToDemandDTO.getSuccessApplyBillDetailList().add(pmsApplyBillDetailPO);
            }
            pmsDemandGoodsDetailReq.getDemandDeptGoodsDetailList().add(pmsDemandDeptGoodsDetailReq);
        }

        //写来源
        PmsDemandDetailSourceRefReq pmsDemandDetailSourceRefReq = new PmsDemandDetailSourceRefReq();
        pmsDemandDetailSourceRefReq.setInsideId(autoDemandInsideIdDTO.getDetailSourceRefInsideId());
        autoDemandInsideIdDTO.setDetailSourceRefInsideId(autoDemandInsideIdDTO.getDetailSourceRefInsideId() + 1);

        pmsDemandDetailSourceRefReq.setPinsideId(pmsDemandDeptGoodsDetailReq.getInsideId());
        pmsDemandDetailSourceRefReq.setGoodsInsideId(pmsDemandGoodsDetailReq.getInsideId());

        pmsDemandDetailSourceRefReq.setDemandBillNo(pmsDemandGoodsDetailReq.getBillNo());//需求单号
        pmsDemandDetailSourceRefReq.setType(1);//来源类型 1.门店要货(订货申请) 2.主动配货(主派)

        if(!checkDeptGood){
            pmsDemandDetailSourceRefReq.setStatus(0);//0失败 1成功 2失效
        }
        pmsDemandDetailSourceRefReq.setOrderDeptCode(pmsDemandDeptGoodsDetailReq.getOrderDeptCode());//订货部门编码
        pmsDemandDetailSourceRefReq.setOrderDeptName(pmsDemandDeptGoodsDetailReq.getOrderDeptName());//订货部门名称
        pmsDemandDetailSourceRefReq.setSkuCode(pmsApplyBillDetailPO.getSkuCode());//商品编码
        pmsDemandDetailSourceRefReq.setSkuName(pmsApplyBillDetailPO.getSkuName());//商品名称
        pmsDemandDetailSourceRefReq.setGoodsType(pmsApplyBillDetailPO.getGoodsType());//商品类型,1主品,2赠品
        pmsDemandDetailSourceRefReq.setAttributeCode(pmsApplyBillPO.getAttributeCode());//来源单订退货属性编码
        pmsDemandDetailSourceRefReq.setAttributeName(pmsApplyBillPO.getAttributeName());//来源单订退货属性名称
        pmsDemandDetailSourceRefReq.setSrcShippingWay(pmsApplyBillDetailPO.getShippingWay());//来源单据出货途径 0.采购 1.配送
        pmsDemandDetailSourceRefReq.setSrcShipperCode(pmsApplyBillDetailPO.getShipperCode());//来源单据出货方编码 1.申请类型=采购，供应商编码 2-申请类型=配送，部门档案（部门类型=配送中心 且 状态<>停用）
        pmsDemandDetailSourceRefReq.setSrcShipperName(pmsApplyBillDetailPO.getShipperName());//来源单据出货方名称

        pmsDemandDetailSourceRefReq.setApplyBillNo(pmsApplyBillDetailPO.getBillNo());//来源单号-申请单
        pmsDemandDetailSourceRefReq.setSrcInsideId(pmsApplyBillDetailPO.getInsideId());//来源单内序号
        pmsDemandDetailSourceRefReq.setSrcCustomerCode(pmsApplyBillPO.getCustomerCode());//来源客户编码
        pmsDemandDetailSourceRefReq.setSrcCustomerName(pmsApplyBillPO.getCustomerName());//来源客户名称
        pmsDemandDetailSourceRefReq.setSrcSendMode(pmsApplyBillPO.getSendMode());//来源单据送货方式 0-到店，1-到客户
        pmsDemandDetailSourceRefReq.setGoodsRemark(pmsApplyBillDetailPO.getRemark());//来源单商品备注
        pmsDemandDetailSourceRefReq.setSrcBuyerName(pmsApplyBillPO.getBuyerName());//来源客户联系人
        pmsDemandDetailSourceRefReq.setSrcBuyerTel(pmsApplyBillPO.getBuyerTel());//来源客户手机
        pmsDemandDetailSourceRefReq.setSrcBuyerAddr(pmsApplyBillPO.getBuyerAddr());//来源客户地址

        pmsDemandDetailSourceRefReq.setSrcDemandQty(pmsApplyBillDetailPO.getOrderQty());//来源需求数量
        pmsDemandDetailSourceRefReq.setSrcDemandOddQty(pmsApplyBillDetailPO.getOddQty());//需求零头数量
        pmsDemandDetailSourceRefReq.setSrcDemandWholeQty(pmsApplyBillDetailPO.getWholeAmount());//需求整件数量
        pmsDemandDetailSourceRefReq.setSrcDemandUnitRate(pmsApplyBillDetailPO.getUnitRate());//来源需求包装率

        pmsDemandDetailSourceRefReq.setSrcRefundReason(pmsApplyBillPO.getRefundReason());//来源退货原因编码
        pmsDemandDetailSourceRefReq.setSrcRefundReasonDesc(pmsApplyBillPO.getRefundReasonDesc());//来源退货原因名称

        pmsDemandDetailSourceRefReq.setSrcManageCategoryClass(pmsApplyBillDetailPO.getManageCategoryClass());//来源单据管理分类项编码
        pmsDemandDetailSourceRefReq.setSrcManageCategoryCode(pmsApplyBillDetailPO.getManageCategoryCode());//来源单据管理分类编码
        pmsDemandDetailSourceRefReq.setSrcManageCategoryName(pmsApplyBillDetailPO.getManageCategoryName());//来源单据管理分类名称

        pmsDemandDetailSourceRefReq.setPurchBatchNo(pmsApplyBillDetailPO.getPurchBatchNo());//采购批次

        pmsDemandDetailSourceRefReq.setSrcOrderPrice(pmsApplyBillDetailPO.getPurchTaxPrice());//来源订货价

        pmsDemandDetailSourceRefReq.setRemark(pmsApplyBillPO.getRemark());//来源单据备注

        pmsDemandDetailSourceRefReq.setResponseQty(pmsApplyBillDetailPO.getOrderQty());//响应数量
        pmsDemandDetailSourceRefReq.setResponseOddQty(pmsApplyBillDetailPO.getOddQty());//响应零头数量
        pmsDemandDetailSourceRefReq.setResponseWholeQty(pmsApplyBillDetailPO.getWholeAmount());//响应整件数量
        pmsDemandDetailSourceRefReq.setResponseUnitRate(pmsApplyBillDetailPO.getUnitRate());//响应订货包装率

        pmsDemandDeptGoodsDetailReq.getDemandDetailSourceList().add(pmsDemandDetailSourceRefReq);

        //写出货方 基于 出货途径+出货方 分组了，所有出货方只能是1个了
//        普通配送： 出货方-配送1个
//        直流：出货方-配送1个，出货方-采购1个 部门商品的直流供应商也只有一个
//        普通采购：出货方-配送0，出货方-采购多个变成1个

        //        普通配送：配转采，出货方-配送1个，出货方-采购多个
        String purchOrderDeptCode = pmsDemandDeptGoodsDetailReq.getOrderDeptCode();
        //配送
        if(ObjectUtils.equals(ShippingWayEnum.DELIVERY.getCode(),pmsApplyBillDetailPO.getShippingWay())
            //&& ObjectUtils.equals(DirectSignEnum.NOT_DIRECT.getCode(),pmsApplyBillDetailPO.getDirectSign())){
        ){
            PmsDemandDeliveryShipperReq pmsDemandDeliveryShipperReq;

            purchOrderDeptCode = pmsApplyBillDetailPO.getShipperCode();
            //如果合并出货方
            if(mergeFlag){
                pmsDemandDeliveryShipperReq = pmsDemandDeptGoodsDetailReq.getDemandDeliveryShipperList().get(0);
                pmsDemandDeliveryShipperReq.setDistMoney(pmsDemandDeliveryShipperReq.getDistMoney().add(pmsApplyBillDetailPO.getPurchMoney()));//配送金额
                pmsDemandDeliveryShipperReq.setDistTax(pmsDemandDeliveryShipperReq.getDistTax().add(pmsApplyBillDetailPO.getPurchTax()));//配送税金
                pmsDemandDeliveryShipperReq.setResponseQty(pmsDemandDeliveryShipperReq.getResponseQty().add(pmsApplyBillDetailPO.getOrderQty()));//响应数量

                BigDecimal deliveryUnitRate = pmsDemandDeliveryShipperReq.getDeliveryUnitRate();
                if(null == deliveryUnitRate){
                    deliveryUnitRate = BigDecimal.ONE;
                }
                BigDecimal oddQty = pmsDemandDeliveryShipperReq.getResponseQty().remainder(deliveryUnitRate);
                BigDecimal wholeQty = pmsDemandDeliveryShipperReq.getResponseQty().subtract(oddQty);//箱数

                pmsDemandDeliveryShipperReq.setDeliveryWholeQty(wholeQty);//整件数量
                pmsDemandDeliveryShipperReq.setDeliveryOddQty(oddQty);//零头数量
            }
            else{
                pmsDemandDeliveryShipperReq = new PmsDemandDeliveryShipperReq();
                pmsDemandDeliveryShipperReq.setInsideId(autoDemandInsideIdDTO.getDeliveryInsideId());//单内序号,前端生成，单据内唯一
                autoDemandInsideIdDTO.setDeliveryInsideId(autoDemandInsideIdDTO.getDeliveryInsideId() + 1);

                pmsDemandDeliveryShipperReq.setBillNo(pmsDemandGoodsDetailReq.getBillNo());
                pmsDemandDeliveryShipperReq.setPinsideId(pmsDemandDeptGoodsDetailReq.getInsideId());//上一级(pms_demand_dept_goods_detail)单内序号,前端生成，单据内唯一
                pmsDemandDeliveryShipperReq.setGoodsInsideId(pmsDemandGoodsDetailReq.getInsideId());//上上一级(pms_demand_goods_detail)单内序号,前端生成，单据内唯一
                pmsDemandDeliveryShipperReq.setStatus(1);//状态,0未选择, 1选中
                pmsDemandDeliveryShipperReq.setConvertFlag(convertFlag);//转单标识,0普通, 1配转采

                pmsDemandDeliveryShipperReq.setPurchBatchNo(pmsApplyBillDetailPO.getPurchBatchNo());//采购批次

                pmsDemandDeliveryShipperReq.setSkuCode(pmsApplyBillDetailPO.getSkuCode());//商品编码
                pmsDemandDeliveryShipperReq.setSkuName(pmsApplyBillDetailPO.getSkuName());//商品名称
                pmsDemandDeliveryShipperReq.setGoodsType(pmsApplyBillDetailPO.getGoodsType());//商品类型,1主品,2赠品

                pmsDemandDeliveryShipperReq.setOrderDeptCode(pmsDemandDeptGoodsDetailReq.getOrderDeptCode());//订货部门编码
                pmsDemandDeliveryShipperReq.setDistDeptCode(pmsApplyBillDetailPO.getShipperCode());//配送部门编码
                pmsDemandDeliveryShipperReq.setDistDeptName(pmsApplyBillDetailPO.getShipperName());//配送部门名称
                pmsDemandDeliveryShipperReq.setDirectSign(pmsApplyBillDetailPO.getDirectSign());//是否直流
                pmsDemandDeliveryShipperReq.setDeliveryUnitRate(pmsApplyBillDetailPO.getUnitRate());//配送包装率

                pmsDemandDeliveryShipperReq.setPromoteCode(pmsApplyBillDetailPO.getPromoteCode());//促销活动编码
                pmsDemandDeliveryShipperReq.setPromoteName(pmsApplyBillDetailPO.getPromoteName());//促销活动名称
                pmsDemandDeliveryShipperReq.setPromotePrice(pmsApplyBillDetailPO.getPromotePrice());//促销价
                pmsDemandDeliveryShipperReq.setLastPurchPrice(pmsApplyBillDetailPO.getLastPurchPrice());//最后进价(最后进价、部门商品档案进价、档案进价,商品一个字段返回)
                pmsDemandDeliveryShipperReq.setGoodsDistPrice(pmsApplyBillDetailPO.getGoodsDistPrice());//档案配送价（部门商品配送价，优先 没有取 档案配送价 商品逻辑）

//                pmsDemandDeliveryShipperReq.setDistDeptCostPrice();//配送部门成本价

                pmsDemandDeliveryShipperReq.setDistPriceBillNo("");//配送价格单编号

                pmsDemandDeliveryShipperReq.setDistDeptStockRealQty(pmsApplyBillDetailPO.getSendStockQty());//配送部门实际库存
                pmsDemandDeliveryShipperReq.setDistDeptStockAtpQty(pmsApplyBillDetailPO.getSendStockQty());//配送部门可用库存
                pmsDemandDeliveryShipperReq.setDistPrice(pmsApplyBillDetailPO.getPurchTaxPrice());//配送价格
                pmsDemandDeliveryShipperReq.setResponseQty(pmsApplyBillDetailPO.getOrderQty());//响应数量

                pmsDemandDeliveryShipperReq.setDistMoney(pmsApplyBillDetailPO.getPurchMoney());//配送金额
                pmsDemandDeliveryShipperReq.setDistTax(pmsApplyBillDetailPO.getPurchTax());//配送税金
                pmsDemandDeliveryShipperReq.setDeliveryWholeQty(pmsApplyBillDetailPO.getWholeAmount());//整件数量
                pmsDemandDeliveryShipperReq.setDeliveryOddQty(pmsApplyBillDetailPO.getOddQty());//零头数量

                if(ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),pmsApplyBillDetailPO.getDirectSign())){
                    pmsDemandDeliveryShipperReq.setDockCode("");//停靠点编码 -- 出货途径是配送且时直流的行 提交时会写入值
                    pmsDemandDeliveryShipperReq.setDockCode("");//停靠点编码 -- 出货途径是配送且时直流的行 提交时会写入值
                }
                pmsDemandDeptGoodsDetailReq.getDemandDeliveryShipperList().add(pmsDemandDeliveryShipperReq);
            }
            //追加追减 - 配转采
            if(ObjectUtils.equals(1,convertFlag)){
                PmsDemandDeliveryToPurchRefReq demandDeliveryToPurchRefReq = new PmsDemandDeliveryToPurchRefReq();
                demandDeliveryToPurchRefReq.setBillNo(pmsDemandDeptGoodsDetailReq.getBillNo());
                demandDeliveryToPurchRefReq.setDeliveryShipperInsideId(pmsDemandDeliveryShipperReq.getInsideId());
                demandDeliveryToPurchRefReq.setDeliveryToPurchInsideId(deliveryToPurchDeliverySourceResp.getDeliveryToPurchInsideId());
                demandDeliveryToPurchRefReq.setDeptGoodsInsideId(pmsDemandDeptGoodsDetailReq.getInsideId());
                demandDeliveryToPurchRefReq.setType(convertFlag);
            }
        }
        if((ObjectUtils.equals(ShippingWayEnum.DELIVERY.getCode(),pmsApplyBillDetailPO.getShippingWay())
                && ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),pmsApplyBillDetailPO.getDirectSign()))
                || ObjectUtils.equals(ShippingWayEnum.PURCHASE.getCode(),pmsApplyBillDetailPO.getShippingWay())){
            //直流 或 普通采购
            PmsDemandPurchShipperReq pmsDemandPurchShipperReq;
            //如果合并出货方
            if(mergeFlag){
                pmsDemandPurchShipperReq = pmsDemandDeptGoodsDetailReq.getDemandPurchShipperList().get(0);

                pmsDemandPurchShipperReq.setPurchMoney(pmsDemandPurchShipperReq.getPurchMoney().add(pmsApplyBillDetailPO.getPurchMoney()));//采购金额
                pmsDemandPurchShipperReq.setPurchTax(pmsDemandPurchShipperReq.getPurchTax().add(pmsApplyBillDetailPO.getPurchTax()));//采购税金
                pmsDemandPurchShipperReq.setPurchQty(pmsDemandPurchShipperReq.getPurchQty().add(pmsApplyBillDetailPO.getOrderQty()));//采购数量

                BigDecimal purchUnitRate = pmsDemandPurchShipperReq.getPurchUnitRate();
                if(null == purchUnitRate){
                    purchUnitRate = BigDecimal.ONE;
                }
                BigDecimal oddQty = pmsDemandPurchShipperReq.getPurchQty().remainder(purchUnitRate);
                BigDecimal wholeQty = pmsDemandPurchShipperReq.getPurchQty().subtract(oddQty);//箱数

                pmsDemandPurchShipperReq.setPurchWholeQty(wholeQty);//整件数量
                pmsDemandPurchShipperReq.setPurchOddQty(oddQty);//零头数量
            }
            else{
                pmsDemandPurchShipperReq = new PmsDemandPurchShipperReq();
                pmsDemandPurchShipperReq.setInsideId(autoDemandInsideIdDTO.getPurchInsideId());//单内序号,前端生成，单据内唯一
                autoDemandInsideIdDTO.setPurchInsideId(autoDemandInsideIdDTO.getPurchInsideId() + 1);

                pmsDemandPurchShipperReq.setBillNo(pmsDemandGoodsDetailReq.getBillNo());
                pmsDemandPurchShipperReq.setPinsideId(pmsDemandDeptGoodsDetailReq.getInsideId());//上一级(pms_demand_dept_goods_detail)单内序号,前端生成，单据内唯一
                pmsDemandPurchShipperReq.setGoodsInsideId(pmsDemandGoodsDetailReq.getInsideId());//上上一级(pms_demand_goods_detail)单内序号,前端生成，单据内唯一
                pmsDemandPurchShipperReq.setStatus(1);//状态,0未选择, 1选中
                pmsDemandPurchShipperReq.setConvertFlag(0);//转单标识,0普通, 1配转采

                if(ObjectUtils.equals(ShippingWayEnum.DELIVERY.getCode(),pmsApplyBillDetailPO.getShippingWay())
                        && ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),pmsApplyBillDetailPO.getDirectSign())){
                    pmsDemandPurchShipperReq.setOrderDeptCode(purchOrderDeptCode);//订货部门编码
                    pmsDemandPurchShipperReq.setSupplierCode(pmsApplyBillDetailPO.getDirectSupplierCode());//供应商编码
                    pmsDemandPurchShipperReq.setSupplierName(pmsApplyBillDetailPO.getDirectSupplierName());//供应商名称
                    pmsDemandPurchShipperReq.setContractNo(pmsApplyBillDetailPO.getDirectContractNo());//直流合同号
                }
                else{
                    pmsDemandPurchShipperReq.setOrderDeptCode(pmsDemandDeptGoodsDetailReq.getOrderDeptCode());//订货部门编码
                    pmsDemandPurchShipperReq.setSupplierCode(pmsApplyBillDetailPO.getShipperCode());//供应商编码
                    pmsDemandPurchShipperReq.setSupplierName(pmsApplyBillDetailPO.getShipperName());//供应商名称
                    pmsDemandPurchShipperReq.setContractNo(pmsApplyBillDetailPO.getContractNo());//合同号
                }
                pmsDemandPurchShipperReq.setSkuCode(pmsApplyBillDetailPO.getSkuCode());//商品编码
                pmsDemandPurchShipperReq.setSkuName(pmsApplyBillDetailPO.getSkuName());//商品名称
                pmsDemandPurchShipperReq.setGoodsType(pmsApplyBillDetailPO.getGoodsType());//商品类型,1主品,2赠品

                pmsDemandPurchShipperReq.setDirectSign(pmsApplyBillDetailPO.getDirectSign());//是否直流

                pmsDemandPurchShipperReq.setPromoteActivityCode(pmsApplyBillDetailPO.getPromoteCode());//促销活动编码
                pmsDemandPurchShipperReq.setPromoteActivityName(pmsApplyBillDetailPO.getPromoteName());//促销活动名称
                pmsDemandPurchShipperReq.setPromotePeriodPrice(pmsApplyBillDetailPO.getPromotePrice());//促销价
                pmsDemandPurchShipperReq.setLastPurchPrice(pmsApplyBillDetailPO.getLastPurchPrice());//最后进价(最后进价、部门商品档案进价、档案进价,商品一个字段返回)
                pmsDemandPurchShipperReq.setContractMaxPurchPrice(pmsApplyBillDetailPO.getContractMaxPurchPrice());//合同最高进价


                pmsDemandPurchShipperReq.setContractPurchPrice(pmsApplyBillDetailPO.getContractPurchPrice());//合同进价
                pmsDemandPurchShipperReq.setContractSpecialPrice(pmsApplyBillDetailPO.getContractSpecialPrice());//合同特供价


                pmsDemandPurchShipperReq.setPurchUnitRate(pmsApplyBillDetailPO.getUnitRate());//采购包装率

                pmsDemandPurchShipperReq.setPurchPrice(pmsApplyBillDetailPO.getPurchTaxPrice());//采购价格
                pmsDemandPurchShipperReq.setPurchQty(pmsApplyBillDetailPO.getOrderQty());//采购数量

                pmsDemandPurchShipperReq.setPurchMoney(pmsApplyBillDetailPO.getPurchMoney());//采购金额
                pmsDemandPurchShipperReq.setPurchTax(pmsApplyBillDetailPO.getPurchTax());//采购税金
                pmsDemandPurchShipperReq.setPurchWholeQty(pmsApplyBillDetailPO.getWholeAmount());//整件数量
                pmsDemandPurchShipperReq.setPurchOddQty(pmsApplyBillDetailPO.getOddQty());//零头数量



                pmsDemandPurchShipperReq.setDockCode("");//停靠点编码 -- 出货途径是配送且时直流的行 提交时会处理
                pmsDemandPurchShipperReq.setDockName("");//停靠点编码 -- 出货途径是配送且时直流的行 提交时会处理

                pmsDemandPurchShipperReq.setLastSupplierCode("");//最近一次供应商编码 配转采
                pmsDemandPurchShipperReq.setLastSupplierName("");//最近一次供应商名称 配转采

                pmsDemandPurchShipperReq.setMainSupplierMode(0);//是否主供应商,0否,1是 配转采

                pmsDemandDeptGoodsDetailReq.getDemandPurchShipperList().add(pmsDemandPurchShipperReq);
            }
        }

    }

    /**
     * 校验部门商品
     * @param autoApplyToDemandDTO
     * @param pmsApplyBillPO
     * @param pmsApplyBillDetailPO
     * @param pmsDemandDeptGoodsDetailReq
     * @return
     */
    private Boolean checkDeptGood(AutoApplyToDemandDTO autoApplyToDemandDTO, PmsApplyBillPO pmsApplyBillPO, PmsApplyBillDetailPO pmsApplyBillDetailPO, PmsDemandDeptGoodsDetailReq pmsDemandDeptGoodsDetailReq) {
        if(!ObjectUtils.equals(PmsDemanBillSourceEnum.AUTO.getCode(),autoApplyToDemandDTO.getBillSource())){
            return true;
        }
//            1、判断有效性，需要将订货申请行重新按订货申请的出货途径和出货方 匹配部门商品经营状态、流转途径 和订单/商品订货策略，判断该订货申请行是否有效
//            a.存在审批时满足 可要货，后续修改了配置，不可要货，或者不可向该出货方要货、或者直流属性发生了变化的-----则该申请行不再参与到需求单后续处理
        String key = pmsApplyBillPO.getDeptCode() + "_" + pmsApplyBillDetailPO.getSkuCode();
        GoodsQueryResp goodsQueryResp = autoApplyToDemandDTO.getGoodsQueryRespMap().get(key);
        ApplyGoodsDTO applyGoodsDTO = supplychainPmsBizRuleEngineService.checkGoodsBasic(goodsQueryResp);
        if(!applyGoodsDTO.getCheckSign()){
            pmsDemandDeptGoodsDetailReq.setResponseFailReason(applyGoodsDTO.getErrMsg());//响应失败原因
            return false;
        }

        //部门商品不是直流，订货申请是直流
        if(ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),pmsApplyBillDetailPO.getDirectSign())
                &&  ObjectUtils.notEqual(DirectSignEnum.DIRECT.getCode(),goodsQueryResp.getGoodsInfo().getDirectFlag())){
            String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B022.getErrorMsg(), new String[]{pmsApplyBillPO.getBillNo(), pmsApplyBillDetailPO.getSkuCode()}).getMessage();
            pmsDemandDeptGoodsDetailReq.setResponseFailReason(message);//响应失败原因
            return false;
        }
        else if(ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),pmsApplyBillDetailPO.getDirectSign())
                &&  ObjectUtils.equals(DirectSignEnum.DIRECT.getCode(),goodsQueryResp.getGoodsInfo().getDirectFlag())
                && !pmsApplyBillDetailPO.getDirectSupplierCode().equals(goodsQueryResp.getGoodsInfo().getDirectSupplierCode())){
            // 直流，判断直流供应商
            String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B023.getErrorMsg(), new String[]{pmsApplyBillPO.getBillNo(),
                    pmsApplyBillDetailPO.getSkuCode(),pmsApplyBillDetailPO.getDirectSupplierCode(),goodsQueryResp.getGoodsInfo().getDirectSupplierCode()}).getMessage();
            pmsDemandDeptGoodsDetailReq.setResponseFailReason(message);//响应失败原因
            return false;
        }

        ManageAndCirculationResp.CirculationMode circulationMode = autoApplyToDemandDTO.getManageAndCirculation().getCirculationMap().get(goodsQueryResp.getGoodsInfo().getCirculationModeCode());
        ManageAndCirculationResp.WorkState workState = autoApplyToDemandDTO.getManageAndCirculation().getManageStateMap().get(goodsQueryResp.getGoodsInfo().getWorkStateCode());

        if(null == circulationMode ){
            String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B025.getErrorMsg(), new String[]{pmsApplyBillPO.getBillNo(),
                    pmsApplyBillDetailPO.getSkuCode()}).getMessage();
            pmsDemandDeptGoodsDetailReq.setResponseFailReason(message);//响应失败原因
            return false;
        }

        if(null == workState){
            String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B024.getErrorMsg(), new String[]{pmsApplyBillPO.getBillNo(),
                    pmsApplyBillDetailPO.getSkuCode()}).getMessage();
            pmsDemandDeptGoodsDetailReq.setResponseFailReason(message);//响应失败原因
            return false;
        }

        //公共逻辑 是否允许进货，是否允许进货退货
        if(!(ObjectUtils.equals(1,workState.getAllowPurchaseFlag()) && ObjectUtils.equals(1,workState.getAllowPurchaseBackFlag()))){
            String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B026.getErrorMsg(), new String[]{pmsApplyBillPO.getBillNo(),
                    pmsApplyBillDetailPO.getSkuCode()}).getMessage();
            pmsDemandDeptGoodsDetailReq.setResponseFailReason(message);//响应失败原因
            return false;
        }

        //判断出货途径 -配送
        if(ObjectUtils.equals(ShippingWayEnum.DELIVERY.getCode(),pmsApplyBillDetailPO.getShippingWay())){
            if(!(ObjectUtils.equals(1,circulationMode.getAllowFromDistributionFlag()) && ObjectUtils.equals(1,circulationMode.getAllowBackDistributionFlag()))){
                String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B028.getErrorMsg(), new String[]{pmsApplyBillPO.getBillNo(),
                        pmsApplyBillDetailPO.getSkuCode()}).getMessage();
                pmsDemandDeptGoodsDetailReq.setResponseFailReason(message);//响应失败原因
                return false;
            }
        }
        else{
            //采购
            if(!(ObjectUtils.equals(1,circulationMode.getAllowBackSupplierFlag()) && ObjectUtils.equals(1,circulationMode.getAllowFromSupplierFlag()))){
                String message = MessageFormatter.arrayFormat(PmsErrorCodeEnum.SC_PMS_002_B027.getErrorMsg(), new String[]{pmsApplyBillPO.getBillNo(),
                        pmsApplyBillDetailPO.getSkuCode()}).getMessage();
                pmsDemandDeptGoodsDetailReq.setResponseFailReason(message);//响应失败原因
                return false;
            }
        }

        //TODO 商品订货策略
        return true;
    }

    /**
     * 基于明细重新加工汇总数量
     * @param demandGoodsDetailList
     * @param billSource
     */
    private void rerunDemandGoodsDetail(List<PmsDemandGoodsDetailReq> demandGoodsDetailList,Integer billSource){
        for (PmsDemandGoodsDetailReq pmsDemandGoodsDetailReq : demandGoodsDetailList) {

            BigDecimal unitRate = pmsDemandGoodsDetailReq.getUnitRate();
            if(null == unitRate){
                unitRate = BigDecimal.ONE;
            }
            BigDecimal oddQty = pmsDemandGoodsDetailReq.getDemandQty().remainder(unitRate);
            BigDecimal wholeQty = pmsDemandGoodsDetailReq.getDemandQty().subtract(oddQty);//箱数

            pmsDemandGoodsDetailReq.setDemandWholeQty(wholeQty);//需求整件数量 需要行末尾重新计算
            pmsDemandGoodsDetailReq.setDemandOddQty(oddQty);//需求零头数量 需要行末尾重新计算
            pmsDemandGoodsDetailReq.setResponseOddQty(oddQty);//响应零头数量 需要行末尾重新计算
            pmsDemandGoodsDetailReq.setResponseWholeQty(wholeQty);//响应整件数量 需要行末尾重新计算

            //商品 追加追减重算商品 响应/需求 数量、零头数量、整件数量、金额、税金  TODO 王国灏
            if(ObjectUtils.equals(PmsDemanBillSourceEnum.ZHUI_JIA_JIAN.getCode(),billSource)){

            }

            for (PmsDemandDeptGoodsDetailReq pmsDemandDeptGoodsDetailReq : pmsDemandGoodsDetailReq.getDemandDeptGoodsDetailList()) {
                unitRate = pmsDemandGoodsDetailReq.getUnitRate();
                if(null == unitRate){
                    unitRate = BigDecimal.ONE;
                }
                BigDecimal oddQtyDept = pmsDemandDeptGoodsDetailReq.getDemandQty().remainder(unitRate);
                BigDecimal wholeQtyDept = pmsDemandDeptGoodsDetailReq.getDemandQty().subtract(oddQtyDept);//箱数
                pmsDemandDeptGoodsDetailReq.setDemandWholeQty(wholeQtyDept);//需求整件数量 需要行末尾重新计算
                pmsDemandDeptGoodsDetailReq.setDemandOddQty(oddQtyDept);//需求零头数量 需要行末尾重新计算
                pmsDemandDeptGoodsDetailReq.setResponseOddQty(oddQtyDept);//响应零头数量 需要行末尾重新计算
                pmsDemandDeptGoodsDetailReq.setResponseWholeQty(wholeQtyDept);//响应整件数量 需要行末尾重新计算

                //商品，部门商品，出货方、管理关系表，配转采标记的不用处理convertFlag:转单标识,0普通, 1配转采 追加追减重算商品 响应/需求 数量、零头数量、整件数量、金额、税金 TODO 王国灏
                if(ObjectUtils.equals(PmsDemanBillSourceEnum.ZHUI_JIA_JIAN.getCode(),billSource)){

                }
            }
        }
    }

    /**
     * 需求单自动组装 商品
     * @param pmsDemandGoodsDetailReq
     * @param pmsApplyBillDetailPO
     */
    private void assemblyDemandGoodsDetail(PmsDemandGoodsDetailReq pmsDemandGoodsDetailReq,PmsApplyBillDetailPO pmsApplyBillDetailPO){
        pmsDemandGoodsDetailReq.setSkuCode(pmsApplyBillDetailPO.getSkuCode());//商品编码
        pmsDemandGoodsDetailReq.setSkuName(pmsApplyBillDetailPO.getSkuName());//商品名称
        pmsDemandGoodsDetailReq.setSkuModel(pmsApplyBillDetailPO.getSkuModel());//商品规格
        pmsDemandGoodsDetailReq.setGoodsNo(pmsApplyBillDetailPO.getGoodsNo());//商品货号
        pmsDemandGoodsDetailReq.setGoodsType(pmsApplyBillDetailPO.getGoodsType());//商品类型,1主品,2赠品
        pmsDemandGoodsDetailReq.setBrandCode(pmsApplyBillDetailPO.getBrandCode());//品牌编码
//        pmsDemandGoodsDetailReq.setBrandName(pmsApplyBillDetailPO.getb);//品牌名称
        pmsDemandGoodsDetailReq.setCategoryCode(pmsApplyBillDetailPO.getCategoryCode());//品类编码
        pmsDemandGoodsDetailReq.setCategoryName(pmsApplyBillDetailPO.getCategoryName());//品类名称
        pmsDemandGoodsDetailReq.setCategoryCodeAll(pmsApplyBillDetailPO.getCategoryCodeFullPath());//全路径品类编码,英文逗号分割
        pmsDemandGoodsDetailReq.setSaleMode(pmsApplyBillDetailPO.getSaleMode());//销售模式 1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码
        pmsDemandGoodsDetailReq.setUnit(pmsApplyBillDetailPO.getBasicUnit());//单位
        pmsDemandGoodsDetailReq.setUnitRate(pmsApplyBillDetailPO.getGoodsUnitRate());//商品包装率 -- 商品档案
        pmsDemandGoodsDetailReq.setUomAttr(NumberUtil.getInteger(pmsApplyBillDetailPO.getUomAttr()));//计量属性 0：普通 1：计量 2：称重
        pmsDemandGoodsDetailReq.setInputTaxRate(pmsApplyBillDetailPO.getInputTaxRate());//销项税率，13%存13
        pmsDemandGoodsDetailReq.setOutputTaxRate(pmsApplyBillDetailPO.getOutputTaxRate());//进项税率，13%存13
        pmsDemandGoodsDetailReq.setPackageUnit(pmsApplyBillDetailPO.getWholeUnit());//整件单位
        pmsDemandGoodsDetailReq.setBarcode(pmsApplyBillDetailPO.getBarcode());

        pmsDemandGoodsDetailReq.setDemandTax(pmsApplyBillDetailPO.getPurchTax());//需求金额税金
        pmsDemandGoodsDetailReq.setDemandMoney(pmsApplyBillDetailPO.getPurchMoney());//需求金额

        pmsDemandGoodsDetailReq.setDemandQty(pmsApplyBillDetailPO.getOrderQty());//需求数量
        pmsDemandGoodsDetailReq.setResponseQty(pmsApplyBillDetailPO.getOrderQty());//响应数量

        pmsDemandGoodsDetailReq.setDemandWholeQty(BigDecimal.ZERO);//需求整件数量 需要行末尾重新计算
        pmsDemandGoodsDetailReq.setDemandOddQty(BigDecimal.ZERO);//需求零头数量 需要行末尾重新计算
        pmsDemandGoodsDetailReq.setResponseOddQty(BigDecimal.ZERO);//响应零头数量 需要行末尾重新计算
        pmsDemandGoodsDetailReq.setResponseWholeQty(BigDecimal.ZERO);//响应整件数量 需要行末尾重新计算


        pmsDemandGoodsDetailReq.setResponseMoney(pmsApplyBillDetailPO.getPurchMoney());//响应金额
        pmsDemandGoodsDetailReq.setResponseTax(pmsApplyBillDetailPO.getPurchTax());//响应金额税金


    }

    /**
     * 订货申请转换需求单单据流向
     * @param pmsDemandBillReq
     * @param pmsApplyBillPO
     */
    private void applyCateToDemandBillDirection(PmsDemandBillReq pmsDemandBillReq,PmsApplyBillPO pmsApplyBillPO){
        if(ApplyCateEnum.REPLENISHMENT.getCode().equals(pmsApplyBillPO.getApplyCate())){
            pmsDemandBillReq.setBillDirection(1);
        }
        else{
            pmsDemandBillReq.setBillDirection(-1);
        }
    }

    /**
     * 过滤不满足条件的订货申请商品行
     * @param
     * @return
     */
    private AutoApplyToDemandDTO checkFilterAutoApplyToDemand(AutoApplyToDemandReq autoApplyToDemandReq) {
        //        2、排除订货属性是紧急 的行，紧急的需要手工调需求生成；排除 追加、追减的行；
//        3、取订货申请行为待提单状态的行
//        4、该些行捞取后匹配需求处理策略 判断是否参与生成需求单
//        a.首先判断是否在自动需求排除范围规则里的品，如果是，则对应订货申请行不参与自动需求单
//        b.针对不在排除规则的，则判断是否满足自动生成需求单规则。
//        获取订货申请行经营状态、流转途径、订退货属性、单据类别、出货途径、是否直流、订货部门、配送部门，如果与需求单规则任意一组存在交集，则参与自动需求；
//
//        否则不参与。
        Map<String,String> deptMap = new HashMap<>();
        List<PmsApplyBillDetailPO> applyBillDetailList = new ArrayList<>();
        Map<String,PmsApplyBillPO> applyBillMap = new HashMap<>();
        for (PmsApplyBillPO pmsApplyBillPO : autoApplyToDemandReq.getApplyBillList()) {
            //排除订货属性是紧急 的行，紧急的需要手工调需求生成；排除 追加、追减
            if(pmsApplyBillPO.getAttributeCode().equals(OrderAttributeEnum.ZHUI_JIA) || pmsApplyBillPO.getAttributeCode().equals(OrderAttributeEnum.ZHUI_JIAN)
                    || pmsApplyBillPO.getAttributeCode().equals(OrderAttributeEnum.JIN_JI)){
                continue;
            }
            applyBillMap.put(pmsApplyBillPO.getBillNo(),pmsApplyBillPO);
            deptMap.put(pmsApplyBillPO.getDeptCode(),pmsApplyBillPO.getDeptCode());
        }

        List<String> deptCodeList = new ArrayList<>();
        List<String> deptGroupCodeList = new ArrayList<>();
        // 获取部门上级店组群
        QueryBatchDeptListReq req = QueryBatchDeptListReq.builder()
                .classCode(GroupDeptEnum.CONTROL_GROUP.getCode())
                .deptCodeList(new ArrayList<>(deptMap.keySet()))
                .build();
        List<QueryBatchDeptListResp.Rows> groupList = baseDataSystemFeignClient.queryUpDeptListBatch(req).getRows();
        Map<String,List<String>> deptGroupMap = new HashMap<>();
        Map<String,String> groupMap = new HashMap<>();

        for (QueryBatchDeptListResp.Rows row : groupList) {

            deptCodeList.add(row.getCode());
            List<String> groupCodelist = new ArrayList<>();
            for (QueryBatchDeptListResp.DeptGroup deptGroup : row.getDeptGroupList()) {
                groupMap.put(deptGroup.getCode(),deptGroup.getCode());
                groupCodelist.add(deptGroup.getCode());
            }
            deptGroupMap.put(row.getCode(),groupCodelist);
        }
        deptGroupCodeList.addAll(new ArrayList<>(deptGroupMap.keySet()));


        MdDemandStrategyExcludePageQueryReq mdDemandStrategyExcludePageQueryReq = new MdDemandStrategyExcludePageQueryReq();
        mdDemandStrategyExcludePageQueryReq.setPageSize(10000L);
        PageResult<MdDemandStrategyExcludeResponseDTO> mdDemandStrategyExcludeResponseDTOPageResult = mdDemandStrategyDomainService.pageQueryExclude(mdDemandStrategyExcludePageQueryReq);
        //
        List<String> excludeGoodsList = new ArrayList<>();
        List<String> excludeCategoryCodeList = new ArrayList<>();
        for (MdDemandStrategyExcludeResponseDTO row : mdDemandStrategyExcludeResponseDTOPageResult.getRows()) {
            //类型,1:品类、2:商品
            if(ObjectUtils.equals(1,row.getType())){
                excludeCategoryCodeList.add(row.getCode());
            }
            else{
                excludeGoodsList.add(row.getCode());
            }
        }

        List<MdDemandStrategyAutoMappingGroupDTO> queryDemandStrategyAutoList = new ArrayList<>();
        for (PmsApplyBillDetailPO pmsApplyBillDetailPO : autoApplyToDemandReq.getApplyBillDetailList()) {
            //排除订货属性是紧急 的行，紧急的需要手工调需求生成；排除 追加、追减的行；
            if(!applyBillMap.containsKey(pmsApplyBillDetailPO.getBillNo())){
                continue;
            }

            //取订货申请行为待提单状态的行
            if(ObjectUtils.notEqual(1,pmsApplyBillDetailPO.getStatus())){
                continue;
            }

            //排除商品、品类
            //排除商品
            if(excludeGoodsList.contains(pmsApplyBillDetailPO.getSkuCode())){
                continue;
            }
            //排除品类
            String categoryCodeAll = pmsApplyBillDetailPO.getCategoryCodeFullPath();
            if(StringUtils.isNotEmpty(categoryCodeAll)){
                String[] split = categoryCodeAll.split(",");
                if(split.length > 0){
                    List<String> list = Arrays.asList(split);
                    list.retainAll(excludeCategoryCodeList);
                    if(CollectionUtils.isNotEmpty(list)){
                        continue;
                    }
                }
            }
            PmsApplyBillPO pmsApplyBillPO = applyBillMap.get(pmsApplyBillDetailPO.getBillNo());

            MdDemandStrategyAutoMappingGroupDTO demandStrategyAutoMappingGroupDTO = new MdDemandStrategyAutoMappingGroupDTO();
            String bizBillNo = pmsApplyBillDetailPO.getBillNo() + "_" + pmsApplyBillDetailPO.getInsideId();
            demandStrategyAutoMappingGroupDTO.setBizBillNo(bizBillNo);
            demandStrategyAutoMappingGroupDTO.getOperationStatusList().add(pmsApplyBillDetailPO.getWorkStatusCode());
            demandStrategyAutoMappingGroupDTO.getCirculationChannelList().add(pmsApplyBillDetailPO.getCirculationModeCode());
            demandStrategyAutoMappingGroupDTO.getOrderReturnAttributeList().add(pmsApplyBillPO.getAttributeCode());
            if(ObjectUtils.equals(ApplyCateEnum.REPLENISHMENT.getCode(),pmsApplyBillPO.getApplyCate())){
                demandStrategyAutoMappingGroupDTO.getBillDirectionList().add(MdDocumentTypeEnum.ORDER.getCode().toString());
            }
            else if(ObjectUtils.equals(ApplyCateEnum.RETURNS.getCode(),pmsApplyBillPO.getApplyCate())){
                demandStrategyAutoMappingGroupDTO.getBillDirectionList().add(MdDocumentTypeEnum.RETURN.getCode().toString());
            }
            else{
                continue;
            }

            demandStrategyAutoMappingGroupDTO.getShippingChannelList().add(pmsApplyBillDetailPO.getOrderRoute().toString());
            demandStrategyAutoMappingGroupDTO.getIsDirectFlowList().add(pmsApplyBillDetailPO.getDirectSign().toString());
            demandStrategyAutoMappingGroupDTO.getDeptCodeList().add(pmsApplyBillPO.getDeptCode());
            demandStrategyAutoMappingGroupDTO.getDeptGroupCodeList().addAll(deptGroupMap.get(pmsApplyBillPO.getDeptCode()));
            if(ObjectUtils.equals(ShippingWayEnum.DELIVERY.getCode(),pmsApplyBillDetailPO.getShippingWay())){
                demandStrategyAutoMappingGroupDTO.getWhCodeList().add(pmsApplyBillDetailPO.getShipperCode());
            }

            queryDemandStrategyAutoList.add(demandStrategyAutoMappingGroupDTO);
            applyBillDetailList.add(pmsApplyBillDetailPO);
        }

        List<MdDemandStrategyAutoMappingGroupDTO> mdDemandStrategyAutoMappingGroupDTOS = mdDemandStrategyDomainService.queryStrategyGroups(queryDemandStrategyAutoList);
        Map<String, MdDemandStrategyAutoMappingGroupDTO> mdDemandStrategyAutoMappingGroupDTOMap = mdDemandStrategyAutoMappingGroupDTOS.parallelStream().collect(Collectors.toMap(MdDemandStrategyAutoMappingGroupDTO::getBizBillNo, Function.identity()));

        //
        List<PmsApplyBillPO> applyBillResultList = new ArrayList<>();
        List<PmsApplyBillDetailPO> applyBillDetailResultList = new ArrayList<>();
        Map<String,PmsApplyBillPO> applyBillResultMap = new HashMap<>();
        for (PmsApplyBillDetailPO pmsApplyBillDetailPO : applyBillDetailList) {
            String bizBillNo = pmsApplyBillDetailPO.getBillNo() + "_" + pmsApplyBillDetailPO.getInsideId();
            if(mdDemandStrategyAutoMappingGroupDTOMap.containsKey(bizBillNo)){
                applyBillDetailResultList.add(pmsApplyBillDetailPO);
                PmsApplyBillPO pmsApplyBillPO = applyBillMap.get(pmsApplyBillDetailPO.getBillNo());
                applyBillResultMap.put(pmsApplyBillDetailPO.getBillNo(),pmsApplyBillPO);


            }
        }
        applyBillResultList.addAll(new ArrayList<>(applyBillResultMap.values()));

        AutoApplyToDemandDTO autoApplyToDemandDTO = new AutoApplyToDemandDTO();
        autoApplyToDemandDTO.setApplyBillList(applyBillResultList);
        autoApplyToDemandDTO.setApplyBillMap(applyBillResultMap);
        autoApplyToDemandDTO.setApplyBillDetailList(applyBillDetailResultList);
        autoApplyToDemandDTO.setBillSource(1);

        return autoApplyToDemandDTO;
    }
}
