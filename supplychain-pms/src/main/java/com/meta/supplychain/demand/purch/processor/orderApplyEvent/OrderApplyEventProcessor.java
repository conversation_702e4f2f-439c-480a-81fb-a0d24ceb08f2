package com.meta.supplychain.demand.purch.processor.orderApplyEvent;

import com.meta.supplychain.entity.bo.OrderApplyEventBO;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyBillResp;
import com.meta.supplychain.enums.OrderApplyStatusEventEnum;

import java.util.List;

public abstract class OrderApplyEventProcessor {

    public abstract List<ApplyBillResp> doHandle(OrderApplyEventBO<?> eventBO);

    public abstract OrderApplyStatusEventEnum getEventType();
}
