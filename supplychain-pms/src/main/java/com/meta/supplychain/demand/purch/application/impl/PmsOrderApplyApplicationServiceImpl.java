package com.meta.supplychain.demand.purch.application.impl;

import cn.linkkids.ageiport.client.AgeiTaskClient;
import cn.linkkids.ageiport.params.ExportDataParams;
import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.json.Jsons;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.lock.LockManager;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.ageiport.common.utils.CollectionUtils;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.meta.supplychain.common.component.service.impl.commonbiz.CommonFranchiseService;
import com.meta.supplychain.common.component.service.intf.ISupplychainBizGoodsRuleService;
import com.meta.supplychain.common.component.service.intf.ISupplychainPmsBizRuleEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.constants.LockConstants;
import com.meta.supplychain.convert.pms.OrderApplyBillConvert;
import com.meta.supplychain.convert.pms.OrderApplyBillDetailConvert;
import com.meta.supplychain.demand.purch.application.intf.PmsOrderApplyApplicationService;
import com.meta.supplychain.demand.purch.domain.intf.IWmsAdjustDeliveryOrderService;
import com.meta.supplychain.demand.purch.domain.intf.PmsAcceptDomainService;
import com.meta.supplychain.demand.purch.domain.intf.PmsDemandDomainService;
import com.meta.supplychain.demand.purch.domain.intf.PmsOrderApplyDomainService;
import com.meta.supplychain.demand.purch.domain.intf.PmsPurchaseOrderDomainService;
import com.meta.supplychain.demand.purch.enums.BillOperateTypeEnum;
import com.meta.supplychain.demand.purch.event.OrderApplyAuditEvent;
import com.meta.supplychain.demand.purch.processor.export.PmsOrderApplyProcessor;
import com.meta.supplychain.demand.purch.processor.export.PmsOrderApplyRefundProcessor;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractSkuInfo;
import com.meta.supplychain.entity.dto.md.component.goodsrule.DemandBatchRecordGoodsDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.DeptGoodsDeliveryPriceDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.DeptGoodsDeliveryPriceQueryDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.DeptGoodsPurchPriceDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.DeptGoodsPurchPriceQueryDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GeneratePurchBatchDTO;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.ValidGoodsInfo;
import com.meta.supplychain.entity.dto.pms.demand.AutoApplyToDemandDTO;
import com.meta.supplychain.entity.dto.pms.req.accept.QueryAcceptDTO;
import com.meta.supplychain.entity.dto.pms.req.addReduce.PmsOrderAllocateDetail;
import com.meta.supplychain.entity.dto.pms.req.addReduce.PmsOrderAllocateReq;
import com.meta.supplychain.entity.dto.pms.req.addReduce.PmsOrderAllocateSubmitReq;
import com.meta.supplychain.entity.dto.pms.req.apply.ApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.ApplyBillDetailDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.ApplyBillGoodsDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.ApplyGoodsInfo;
import com.meta.supplychain.entity.dto.pms.req.apply.ApplyPriceDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.BatchApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.CancelApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.CancelApplyDetail4DemandDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.LadingBillWriteBackDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryAddReduceDetailReq;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryApplyBillDetailReq;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryApplyOrder4AppDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryDemandApplyDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryDetailByInsideIdReq;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryOrder4PrintReq;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryUnfinishedApplyDTO;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchaseBillAdjust4AsReq;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchaseByDeliveryQryReq;
import com.meta.supplychain.entity.dto.pms.resp.ApplyBillDemandResp;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyBillResp;
import com.meta.supplychain.entity.dto.pms.resp.addReduce.PmsAddReduceDetailResp;
import com.meta.supplychain.entity.dto.pms.resp.addReduce.PmsAddReduceSource;
import com.meta.supplychain.entity.dto.pms.resp.addReduce.PmsOrderAllocateOrderInfo;
import com.meta.supplychain.entity.dto.pms.resp.addReduce.PmsOrderAllocateResp;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyPriceResp;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyStatisticResp;
import com.meta.supplychain.entity.dto.pms.resp.apply.QueryOrder4PrintResp;
import com.meta.supplychain.entity.dto.pms.resp.apply.ShipperInfo;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchBillOptResp;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchaseDeliverySkuResp;
import com.meta.supplychain.entity.dto.stock.resp.BatchRecordResp;
import com.meta.supplychain.entity.dto.wds.req.WdDeliveryBillQueryListBatchReq;
import com.meta.supplychain.entity.dto.wds.req.WdDeliveryBillQueryListBatchReqInner;
import com.meta.supplychain.entity.dto.wds.req.WdsDeliveryOrderAdjustBaseReq;
import com.meta.supplychain.entity.dto.wds.req.WdsDeliveryOrderAdjustGoodsReq;
import com.meta.supplychain.entity.dto.wds.resp.WdsDeliveryOrderAdjustResp;
import com.meta.supplychain.entity.po.pms.PmsApplyBillDetailPO;
import com.meta.supplychain.entity.po.pms.PmsApplyBillPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import com.meta.supplychain.enums.AddReduceTypeEnum;
import com.meta.supplychain.enums.OrderAttributeEnum;
import com.meta.supplychain.enums.pms.BillDetailStateEnum;
import com.meta.supplychain.enums.pms.BillStateEnum;
import com.meta.supplychain.enums.pms.DirectSignEnum;
import com.meta.supplychain.enums.pms.PmsDemanBillSourceEnum;
import com.meta.supplychain.enums.pms.PmsErrorCodeEnum;
import com.meta.supplychain.enums.pms.ShippingWayEnum;
import com.meta.supplychain.enums.wds.WDDeliveryOrderBillStatusEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsApplyBillDetailRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsApplyBillRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillRepository;
import com.meta.supplychain.util.DateUtil;
import com.meta.supplychain.util.UserUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import com.metadata.idaas.client.model.LoginUserDTO;
import com.metadata.idaas.client.util.ClientIdentUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/03/30 01:56
 **/
@Service
@Slf4j
public class PmsOrderApplyApplicationServiceImpl implements PmsOrderApplyApplicationService {

    @Resource
    private UserUtil userUtil;

    @Autowired
    private PmsAcceptDomainService pmsAcceptDomainService;

    @Autowired
    private PmsOrderApplyDomainService pmsOrderApplyDomainService;

    @Autowired
    private PmsDemandDomainService pmsDemandDomainService;

    @Autowired
    private PmsApplyBillRepositoryService pmsApplyBillRepositoryService;

    @Autowired
    private PmsApplyBillDetailRepositoryService pmsApplyBillDetailRepositoryService;

    @Autowired
    private IWdDeliveryBillRepository iWdDeliveryBillRepository;

    @Autowired
    private ISupplychainBizGoodsRuleService iSupplychainBizGoodsRuleService;

    @Autowired
    private ISupplychainPmsBizRuleEngineService iSupplychainPmsBizRuleEngineService;

    @Autowired
    private PmsPurchaseOrderDomainService pmsPurchaseOrderDomainService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private IWmsAdjustDeliveryOrderService iWmsAdjustDeliveryOrderService;

    @Autowired
    private CommonFranchiseService commonFranchiseService;

    OrderApplyBillConvert orderApplyBillConvert = OrderApplyBillConvert.MAPPER;

    OrderApplyBillDetailConvert orderApplyBillDetailConvert = OrderApplyBillDetailConvert.MAPPER;

    @Resource
    private LockManager lockManager;

    @Autowired
    private ICommonStockService iCommonStockService;

    private PmsDemandDomainService getPmsDemandDomainService() {
        PmsDemandDomainService pmsDemandDomainService = SpringContextUtil.getApplicationContext().getBean(PmsDemandDomainService.class);
        return pmsDemandDomainService;
    }

    @Override
    public Result<ApplyBillResp> createOrderApply(ApplyBillDTO applyBillDTO) {
        // 分布式锁控制 防并发修改导致数量错误
        boolean lock = lockManager.tryLock(LockConstants.PMS_ORDER_APPLY_LOCK + applyBillDTO.getBillNo(), 10, TimeUnit.MINUTES);
        if (!lock) {
            return Results.ofCommonError(PmsErrorCodeEnum.SC_PMS_004_P021);
        }
        try {
            //todo 1.业务参数校验(包含价格查询校验)
            pmsOrderApplyDomainService.checkApply(applyBillDTO, BillOperateTypeEnum.NEW);

            // 单据唯一校验(数据库)
            PmsApplyBillPO applyBillPO = PmsApplyBillPO.builder()
                    .billNo(applyBillDTO.getBillNo()).build();
            OpInfo opInfo = userUtil.getOpInfoWithThrow();
            try {
                applyBillPO.setCreateCode(opInfo.getOperatorCode());
                applyBillPO.setCreateName(opInfo.getOperatorName());
                pmsApplyBillRepositoryService.save(applyBillPO);
            } catch (Exception e) {
                //todo 构造错误信息
                return Results.ofCommonError(PmsErrorCodeEnum.SC_PMS_001_P012);
            }

            //构造参数落库
            // 入参转PO
            // 落库
            pmsOrderApplyDomainService.dataPersistence(applyBillDTO, applyBillPO.getId());

            //推审核 同步
            applicationEventPublisher.publishEvent(new OrderApplyAuditEvent(
                    this, applyBillDTO.getTargetState(),
                    Lists.newArrayList(applyBillDTO.getBillNo())));
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建订单申请失败", e);
            return Results.ofCommonError(PmsErrorCodeEnum.SC_PMS_001_U001);
        } finally {
            lockManager.unlock(LockConstants.PMS_ORDER_APPLY_LOCK + applyBillDTO.getBillNo());
        }
        return Results.ofSuccess();
    }

    @Override
    public Result<ApplyBillResp> updateOrderApply(ApplyBillDTO applyBillDTO) {
        // 分布式锁控制 防并发修改导致数量错误
        boolean lock = lockManager.tryLock(LockConstants.PMS_ORDER_APPLY_LOCK + applyBillDTO.getBillNo(), 10, TimeUnit.MINUTES);
        if (!lock) {
            return Results.ofCommonError(PmsErrorCodeEnum.SC_PMS_004_P021);
        }
        try {
            OpInfo opInfo = userUtil.getOpInfoWithThrow();
            //业务参数校验(包含价格查询校验)
            pmsOrderApplyDomainService.checkApply(applyBillDTO, BillOperateTypeEnum.UPDATE);
            applyBillDTO.setUpdateTime(LocalDateTime.now());
            applyBillDTO.setUpdateCode(opInfo.getOperatorCode());
            applyBillDTO.setUpdateName(opInfo.getOperatorName());

            //构造参数落库
            pmsOrderApplyDomainService.dataPersistence(applyBillDTO, applyBillDTO.getId());

            //推审核事件
            applicationEventPublisher.publishEvent(new OrderApplyAuditEvent(
                    this, applyBillDTO.getTargetState(),
                    Lists.newArrayList(applyBillDTO.getBillNo())));
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新订单申请失败", e);
            return Results.ofCommonError(PmsErrorCodeEnum.SC_PMS_001_U001);
        } finally {
            lockManager.unlock(LockConstants.PMS_ORDER_APPLY_LOCK + applyBillDTO.getBillNo());
        }
        return Results.ofSuccess();
    }

    @Override
    public Result updateAuditOrderApply(ApplyBillDTO applyBillDTO) {
        // 分布式锁控制 防并发修改导致数量错误
        boolean lock = lockManager.tryLock(LockConstants.PMS_ORDER_APPLY_LOCK + applyBillDTO.getBillNo(), 10, TimeUnit.MINUTES);
        if (!lock) {
            return Results.ofCommonError(PmsErrorCodeEnum.SC_PMS_004_P021);
        }

        try {
            Result checkRepeatGoods = pmsOrderApplyDomainService.checkRepeatGoods(applyBillDTO.getBillDetails());
            if (!checkRepeatGoods.isSuccess()) {
                return checkRepeatGoods;
            }
            ;
            //业务校验 (单据是否存在&状态是否可修改&不可新增商品只可修改数量)
            Result result = pmsOrderApplyDomainService.checkUpdateAudit(applyBillDTO);
            if (!result.isSuccess()) {
                return result;
            }

            //计算每行商品调整的数量
            List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = pmsApplyBillDetailRepositoryService.queryListByBillNo(applyBillDTO.getBillNo());
            Map<String, PmsApplyBillDetailPO> billDetailMap = pmsApplyBillDetailPOS.stream()
                    .collect(Collectors.toMap(PmsApplyBillDetailPO::getUniqKey, detail -> detail));
            try {
                //todo 库存调用抽象
                BatchRecordResp batchRecordResp = pmsOrderApplyDomainService.costStockExecute4Update(applyBillDTO, applyBillDTO.getBillDetails(), billDetailMap);
            } catch (Exception e) {
                Logs.error("库存上报失败", e);
                throw new BizException("1000", "库存上报失败: " + e.getMessage());
            }
        }catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新订单申请审核失败", e);
            return Results.ofCommonError(PmsErrorCodeEnum.SC_PMS_001_U001);
        } finally {
            lockManager.unlock(LockConstants.PMS_ORDER_APPLY_LOCK + applyBillDTO.getBillNo());
        }
        return Results.ofSuccess();
    }

    @Override
    public Result<List<ApplyPriceResp>> getApplyPrice(ApplyPriceDTO applyPriceDTO) {
        // 按出货途径拆分采购配送
        List<ApplyPriceResp> applyPriceResps = new ArrayList<>();
        Map<Integer, List<ApplyGoodsInfo>> applyGoods2ShippingWayMap =
                applyPriceDTO.getGoodsInfos().stream().collect(Collectors.groupingBy(ApplyGoodsInfo::getShippingWay));
        if (applyGoods2ShippingWayMap.containsKey(ShippingWayEnum.PURCHASE.getCode())) {
            List<String> purchSkuCodes = applyGoods2ShippingWayMap.get(ShippingWayEnum.PURCHASE.getCode()).stream().map(ApplyGoodsInfo::getSkuCode).collect(Collectors.toList());
            // 查采购
            if (CollectionUtils.isNotEmpty(purchSkuCodes)) {
                DeptGoodsPurchPriceQueryDTO purchPriceQueryDTO = DeptGoodsPurchPriceQueryDTO.builder()
                        .deptCode(applyPriceDTO.getDeptCode())
                        .applyCate(applyPriceDTO.getApplyCate())
                        .contractSkuInfos(applyPriceDTO.getGoodsInfos().stream().map(e -> ContractSkuInfo.builder().contractNo(e.getContractNo()).skuCode(e.getSkuCode()).build()).collect(Collectors.toList()))
                        .build();
                List<DeptGoodsPurchPriceDTO> deptGoodsPurchPriceDTOS = iSupplychainBizGoodsRuleService.listDeptGoodsPurchPrice(purchPriceQueryDTO);
                applyPriceResps.addAll(deptGoodsPurchPriceDTOS.stream()
                        .map(e -> ApplyPriceResp.builder().applyPrice(e.getFinaPrice()).priceType(e.getPriceType()).contractNo(e.getContractNo()).skuCode(e.getSkuCode()).priceType(e.getPriceType()).build())
                        .collect(Collectors.toList()));
            }
        }

        //查配送
        if (applyGoods2ShippingWayMap.containsKey(ShippingWayEnum.DELIVERY.getCode())) {
            List<ValidGoodsInfo> validGoodsInfos = applyGoods2ShippingWayMap.get(ShippingWayEnum.DELIVERY.getCode())
                    .stream().map(e -> ValidGoodsInfo.builder().skuCode(e.getSkuCode()).categoryCode(e.getCategoryCode()).distCode(e.getDistCode()).build())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(validGoodsInfos)) {
                DeptGoodsDeliveryPriceQueryDTO deliveryPriceQueryDTO = DeptGoodsDeliveryPriceQueryDTO.builder()
                        .deptCode(applyPriceDTO.getDeptCode())
                        .goodsInfos(validGoodsInfos)
                        .build();
                List<DeptGoodsDeliveryPriceDTO> deptGoodsDeliveryPriceDTOS = iSupplychainBizGoodsRuleService.listDeptGoodsDeliveryPrice(deliveryPriceQueryDTO);
                applyPriceResps.addAll(deptGoodsDeliveryPriceDTOS.stream()
                        .map(e -> ApplyPriceResp.builder().applyPrice(e.getFinaPrice()).priceType(e.getPriceType()).distCode(e.getDistCode()).skuCode(e.getSkuCode()).priceType(e.getPriceType()).build())
                        .collect(Collectors.toList()));
            }
        }

        return Results.ofSuccess(applyPriceResps);
    }

    @Override
    public Result<List<ApplyBillResp>> auditOrderApply(BatchApplyBillDTO batchApplyBillDTO) {
        List<String> auditedKeys = new ArrayList<>();
        OpInfo opInfo = userUtil.getOpInfoWithThrow();
        LocalDateTime now = LocalDateTime.now();
        // 业务校验 -状态 策略等校验
        List<ApplyBillDTO> applyBillDTOS = pmsOrderApplyDomainService.checkAudit(batchApplyBillDTO.getBillNumbers());

        // 1. 单据锁
        List<String> billNos = applyBillDTOS.stream().map(ApplyBillDTO::getBillNo).collect(Collectors.toList());
        try {
            for (String billNo : billNos) {
                boolean lock = lockManager.tryLock(LockConstants.PMS_ORDER_APPLY_LOCK + billNo, 10, TimeUnit.MINUTES);
                if (!lock) {
                    return Results.ofCommonError(PmsErrorCodeEnum.SC_PMS_004_P021);
                }
                auditedKeys.add(billNo);
            }
            Map<String, List<PmsApplyBillDetailPO>> detailMap = new HashMap<>();
            //  需求批次生成
            for (ApplyBillDTO applyBillDTO : applyBillDTOS) {
                List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = pmsApplyBillDetailRepositoryService.queryListByBillNo(applyBillDTO.getBillNo());
                detailMap.put(applyBillDTO.getBillNo(), pmsApplyBillDetailPOS);
                List<PmsApplyBillDetailPO> billDetailPOS = pmsApplyBillDetailPOS.stream().filter(e -> ObjectUtils.equals(ShippingWayEnum.DELIVERY.getCode(), e.getShippingWay())).collect(Collectors.toList());
                List<DemandBatchRecordGoodsDTO> demandBatchRecordGoodsDTOS = billDetailPOS.stream().map(e ->
                        DemandBatchRecordGoodsDTO.builder()
                                .categoryCode(e.getCategoryCode())
                                .skuCode(e.getSkuCode())
                                .deptName(e.getShipperName())
                                .deptCode(e.getShipperCode())
                                .build()
                ).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(demandBatchRecordGoodsDTOS)) {
                    GeneratePurchBatchDTO generatePurchBatch = GeneratePurchBatchDTO.builder()
                            .date(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()))
                            .deliverDays((int) ChronoUnit.DAYS.between(applyBillDTO.getCreateTime().toLocalDate(), applyBillDTO.getDeliverDate()))
                            .demandBatchRecordGoodsList(demandBatchRecordGoodsDTOS)
                            .build();
                    if (!OrderAttributeEnum.isZJ(applyBillDTO.getAttributeCode())) {
                        iSupplychainPmsBizRuleEngineService.generateProcurementBatch(generatePurchBatch);
                    }

                    Map<String, DemandBatchRecordGoodsDTO> purchBatchNoMap = new HashMap<>();
                    for (DemandBatchRecordGoodsDTO demandBatchRecordGoodsDTO : generatePurchBatch.getDemandBatchRecordGoodsList()) {
                        String key = demandBatchRecordGoodsDTO.getDeptCode() + "_" + demandBatchRecordGoodsDTO.getSkuCode();
                        purchBatchNoMap.put(key,demandBatchRecordGoodsDTO);
                    }
//                    Map<String, DemandBatchRecordGoodsDTO> purchBatchNoMap = generatePurchBatch.getDemandBatchRecordGoodsList().stream()
//                            .filter(e -> e.getPurchBatchNo() != null).collect(Collectors.toMap(DemandBatchRecordGoodsDTO::getSkuCode, Function.identity()));
                    for (PmsApplyBillDetailPO pmsApplyBillDetailPO : billDetailPOS) {
                        String key = pmsApplyBillDetailPO.getShipperCode() + "_" + pmsApplyBillDetailPO.getSkuCode();
                        if(purchBatchNoMap.containsKey(key)){
                            DemandBatchRecordGoodsDTO demandBatchRecordGoodsDTO = purchBatchNoMap.get(key);
                            pmsApplyBillDetailPO.setPurchBatchNo(demandBatchRecordGoodsDTO.getPurchBatchNo());
                            pmsApplyBillDetailPO.setPurchBatchName(demandBatchRecordGoodsDTO.getPurchBatchName());
                        }
                    }
                    pmsApplyBillDetailRepositoryService.updateBatchById(billDetailPOS);
                }
            }

            for (ApplyBillDTO applyBillDTO : applyBillDTOS) {
                List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = detailMap.get(applyBillDTO.getBillNo());
                // 推库存 ( 推加盟 加强推库存方法前置占用和异常回滚 )
                try {
                    pmsOrderApplyDomainService.costStockExecute(applyBillDTO, pmsApplyBillDetailPOS, BillOperateTypeEnum.AUDIT);
                } catch (Exception e) {
                    log.error("库存占用失败, 异常:", e);
                    throw new BizException(PmsErrorCodeEnum.SC_PMS_001_B018);
                }
            }
            // 更新单据状态

            List<PmsApplyBillPO> applyBillPOS = applyBillDTOS.stream().map(e ->
                    PmsApplyBillPO.builder().id(e.getId())
                            .auditTime(now).auditManCode(opInfo.getOperatorCode()).auditManName(opInfo.getOperatorName())
                            .status(BillStateEnum.AUDIT.getCode()).build()).collect(Collectors.toList());
            pmsApplyBillRepositoryService.updateBatchById(applyBillPOS);
            // 推申请单审核状态入事件(需求单使用)
            pmsDemandDomainService.auditApplyToDemand(billNos);
        } catch (BizException e) {
            log.error("审核失败, 业务异常:", e);
            throw e;
        } catch (Exception e) {
            log.error("审核失败, 系统异常:", e);
            throw new BizException(PmsErrorCodeEnum.SC_PMS_001_U001);
        } finally {
            try {
                if (CollectionUtils.isNotEmpty(auditedKeys)) {
                    for (String auditedKey : auditedKeys) {
                        lockManager.unlock(LockConstants.PMS_ORDER_APPLY_LOCK + auditedKey);
                    }
                }
            } catch (Exception e) {
                log.error("解锁异常:", e);
            }
        }
        return Results.ofSuccess();
    }

    @Override
    public Result<ApplyBillResp> cancelOrderApply(CancelApplyBillDTO cancelApplyBillDTO) {
        // 分布式锁控制 防并发修改导致数量错误
        boolean lock = lockManager.tryLock(LockConstants.PMS_ORDER_APPLY_LOCK + cancelApplyBillDTO.getBillNumber(), 10, TimeUnit.MINUTES);
        if (!lock) {
            return Results.ofCommonError(PmsErrorCodeEnum.SC_PMS_004_P021);
        }
        try {
            PmsApplyBillPO pmsApplyBillPO = pmsApplyBillRepositoryService.getByBillNo(cancelApplyBillDTO.getBillNumber());
            if (pmsApplyBillPO == null) {
                throw new BizException(PmsErrorCodeEnum.SC_PMS_004_P001);
            }
            if (!BillStateEnum.canCancel(pmsApplyBillPO.getStatus())) {
                return Results.ofCommonError(PmsErrorCodeEnum.SC_PMS_001_B017);
            }

            OpInfo opInfo = userUtil.getOpInfoWithThrow();
            //3.更新单据状态
            PmsApplyBillPO pmsApplyBill4Update = PmsApplyBillPO.builder().id(cancelApplyBillDTO.getId())
                    .cancelTime(LocalDateTime.now())
                    .cancelManCode(opInfo.getOperatorCode())
                    .cancelManName(opInfo.getOperatorName())
                    .status(BillStateEnum.CANCEL.getCode()).build();
            pmsApplyBillRepositoryService.updateById(pmsApplyBill4Update);

            List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = pmsApplyBillDetailRepositoryService.queryListByBillNo(pmsApplyBillPO.getBillNo());
            List<PmsApplyBillDetailPO> detailPOS = pmsApplyBillDetailPOS.stream().map(e -> PmsApplyBillDetailPO.builder().id(e.getId()).status(BillDetailStateEnum.EXTRACTED.getCode()).build()).collect(Collectors.toList());
            pmsApplyBillDetailRepositoryService.updateBatchById(detailPOS);
            //2. 业务参数校验 (校验单据是否已审核 审核后取消需推库存)
            if (BillStateEnum.checkUpdateAudit(pmsApplyBillPO.getStatus())) {
                try {
                    pmsOrderApplyDomainService.costStockExecute(CglibCopier.copy(pmsApplyBillPO, ApplyBillDTO.class), pmsApplyBillDetailPOS, BillOperateTypeEnum.CANCEL);
                } catch (Exception e) {
                    log.error("库存占用失败, 异常:", e);
                    BizExceptions.throwWithMsg(PmsErrorCodeEnum.SC_PMS_001_B018.getErrorMsg());
                }
            }
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建订单申请失败", e);
            return Results.ofCommonError(PmsErrorCodeEnum.SC_PMS_001_U001);
        } finally {
            lockManager.unlock(LockConstants.PMS_ORDER_APPLY_LOCK + cancelApplyBillDTO.getBillNumber());
        }
        return Results.ofSuccess();
    }

    /**
     * 作废需求单对应申请单
     *
     * @param cancelApplyBillDTOs
     * @return
     */
    @Override
    public Result<ApplyBillResp> cancelOrderApply4Demand(List<CancelApplyDetail4DemandDTO> cancelApplyBillDTOs) {
        List<QueryDetailByInsideIdReq> reqs
                = cancelApplyBillDTOs.stream().map(e -> QueryDetailByInsideIdReq.builder().billNo(e.getBillNo()).insideId(e.getInsideId()).build()).collect(Collectors.toList());
        List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = pmsApplyBillDetailRepositoryService.queryListByInsideId(reqs);
        Map<String, List<PmsApplyBillDetailPO>> billDetailPOMap = pmsApplyBillDetailPOS.stream().collect(Collectors.groupingBy(PmsApplyBillDetailPO::getBillNo));
        List<PmsApplyBillPO> listByBillNos = pmsApplyBillRepositoryService.getListByBillNos(Lists.newArrayList(billDetailPOMap.keySet()));
        for (PmsApplyBillPO pmsApplyBillPO : listByBillNos) {
            try {
                pmsOrderApplyDomainService.costStockExecute(orderApplyBillConvert.convertPO2DTO(pmsApplyBillPO),
                        billDetailPOMap.get(pmsApplyBillPO.getBillNo()), BillOperateTypeEnum.CANCEL);
            }  catch (Exception e) {
                log.error("库存占用失败, 异常:", e);
                return Results.ofCommonError(PmsErrorCodeEnum.SC_PMS_001_B018.getCode(),PmsErrorCodeEnum.SC_PMS_001_B018.getErrorMsg() + (e.getMessage() == null ? "":e.getMessage()));
            }
        }

        pmsOrderApplyDomainService.syncApplyDetailState(pmsApplyBillDetailPOS, BillDetailStateEnum.EXTRACTED);
        return Results.ofSuccess();
    }

    @Override
    public Result ladingBillWriteBack(List<LadingBillWriteBackDTO> ladingBillWriteBackDTOS) {
        List<QueryDetailByInsideIdReq> reqs
                = ladingBillWriteBackDTOS.stream().map(e -> QueryDetailByInsideIdReq.builder().billNo(e.getBillNo()).insideId(e.getInsideId()).build()).collect(Collectors.toList());
        List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = pmsApplyBillDetailRepositoryService.queryListByInsideId(reqs);
        Map<String, List<PmsApplyBillDetailPO>> billDetailPOMap = pmsApplyBillDetailPOS.stream().collect(Collectors.groupingBy(PmsApplyBillDetailPO::getBillNo));

        pmsOrderApplyDomainService.syncApplyDetailState(pmsApplyBillDetailPOS, BillDetailStateEnum.EXTRACTED);
        return Results.ofSuccess();
    }

    @Override
    public PageResult<ApplyBillDTO> queryOrderList4App(QueryApplyOrder4AppDTO req) {
        Page<PmsApplyBillPO> page = new Page<>(req.getCurrent(), req.getPageSize());
        Page<PmsApplyBillPO> list4App = pmsApplyBillRepositoryService.getList4App(req, page);
        return PageResult.of(list4App.getTotal(), orderApplyBillConvert.convertPO2DTOList(list4App.getRecords()));
    }

    @Override
    public Result<List<ApplyBillGoodsDTO>> queryUnfinishedOrder(QueryUnfinishedApplyDTO queryUnfinishedApplyDTO) {
        Date date = new Date();
        queryUnfinishedApplyDTO.setStartTime(DateUtil.yyyyMMddSlash(date) + " 00:00:00");
        queryUnfinishedApplyDTO.setEndTime(DateUtil.yyyyMMddSlash(date) + " 23:59:59");
        queryUnfinishedApplyDTO.setStatus(Lists.newArrayList(BillStateEnum.DRAFT.getCode(), BillStateEnum.PENDING_AUDIT.getCode(), BillStateEnum.AUDIT.getCode()));
        queryUnfinishedApplyDTO.setAttributeCodes(Lists.newArrayList(OrderAttributeEnum.ZHUI_JIAN.getCode(),
                OrderAttributeEnum.ZHUI_JIA.getCode(),  OrderAttributeEnum.TUAN_GOU.getCode(),OrderAttributeEnum.JIN_JI.getCode()));
        return Results.ofSuccess(orderApplyBillConvert.convertGoodsPOList2Resp(
                pmsApplyBillRepositoryService.getUnfinishedList(queryUnfinishedApplyDTO)));
    }

    @Override
    public Result<PageResult<ApplyBillDTO>> queryApplyList(QueryApplyBillDTO req) {
        OpInfo opInfoWithThrow = userUtil.getDeptOpInfoWithThrow();
        List<String> manageDeptCodeList = opInfoWithThrow.getManageDeptCodeList();
        if (!opInfoWithThrow.getOriginDeptFlag() && CollectionUtils.isNotEmpty(manageDeptCodeList)) {
            List<String> deptCodes = req.getDeptCodes();
            req.setDeptCodes(CollectionUtils.isNotEmpty(deptCodes) ? manageDeptCodeList.stream().filter(deptCodes::contains).collect(Collectors.toList()) : manageDeptCodeList);
        }
        Page<PmsApplyBillPO> page = new Page<>(req.getCurrent(), req.getPageSize());
        Page<PmsApplyBillPO> billPOPage = pmsApplyBillRepositoryService.getApplyList(req, page);
        return Results.ofSuccess(PageResult.of(billPOPage.getTotal(), orderApplyBillConvert.convertPO2DTOList(billPOPage.getRecords())));
    }

    @Override
    public Result<ApplyBillDTO> queryApplyDetailList(QueryApplyBillDetailReq queryApplyBillDetailReq) {
        PmsApplyBillPO pmsApplyBillPO = pmsApplyBillRepositoryService.getByBillNo(queryApplyBillDetailReq.getBillNo());
        ApplyBillDTO applyBillDTO = orderApplyBillConvert.convertPO2DTO(pmsApplyBillPO);

        List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = pmsApplyBillDetailRepositoryService.queryListByBillNo(applyBillDTO.getBillNo());
        List<ApplyBillDetailDTO> applyBillDetailDTOS = orderApplyBillDetailConvert.convertDetailListPO2DTO(pmsApplyBillDetailPOS);
        applyBillDTO.setBillDetails(applyBillDetailDTOS);
        List<String> purchBillNos = applyBillDetailDTOS.stream().map(e -> StringUtils.isNotEmpty(e.getPurchBillNo()) ? e.getPurchBillNo() : null).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(purchBillNos)) {
            //list转逗号隔开的集合
            String purchBillNosStr = StringUtils.join(purchBillNos, ",");
            applyBillDTO.setPurchBillNo(purchBillNosStr);
        }

        List<String> deliveryBillNo = applyBillDetailDTOS.stream().map(e -> StringUtils.isNotEmpty(e.getDeliveryBillNo()) ? e.getDeliveryBillNo() : null).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(deliveryBillNo)) {
            //list转逗号隔开的集合
            String deliveryBillNoStr = StringUtils.join(deliveryBillNo, ",");
            applyBillDTO.setDeliveryBillNo(deliveryBillNoStr);
        }

        return Results.ofSuccess(applyBillDTO);
    }

    /**
     * 查询追加追减申请明细
     *
     * @param queryAddReduceDetailReq
     * @return
     */
    @Override
    public Result<List<PmsAddReduceDetailResp>> queryAddReduceDetailList(QueryAddReduceDetailReq queryAddReduceDetailReq) {
        queryAddReduceDetailReq.setAttributeCodes(Lists.newArrayList(OrderAttributeEnum.ZHUI_JIA.getCode(), OrderAttributeEnum.ZHUI_JIAN.getCode()));
        List<PmsApplyBillDetailPO> applyBillDetailDTOS = pmsApplyBillDetailRepositoryService.queryAddReduceDetailList(queryAddReduceDetailReq);
        if (CollectionUtils.isEmpty(applyBillDetailDTOS)) {
            return Results.ofSuccess();
        }
        Map<String, List<PmsApplyBillDetailPO>> billDetailMap = applyBillDetailDTOS.stream().collect(Collectors.groupingBy(PmsApplyBillDetailPO::getAddReduceKey));
        List<String> billNos = applyBillDetailDTOS.stream().map(PmsApplyBillDetailPO::getBillNo).distinct().collect(Collectors.toList());
        List<PmsApplyBillPO> pmsApplyBillPOS = pmsApplyBillRepositoryService.getListByBillNos(billNos);
        Map<String, PmsApplyBillPO> pmsApplyBillPOMap = pmsApplyBillPOS.stream().collect(Collectors.toMap(PmsApplyBillPO::getBillNo, Function.identity()));

        List<PmsAddReduceDetailResp> detailResps = billDetailMap.values().stream().map(pmsApplyBillDetailPOS ->
                PmsAddReduceDetailResp.builder()
                        .addReduceQty(pmsApplyBillDetailPOS.stream().map(PmsApplyBillDetailPO::getOrderQty)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                        .purchBatchNo(pmsApplyBillDetailPOS.get(0).getPurchBatchNo())
                        .deptCode(pmsApplyBillDetailPOS.get(0).getDeptCode())
                        .deptName(pmsApplyBillDetailPOS.get(0).getDeptName())
                        .uomAttr(pmsApplyBillDetailPOS.get(0).getUomAttr())
                        .skuCode(pmsApplyBillDetailPOS.get(0).getSkuCode())
                        .skuName(pmsApplyBillDetailPOS.get(0).getSkuName())
                        .goodsType(pmsApplyBillDetailPOS.get(0).getGoodsType())
                        .barcode(pmsApplyBillDetailPOS.get(0).getBarcode())
                        .goodsNo(pmsApplyBillDetailPOS.get(0).getGoodsNo())
                        .categoryCode(pmsApplyBillDetailPOS.get(0).getCategoryCode())
                        .categoryName(pmsApplyBillDetailPOS.get(0).getCategoryName())
                        .unitRate(pmsApplyBillDetailPOS.get(0).getGoodsUnitRate())
                        .basicUnit(pmsApplyBillDetailPOS.get(0).getBasicUnit())
                        .wholeUnit(pmsApplyBillDetailPOS.get(0).getWholeUnit())
                        .skuModel(pmsApplyBillDetailPOS.get(0).getSkuModel())
                        .inputTaxRate(pmsApplyBillDetailPOS.get(0).getInputTaxRate())
                        .outputTaxRate(pmsApplyBillDetailPOS.get(0).getOutputTaxRate())
                        .directSign(pmsApplyBillDetailPOS.get(0).getDirectSign())
                        .addReduceSources(pmsApplyBillDetailPOS.stream().map(e -> {
                                    PmsAddReduceSource addReduceSource = CglibCopier.copy(e, PmsAddReduceSource.class);
                                    addReduceSource.setOrderRemark(pmsApplyBillPOMap.get(e.getBillNo()).getRemark());
                                    addReduceSource.setAttributeCode(pmsApplyBillPOMap.get(e.getBillNo()).getAttributeCode());
                                    addReduceSource.setAttributeName(pmsApplyBillPOMap.get(e.getBillNo()).getAttributeName());
                                    addReduceSource.setAddReduceQty(e.getOrderQty());
                                    if (pmsApplyBillPOMap.get(e.getBillNo()).getAuditTime() != null) {
                                        addReduceSource.setAuditTime(pmsApplyBillPOMap.get(e.getBillNo()).getAuditTime());
                                    }
                                    return addReduceSource;
                                }
                        ).collect(Collectors.toList()))
                        .adjustQty(pmsApplyBillDetailPOS.stream().map(e -> OrderAttributeEnum.ZHUI_JIA.getCode().equals(pmsApplyBillPOMap.get(e.getBillNo()).getAttributeCode()) ? e.getOrderQty() : e.getOrderQty().negate()).reduce(BigDecimal.ZERO, BigDecimal::add))
                        .uomAttr(pmsApplyBillDetailPOS.get(0).getUomAttr())
                        .basicUnit(pmsApplyBillDetailPOS.get(0).getBasicUnit())
                        .build()
        ).collect(Collectors.toList());
        return Results.ofSuccess(detailResps);
    }

    @Override
    public Result<List<ApplyStatisticResp>> queryCount4StateGroup(QueryApplyOrder4AppDTO applyOrder4AppDTO) {
        return Results.ofSuccess(orderApplyBillConvert.convertCountPOList2Resp(pmsApplyBillRepositoryService.getApplyStatistic(applyOrder4AppDTO)));
    }

    /**
     * 按包装率的整数倍取值（向上取整）
     *
     * @param value       原始数值（需大于0）
     * @param packingRate 包装率（需大于0）
     * @return 包装率的整数倍结果（≥value的最小倍数）
     * @throws IllegalArgumentException 参数非法时抛出异常
     */
    public static BigDecimal roundToPackingMultiple(BigDecimal value, BigDecimal packingRate) {
//        if (value.compareTo(BigDecimal.ZERO) <= 0 || packingRate.compareTo(BigDecimal.ZERO) <= 0) {
//            throw new IllegalArgumentException("参数必须大于0");
//        }
        // 公式：ceil(value / packingRate) * packingRate
        return value.divide(packingRate, 0, RoundingMode.CEILING).multiply(packingRate);
    }

    /**
     * 按包装率的整数倍取值（向下取整）
     *
     * @param value       原始数值（需大于0）
     * @param packingRate 包装率（需大于0）
     * @return 包装率的整数倍结果（≤value的最大倍数）
     * @throws IllegalArgumentException 参数非法时抛出异常
     */
    public static BigDecimal roundDownToPackingMultiple(BigDecimal value, BigDecimal packingRate) {
        if (value.compareTo(BigDecimal.ZERO) <= 0 || packingRate.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("参数必须大于0");
        }
        // 公式：floor(value / packingRate) * packingRate
        return value.divide(packingRate, 0, RoundingMode.FLOOR).multiply(packingRate);
    }

    /**
     * 订单分配
     *
     * @param pmsOrderAllocateReq
     * @return
     */
    @Override
    public Result<List<PmsOrderAllocateResp>> orderAllocate(PmsOrderAllocateReq pmsOrderAllocateReq) {
        List<PmsOrderAllocateDetail> allocateReqDetails = pmsOrderAllocateReq.getDetails();
        Map<String, PmsOrderAllocateDetail> allocateDetailMap = allocateReqDetails.stream().collect(Collectors.toMap(PmsOrderAllocateDetail::getAddReduceKey, Function.identity()));

        //todo 筛选出调增的
        List<PmsOrderAllocateDetail> addAllocateList = allocateReqDetails.stream().filter(e -> e.getAddReduceQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        List<PmsOrderAllocateResp> allocateResps = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(addAllocateList)) {
            if (pmsOrderAllocateReq.getAddReduceRule().equals(2)) { // 根据商品订货策略
                allocateResps.addAll(addAllocateList.stream().map(e -> {
                            PmsOrderAllocateResp resp = CglibCopier.copy(e, PmsOrderAllocateResp.class);
                            resp.setAllocateType(1);
                            resp.setTransferNum(roundToPackingMultiple(e.getAdjustQty(), e.getUnitRate()));
                            return resp;
                        }
                ).collect(Collectors.toList()));
            } else {
                //todo 查出配送订单 明细行
                // 查出配送订单对应的采购订单
                //todo 查询配送订单
                WdDeliveryBillQueryListBatchReq queryReq = new WdDeliveryBillQueryListBatchReq();
                queryReq.setStatus(WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_APPROVED.getCode());
                queryReq.setOrderAttributeCodeListNotIn(Lists.newArrayList(OrderAttributeEnum.TUAN_GOU.getCode(), OrderAttributeEnum.JIN_JI.getCode()));
                queryReq.setBatchParams(addAllocateList.stream().map(e ->
                        WdDeliveryBillQueryListBatchReqInner.builder()
                                .inDeptCode(e.getDeptCode())
                                .whCode(pmsOrderAllocateReq.getDistCode())
                                .requireBatch(e.getPurchBatchNo())
                                .skuCode(e.getSkuCode())
                                .goodsType(e.getGoodsType())
                                .build()).collect(Collectors.toList()));
                List<WdDeliveryBillDetailPO> wdDeliveryBillPOS = iWdDeliveryBillRepository.queryDeliveryOrderHdrListBatch(queryReq);

                if (CollectionUtils.isNotEmpty(wdDeliveryBillPOS)) {
                    Map<String, List<WdDeliveryBillDetailPO>> billMap = wdDeliveryBillPOS.stream().collect(Collectors.groupingBy(WdDeliveryBillDetailPO::getBillNo));
                    List<PurchaseByDeliveryQryReq> byDeliveryQryReqs = wdDeliveryBillPOS.stream().map(item -> {
                        PurchaseByDeliveryQryReq req = new PurchaseByDeliveryQryReq();
                        req.setDeliveryBillNo(item.getBillNo());
                        req.setInsideId(item.getInsideId());
                        req.setSkuCode(item.getSkuCode());
                        return req;
                    }).collect(Collectors.toList());
                    List<PurchaseDeliverySkuResp> purchByDeliveryNos = pmsPurchaseOrderDomainService.getPurchByDeliveryNos(byDeliveryQryReqs);
                    Map<String, PurchaseDeliverySkuResp> purchByDeliveryNosMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(purchByDeliveryNos)) {
                        purchByDeliveryNosMap = purchByDeliveryNos.stream().collect(Collectors.toMap(PurchaseDeliverySkuResp::getDeliveryInsideIdKey, Function.identity()));
                    }
                    List<WdDeliveryBillPO> deliveryBillPOS = iWdDeliveryBillRepository.queryDeliveryOrderBillByBillNo(Lists.newArrayList(billMap.keySet()));
                    Map<String, WdDeliveryBillPO> wdDeliveryBillPOMap = deliveryBillPOS.stream().collect(Collectors.toMap(WdDeliveryBillPO::getBillNo, Function.identity()));
                    Map<String, List<WdDeliveryBillDetailPO>> wdDeliveryBillPOSMap = wdDeliveryBillPOS.stream().collect(Collectors.groupingBy(WdDeliveryBillDetailPO::getAddReduceKey));

                    // todo 非直流场景 可以不存在配转采订单
                    Map<String, PurchaseDeliverySkuResp> finalPurchByDeliveryNosMap = purchByDeliveryNosMap;
                    allocateResps.addAll(wdDeliveryBillPOSMap.entrySet().stream().map(entry -> {
                        PmsOrderAllocateDetail orderAllocateDetail = allocateDetailMap.get(entry.getKey());
                        PmsOrderAllocateResp resp = CglibCopier.copy(orderAllocateDetail, PmsOrderAllocateResp.class);
                        Optional<WdDeliveryBillDetailPO> first = entry.getValue().stream().filter(e -> finalPurchByDeliveryNosMap.containsKey(e.getBillNo() + "-" + e.getInsideId())).findFirst();
                        if (first.isPresent()) {
                            WdDeliveryBillDetailPO detailPO = first.get();
                            PmsOrderAllocateOrderInfo transfer = PmsOrderAllocateOrderInfo.builder()
                                    .billType(DirectSignEnum.isDirect(detailPO.getDirectSign()) ? AddReduceTypeEnum.DIRECT_TRANSFER.getCode() : AddReduceTypeEnum.STORE_TRANSFER.getCode())
                                    .auditTime(wdDeliveryBillPOMap.get(detailPO.getBillNo()).getApproveTime())
                                    .billNo(detailPO.getBillNo())
                                    .insideId(detailPO.getInsideId())
                                    .purchUnitRate(detailPO.getOrderUnitRate())
                                    .orderQty(detailPO.getDeliveryQty())
                                    .expectAdjustQty(roundToPackingMultiple(orderAllocateDetail.getAdjustQty(), orderAllocateDetail.getUnitRate()))
                                    .tax(detailPO.getDeliveryTax())
                                    .salePrice(detailPO.getSalePrice())
                                    .saleMoney(detailPO.getSaleMoney())
                                    .build();
                            List<PmsOrderAllocateOrderInfo> pmsOrderAllocateOrderInfos = Lists.newArrayList(transfer);
                            PurchaseDeliverySkuResp billDetailSumResp = finalPurchByDeliveryNosMap.get(detailPO.getBillNo() + "-" + detailPO.getInsideId());
                            PmsOrderAllocateOrderInfo purch = PmsOrderAllocateOrderInfo.builder()
                                    .billType(DirectSignEnum.isDirect(detailPO.getDirectSign()) ? AddReduceTypeEnum.DIRECT_PURCHASE.getCode() : AddReduceTypeEnum.TRANSFER_TO_PURCHASE.getCode())
                                    .auditTime(billDetailSumResp.getAuditTime())
                                    .billNo(billDetailSumResp.getBillNo())
                                    .insideId(billDetailSumResp.getInsideId())
                                    .purchUnitRate(billDetailSumResp.getPurchUnitRate())
                                    .orderQty(billDetailSumResp.getPurchQty())
                                    .expectAdjustQty(roundToPackingMultiple(orderAllocateDetail.getAdjustQty(), orderAllocateDetail.getUnitRate()))
                                    .tax(billDetailSumResp.getPurchTax())
                                    .salePrice(billDetailSumResp.getSalePrice())
                                    .saleMoney(billDetailSumResp.getSaleMoney())
                                    .build();
                            pmsOrderAllocateOrderInfos.add(purch);
                            resp.setOrderAllocateOrderInfoList(pmsOrderAllocateOrderInfos);
                            resp.setExpectedAdjustPurchQty(purch.getExpectAdjustQty());
                            resp.setExpectedAdjustTransferQty(transfer.getExpectAdjustQty());
                            return resp;
                        } else if (DirectSignEnum.NOT_DIRECT.getCode().equals(orderAllocateDetail.getDirectSign())) {
                            WdDeliveryBillDetailPO detailPO = entry.getValue().get(0);
                            PmsOrderAllocateOrderInfo transfer = PmsOrderAllocateOrderInfo.builder()
                                    .billType(DirectSignEnum.isDirect(detailPO.getDirectSign()) ? AddReduceTypeEnum.DIRECT_TRANSFER.getCode() : AddReduceTypeEnum.STORE_TRANSFER.getCode())
                                    .auditTime(wdDeliveryBillPOMap.get(detailPO.getBillNo()).getApproveTime())
                                    .billNo(detailPO.getBillNo())
                                    .insideId(detailPO.getInsideId())
                                    .purchUnitRate(detailPO.getOrderUnitRate())
                                    .orderQty(detailPO.getDeliveryQty())
                                    .expectAdjustQty(roundToPackingMultiple(orderAllocateDetail.getAdjustQty(), orderAllocateDetail.getUnitRate()))
                                    .tax(detailPO.getDeliveryTax())
                                    .salePrice(detailPO.getSalePrice())
                                    .saleMoney(detailPO.getSaleMoney())
                                    .build();
                            List<PmsOrderAllocateOrderInfo> pmsOrderAllocateOrderInfos = Lists.newArrayList(transfer);
                            resp.setOrderAllocateOrderInfoList(pmsOrderAllocateOrderInfos);
                            resp.setExpectedAdjustTransferQty(transfer.getExpectAdjustQty());
                            return resp;
                        }
                        return null;
                    }).collect(Collectors.toList()));
                } else {
                    allocateResps.addAll(addAllocateList.stream().map(e -> {
                                PmsOrderAllocateResp resp = CglibCopier.copy(e, PmsOrderAllocateResp.class);
                                resp.setAllocateType(1);
                                resp.setTransferNum(roundToPackingMultiple(e.getAdjustQty(), e.getUnitRate()));
                                return resp;
                            }
                    ).collect(Collectors.toList()));
                }

                //todo 分配采购订单和配送订单数量
                // 1. 按类型分类 包装率向上取整 数量放第一个订单明细行
                // 2. 统计当前类型单据明细行调增总数量
            }
        }

        //追减场景
        List<PmsOrderAllocateDetail> reduceAllocateList
                = allocateReqDetails.stream().filter(e -> e.getAddReduceQty().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(reduceAllocateList)) {
            //todo 查询配送订单
            WdDeliveryBillQueryListBatchReq queryReq = new WdDeliveryBillQueryListBatchReq();
            queryReq.setStatus(WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_APPROVED.getCode());
            queryReq.setOrderAttributeCodeListNotIn(Lists.newArrayList(OrderAttributeEnum.TUAN_GOU.getCode(), OrderAttributeEnum.JIN_JI.getCode()));
            queryReq.setBatchParams(reduceAllocateList.stream().map(e ->
                    WdDeliveryBillQueryListBatchReqInner.builder()
                            .inDeptCode(e.getDeptCode())
                            .whCode(pmsOrderAllocateReq.getDistCode())
                            .requireBatch(e.getPurchBatchNo())
                            .skuCode(e.getSkuCode())
                            .goodsType(e.getGoodsType())
                            .build()).collect(Collectors.toList()));
            List<WdDeliveryBillDetailPO> wdDeliveryBillPOS = iWdDeliveryBillRepository.queryDeliveryOrderHdrListBatch(queryReq);

            //todo 根据采购订单或配转采订单号 查采购订单信息

            //todo 分配采购订单和配送订单数量
            // 1. 按类型分类 减到0继续下一行 如果是最后一行(当前行已经不够减到0) 按包装率减向下取整 即减7 包装率3 则减6
            // 2. 统计当前类型单据明细行调减总数量

            if (CollectionUtils.isNotEmpty(wdDeliveryBillPOS)) {
                Map<String, List<WdDeliveryBillDetailPO>> billMap = wdDeliveryBillPOS.stream().collect(Collectors.groupingBy(WdDeliveryBillDetailPO::getBillNo));
                List<PurchaseByDeliveryQryReq> byDeliveryQryReqs = wdDeliveryBillPOS.stream().map(item -> {
                    PurchaseByDeliveryQryReq req = new PurchaseByDeliveryQryReq();
                    req.setDeliveryBillNo(item.getBillNo());
                    req.setInsideId(item.getInsideId());
                    req.setSkuCode(item.getSkuCode());
                    return req;
                }).collect(Collectors.toList());

                List<PurchaseDeliverySkuResp> purchByDeliveryNos = pmsPurchaseOrderDomainService.getPurchByDeliveryNos(byDeliveryQryReqs);

                Map<String, PurchaseDeliverySkuResp> purchByDeliveryNosMap = purchByDeliveryNos.stream().collect(Collectors.toMap(PurchaseDeliverySkuResp::getDeliveryInsideIdKey, Function.identity()));
                List<WdDeliveryBillPO> deliveryBillPOS = iWdDeliveryBillRepository.queryDeliveryOrderBillByBillNo(Lists.newArrayList(billMap.keySet()));
                Map<String, WdDeliveryBillPO> wdDeliveryBillPOMap = deliveryBillPOS.stream().collect(Collectors.toMap(WdDeliveryBillPO::getBillNo, Function.identity()));
                Map<String, List<WdDeliveryBillDetailPO>> wdDeliveryBillPOSMap = wdDeliveryBillPOS.stream().collect(Collectors.groupingBy(WdDeliveryBillDetailPO::getAddReduceKey));

                // todo 非直流场景 可以不存在配转采订单
                allocateResps.addAll(wdDeliveryBillPOSMap.entrySet().stream().map(entry -> {
                    PmsOrderAllocateDetail orderAllocateDetail = allocateDetailMap.get(entry.getKey());
                    PmsOrderAllocateResp resp = CglibCopier.copy(orderAllocateDetail, PmsOrderAllocateResp.class);
                    List<WdDeliveryBillDetailPO> detailPOS = entry.getValue().stream().filter(e -> purchByDeliveryNosMap.containsKey(e.getBillNo() + "-" + e.getInsideId())).collect(Collectors.toList());
                    BigDecimal adjustQty = orderAllocateDetail.getAdjustQty();
                    BigDecimal tempAdjustQty = adjustQty.add(BigDecimal.ZERO).abs();
                    if (CollectionUtils.isNotEmpty(detailPOS)) {
                        List<PmsOrderAllocateOrderInfo> pmsOrderAllocateOrderInfos = Lists.newArrayList();
                        BigDecimal countPurchaseQty = BigDecimal.ZERO;
                        BigDecimal countTransferQty = BigDecimal.ZERO;
                        for (WdDeliveryBillDetailPO detailPO : detailPOS) {
                            if (tempAdjustQty.compareTo(detailPO.getDeliveryQty()) > 0) { //说明减到0还有剩余
                                tempAdjustQty = tempAdjustQty.subtract(detailPO.getDeliveryQty());
                                PmsOrderAllocateOrderInfo transfer = PmsOrderAllocateOrderInfo.builder()
                                        .billType(DirectSignEnum.isDirect(detailPO.getDirectSign()) ? AddReduceTypeEnum.DIRECT_TRANSFER.getCode() : AddReduceTypeEnum.STORE_TRANSFER.getCode())
                                        .auditTime(wdDeliveryBillPOMap.get(detailPO.getBillNo()).getApproveTime())
                                        .billNo(detailPO.getBillNo())
                                        .insideId(detailPO.getInsideId())
                                        .purchUnitRate(detailPO.getOrderUnitRate())
                                        .orderQty(detailPO.getDeliveryQty())
                                        .expectAdjustQty(detailPO.getDeliveryQty().negate())
                                        .tax(detailPO.getDeliveryTax())
                                        .salePrice(detailPO.getSalePrice())
                                        .saleMoney(detailPO.getSaleMoney())
                                        .build();
                                pmsOrderAllocateOrderInfos.add(transfer);
                                countTransferQty.add(detailPO.getDeliveryQty());

                                PurchaseDeliverySkuResp billDetailSumResp = purchByDeliveryNosMap.get(detailPO.getBillNo() + "-" + detailPO.getInsideId());
                                PmsOrderAllocateOrderInfo purch = PmsOrderAllocateOrderInfo.builder()
                                        .billType(DirectSignEnum.isDirect(detailPO.getDirectSign()) ? AddReduceTypeEnum.DIRECT_PURCHASE.getCode() : AddReduceTypeEnum.TRANSFER_TO_PURCHASE.getCode())
                                        .auditTime(billDetailSumResp.getAuditTime())
                                        .billNo(billDetailSumResp.getBillNo())
                                        .insideId(billDetailSumResp.getInsideId())
                                        .purchUnitRate(billDetailSumResp.getPurchUnitRate())
                                        .orderQty(billDetailSumResp.getPurchQty())
                                        .expectAdjustQty(billDetailSumResp.getPurchQty().negate())
                                        .tax(billDetailSumResp.getPurchTax())
                                        .salePrice(billDetailSumResp.getSalePrice())
                                        .saleMoney(billDetailSumResp.getSaleMoney())
                                        .build();
                                countPurchaseQty.add(billDetailSumResp.getPurchQty());
                                pmsOrderAllocateOrderInfos.add(purch);
                            } else {
                                BigDecimal downToPackingMultiple = roundDownToPackingMultiple(tempAdjustQty, orderAllocateDetail.getUnitRate());
                                if (downToPackingMultiple.compareTo(BigDecimal.ZERO) > 0) {
                                    PmsOrderAllocateOrderInfo transfer = PmsOrderAllocateOrderInfo.builder()
                                            .billType(DirectSignEnum.isDirect(detailPO.getDirectSign()) ? AddReduceTypeEnum.DIRECT_TRANSFER.getCode() : AddReduceTypeEnum.STORE_TRANSFER.getCode())
                                            .auditTime(wdDeliveryBillPOMap.get(detailPO.getBillNo()).getApproveTime())
                                            .billNo(detailPO.getBillNo())
                                            .insideId(detailPO.getInsideId())
                                            .purchUnitRate(detailPO.getOrderUnitRate())
                                            .orderQty(detailPO.getDeliveryQty())
                                            .expectAdjustQty(downToPackingMultiple.negate())
                                            .tax(detailPO.getDeliveryTax())
                                            .salePrice(detailPO.getSalePrice())
                                            .saleMoney(detailPO.getSaleMoney())
                                            .build();
                                    pmsOrderAllocateOrderInfos.add(transfer);
                                    countTransferQty.add(transfer.getExpectAdjustQty());

                                    PurchaseDeliverySkuResp billDetailSumResp = purchByDeliveryNosMap.get(detailPO.getBillNo() + "-" + detailPO.getInsideId());
                                    PmsOrderAllocateOrderInfo purch = PmsOrderAllocateOrderInfo.builder()
                                            .billType(DirectSignEnum.isDirect(detailPO.getDirectSign()) ? AddReduceTypeEnum.DIRECT_PURCHASE.getCode() : AddReduceTypeEnum.TRANSFER_TO_PURCHASE.getCode())
                                            .auditTime(billDetailSumResp.getAuditTime())
                                            .billNo(billDetailSumResp.getBillNo())
                                            .insideId(billDetailSumResp.getInsideId())
                                            .purchUnitRate(billDetailSumResp.getPurchUnitRate())
                                            .orderQty(billDetailSumResp.getPurchQty())
                                            .expectAdjustQty(downToPackingMultiple.negate())
                                            .tax(billDetailSumResp.getPurchTax())
                                            .salePrice(billDetailSumResp.getSalePrice())
                                            .saleMoney(billDetailSumResp.getSaleMoney())
                                            .build();
                                    pmsOrderAllocateOrderInfos.add(purch);
                                    countPurchaseQty.add(purch.getExpectAdjustQty());
                                }
                                break;
                            }
                        }
                        resp.setOrderAllocateOrderInfoList(pmsOrderAllocateOrderInfos);
                        resp.setExpectedAdjustPurchQty(countPurchaseQty);
                        resp.setExpectedAdjustTransferQty(countPurchaseQty);
                        return resp;
                    } else if (DirectSignEnum.NOT_DIRECT.getCode().equals(orderAllocateDetail.getDirectSign())) {
                        List<PmsOrderAllocateOrderInfo> pmsOrderAllocateOrderInfos = Lists.newArrayList();
                        BigDecimal countTransferQty = BigDecimal.ZERO;
                        for (WdDeliveryBillDetailPO detailPO : detailPOS) {
                            if (tempAdjustQty.compareTo(detailPO.getDeliveryQty()) > 0) { //说明减到0还有剩余
                                tempAdjustQty = tempAdjustQty.subtract(detailPO.getDeliveryQty());
                                PmsOrderAllocateOrderInfo transfer = PmsOrderAllocateOrderInfo.builder()
                                        .billType(DirectSignEnum.isDirect(detailPO.getDirectSign()) ? AddReduceTypeEnum.DIRECT_TRANSFER.getCode() : AddReduceTypeEnum.STORE_TRANSFER.getCode())
                                        .auditTime(wdDeliveryBillPOMap.get(detailPO.getBillNo()).getApproveTime())
                                        .billNo(detailPO.getBillNo())
                                        .insideId(detailPO.getInsideId())
                                        .purchUnitRate(detailPO.getOrderUnitRate())
                                        .orderQty(detailPO.getDeliveryQty())
                                        .expectAdjustQty(detailPO.getDeliveryQty().negate())
                                        .tax(detailPO.getDeliveryTax())
                                        .salePrice(detailPO.getSalePrice())
                                        .saleMoney(detailPO.getSaleMoney())
                                        .build();
                                pmsOrderAllocateOrderInfos.add(transfer);
                                countTransferQty.add(detailPO.getDeliveryQty());
                            } else {
                                BigDecimal downToPackingMultiple = roundDownToPackingMultiple(tempAdjustQty, orderAllocateDetail.getUnitRate());
                                if (downToPackingMultiple.compareTo(BigDecimal.ZERO) > 0) {
                                    PmsOrderAllocateOrderInfo transfer = PmsOrderAllocateOrderInfo.builder()
                                            .billType(DirectSignEnum.isDirect(detailPO.getDirectSign()) ? AddReduceTypeEnum.DIRECT_TRANSFER.getCode() : AddReduceTypeEnum.STORE_TRANSFER.getCode())
                                            .auditTime(wdDeliveryBillPOMap.get(detailPO.getBillNo()).getApproveTime())
                                            .billNo(detailPO.getBillNo())
                                            .insideId(detailPO.getInsideId())
                                            .purchUnitRate(detailPO.getOrderUnitRate())
                                            .orderQty(detailPO.getDeliveryQty())
                                            .expectAdjustQty(downToPackingMultiple.negate())
                                            .tax(detailPO.getDeliveryTax())
                                            .salePrice(detailPO.getSalePrice())
                                            .saleMoney(detailPO.getSaleMoney())
                                            .build();
                                    pmsOrderAllocateOrderInfos.add(transfer);
                                    countTransferQty.add(transfer.getExpectAdjustQty());
                                }
                                break;
                            }
                        }
                        resp.setOrderAllocateOrderInfoList(pmsOrderAllocateOrderInfos);
                        resp.setExpectedAdjustTransferQty(countTransferQty);
                        return resp;
                    }
                    return null;
                }).collect(Collectors.toList()));
            }
        }
        return Results.ofSuccess(allocateResps);

    }

    /**
     * 追加追减提交
     *
     * @param pmsOrderAllocateReq
     * @return
     */
    @Override
    public Result addReduceSubmit(PmsOrderAllocateSubmitReq pmsOrderAllocateReq) {
        Result<List<PmsOrderAllocateResp>> orderAllocate = orderAllocate(pmsOrderAllocateReq);
        if (!orderAllocate.isSuccess()) {
            return Results.ofCommonError(PmsErrorCodeEnum.SC_PMS_001_B001);
        }
        List<PmsOrderAllocateResp> data = orderAllocate.getData();

        // 筛选新增需求单明细
        List<PmsOrderAllocateResp> allocateResps = data.stream()
                .filter(e -> (e.getTransferNum() != null && e.getTransferNum().compareTo(BigDecimal.ZERO) > 0) || (e.getPurchNum() != null && e.getPurchNum().compareTo(BigDecimal.ZERO) > 0))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(allocateResps)) {
            List<PmsAddReduceSource> addReduceSources = Lists.newArrayList();
            for (PmsOrderAllocateResp detail : allocateResps) {
                addReduceSources.addAll(detail.getAddReduceSources());
            }
            List<String> billNos = addReduceSources.stream().map(PmsAddReduceSource::getBillNo).distinct().collect(Collectors.toList());
            List<PmsApplyBillPO> listByBillNos = pmsApplyBillRepositoryService.getListByBillNos(billNos);

            AutoApplyToDemandDTO autoApplyToDemandDTO = new AutoApplyToDemandDTO();
            autoApplyToDemandDTO.setDeliveryToPurchParamDTO(pmsOrderAllocateReq.getDeliveryToPurchParamDTO());
            autoApplyToDemandDTO.setBillSource(PmsDemanBillSourceEnum.ZHUI_JIA_JIAN.getCode());
            autoApplyToDemandDTO.setApplyBillList(listByBillNos);
            autoApplyToDemandDTO.setApplyBillMap(listByBillNos.stream().collect(Collectors.toMap(PmsApplyBillPO::getBillNo, e -> e)));
            autoApplyToDemandDTO.setApplyBillDetailList(addReduceSources.stream().map(e ->
                    orderApplyBillDetailConvert.convertDetail2PO(e)).collect(Collectors.toList()));
            pmsDemandDomainService.hanlerAutoApplyToDemand(autoApplyToDemandDTO);
            pmsApplyBillDetailRepositoryService.updateAddReduceFlag(addReduceSources);
        }

        // 筛选调整配送单明细
        List<PmsOrderAllocateResp> transferAllocateResps = data.stream()
                .filter(e -> e.getExpectedAdjustTransferQty() != null && e.getExpectedAdjustTransferQty().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(transferAllocateResps)) {
            Map<String, List<PmsOrderAllocateOrderInfo>> allocateOrderInfoMap = new HashMap<>();
            for (PmsOrderAllocateResp transferAllocateResp : transferAllocateResps) {
                allocateOrderInfoMap.putAll(transferAllocateResp.getOrderAllocateOrderInfoList().stream()
                        .filter(e -> e.getBillType().equals(AddReduceTypeEnum.DIRECT_TRANSFER.getCode()) || e.getBillType().equals(AddReduceTypeEnum.STORE_TRANSFER.getCode()))
                        .collect(Collectors.groupingBy(PmsOrderAllocateOrderInfo::getBillNo)));
            }

            List<WdsDeliveryOrderAdjustBaseReq> req = allocateOrderInfoMap.entrySet().stream().map(entry -> WdsDeliveryOrderAdjustBaseReq.builder()
                    .billSource(1)
                    .billNo(entry.getKey())
                    .deliverDate(pmsOrderAllocateReq.getDeliverDeliverDate())
                    .validityDate(pmsOrderAllocateReq.getDeliverValidityDate())
                    .detailList(entry.getValue().stream().map(e -> WdsDeliveryOrderAdjustGoodsReq.builder()
                            .insideId(e.getInsideId())
                            .adjustType(e.getExpectAdjustQty().compareTo(BigDecimal.ZERO)> 0 ? 0 : 1)
                            .adjustQty(e.getExpectAdjustQty().abs())
                            .build()).collect(Collectors.toList()))
                    .build()
            ).collect(Collectors.toList());

            List<WdsDeliveryOrderAdjustResp> wdsDeliveryOrderAdjustResps = iWmsAdjustDeliveryOrderService.adjustDeliveryOrder(req, userUtil.getOpInfoWithThrow());

            List<PmsAddReduceSource> addReduceSources = Lists.newArrayList();
            for (PmsOrderAllocateResp detail : transferAllocateResps) {
                addReduceSources.addAll(detail.getAddReduceSources());
            }
            pmsApplyBillDetailRepositoryService.updateAddReduceFlag(addReduceSources);
        }


        //todo 筛选调整采购单明细
        List<PmsOrderAllocateResp> purchAllocateResps = data.stream()
                .filter(e -> e.getExpectedAdjustTransferQty() != null && e.getExpectedAdjustTransferQty().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(purchAllocateResps)) {
            Map<String, List<PmsOrderAllocateOrderInfo>> purchOrderInfoMap = new HashMap<>();
            for (PmsOrderAllocateResp transferAllocateResp : purchAllocateResps) {
                purchOrderInfoMap.putAll(transferAllocateResp.getOrderAllocateOrderInfoList().stream()
                        .filter(e -> e.getBillType().equals(AddReduceTypeEnum.DIRECT_PURCHASE.getCode()) || e.getBillType().equals(AddReduceTypeEnum.TRANSFER_TO_PURCHASE.getCode()))
                        .collect(Collectors.groupingBy(PmsOrderAllocateOrderInfo::getBillNo)));
            }

            List<PurchaseBillAdjust4AsReq> adjust4AsReqList = purchOrderInfoMap.entrySet().stream().map(entry -> PurchaseBillAdjust4AsReq.builder()
                    .billSource(1)
                    .billNo(entry.getKey())
                    .deliverDate(pmsOrderAllocateReq.getDeliverDeliverDate())
                    .validityDate(pmsOrderAllocateReq.getDeliverValidityDate())
                    .detailList(entry.getValue().stream().map(e -> PurchaseBillAdjust4AsReq.GoodsInfo4As.builder()
                            .insideId(e.getInsideId())
                            .adjustQty(e.getExpectAdjustQty())
                            .build()).collect(Collectors.toList()))
                    .build()
            ).collect(Collectors.toList());
            List<PurchBillOptResp> purchBillOptResps = pmsPurchaseOrderDomainService.adjustPurchBill4As(adjust4AsReqList);
            List<PmsAddReduceSource> addReduceSources = Lists.newArrayList();
            for (PmsOrderAllocateResp detail : purchAllocateResps) {
                addReduceSources.addAll(detail.getAddReduceSources());
            }
            pmsApplyBillDetailRepositoryService.updateAddReduceFlag(addReduceSources);
        }
        return Results.ofSuccess();
    }

    @Override
    public Result<List<ApplyBillDemandResp>> queryDemandApplyList(QueryDemandApplyDTO queryDemandApplyDTO) {
        OpInfo opInfo = userUtil.getDeptOpInfoWithThrow();
        List<Integer> shippingWays = new ArrayList<>();
        List<String> manageDeptCodeList = opInfo.getManageDeptCodeList();
        List<String> deptCodes = queryDemandApplyDTO.getInDeptCode();
        if (!opInfo.getOriginDeptFlag() && CollectionUtils.isNotEmpty(manageDeptCodeList)) {
            if(queryDemandApplyDTO.getShippingWay() != null) {
                if(ShippingWayEnum.DELIVERY.getCode().equals(queryDemandApplyDTO.getShippingWay())) {
                    shippingWays.add(ShippingWayEnum.DELIVERY.getCode());
                    queryDemandApplyDTO.setShippingCodes(manageDeptCodeList);
                } else {
                    shippingWays.add(ShippingWayEnum.PURCHASE.getCode());
                    queryDemandApplyDTO.setInDeptCode(CollectionUtils.isNotEmpty(deptCodes) ? manageDeptCodeList.stream().filter(deptCodes::contains).collect(Collectors.toList()) : manageDeptCodeList);
                }
            } else {
                shippingWays.add(ShippingWayEnum.DELIVERY.getCode());
                shippingWays.add(ShippingWayEnum.PURCHASE.getCode());
                queryDemandApplyDTO.setShippingWay(ShippingWayEnum.PURCHASE.getCode());
                queryDemandApplyDTO.setInDeptCode(CollectionUtils.isNotEmpty(deptCodes) ? manageDeptCodeList.stream().filter(deptCodes::contains).collect(Collectors.toList()) : manageDeptCodeList);
            }
        }

        List<ApplyBillDemandResp> applyBillDemandResps = orderApplyBillConvert.convertDemandPOList2Resp(pmsApplyBillRepositoryService.getApplyDemandList(queryDemandApplyDTO));
        List<ApplyBillDetailDTO> applyBillDetailDTOS = orderApplyBillDetailConvert.convertDetailListPO2DTO(pmsApplyBillDetailRepositoryService.queryDemandApplyDetailList(queryDemandApplyDTO));
        if(shippingWays.size() > 1) {
            shippingWays.add(ShippingWayEnum.DELIVERY.getCode());
            queryDemandApplyDTO.setShippingCodes(manageDeptCodeList);
            queryDemandApplyDTO.setInDeptCode(deptCodes);
            List<ApplyBillDemandResp> deliveryApplyBillDemandResps = orderApplyBillConvert.convertDemandPOList2Resp(pmsApplyBillRepositoryService.getApplyDemandList(queryDemandApplyDTO));
            if(CollectionUtils.isNotEmpty(deliveryApplyBillDemandResps)) {
                applyBillDemandResps.addAll(deliveryApplyBillDemandResps);
            }

            applyBillDetailDTOS.addAll(orderApplyBillDetailConvert.convertDetailListPO2DTO(pmsApplyBillDetailRepositoryService.queryDemandApplyDetailList(queryDemandApplyDTO)));
        }

        Map<String, List<ApplyBillDetailDTO>> detailMap = applyBillDetailDTOS.stream().collect(Collectors.groupingBy(ApplyBillDetailDTO::getBillNo));
        for (ApplyBillDemandResp applyBillDemandResp : applyBillDemandResps) {
            applyBillDemandResp.setApplyBillDetailDTOList(detailMap.get(applyBillDemandResp.getBillNo()));
        }
        return Results.ofSuccess(applyBillDemandResps);
    }

    /**
     * 查询需求响应明细列表
     *
     * @param queryDemandApplyDTO
     * @return
     */
    @Override
    public Result<List<ApplyBillDetailDTO>> queryDemandApplyDetailList(QueryDemandApplyDTO queryDemandApplyDTO) {
        queryDemandApplyDTO.setStatus(2);
        return Results.ofSuccess(orderApplyBillDetailConvert.convertDetailListPO2DTO(pmsApplyBillDetailRepositoryService.queryDemandApplyDetailList(queryDemandApplyDTO)));
    }

    @Override
    public Result<ApplyBillDTO> printOrderApply(QueryApplyBillDetailReq req) {
        return queryApplyDetailList(req);
    }

    @Override
    public Result<List<QueryOrder4PrintResp>> queryOrder4Print(QueryOrder4PrintReq queryApplyBillDetailReq) {
        List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = pmsApplyBillDetailRepositoryService.queryListByBillNos(queryApplyBillDetailReq.getBillNos());
        List<QueryOrder4PrintResp> resps = new ArrayList<>();
        if(CollectionUtils.isEmpty(pmsApplyBillDetailPOS)) {
            Map<String, List<PmsApplyBillDetailPO>> purchBillMap = pmsApplyBillDetailPOS.stream().filter(pmsApplyBillDetailPO -> StringUtils.isNotEmpty(pmsApplyBillDetailPO.getPurchBillNo()))
                    .collect(Collectors.groupingBy(PmsApplyBillDetailPO::getPurchBillNo));
            Map<String, List<PmsApplyBillDetailPO>> deliveryBillMap = pmsApplyBillDetailPOS.stream().filter(pmsApplyBillDetailPO -> StringUtils.isNotEmpty(pmsApplyBillDetailPO.getDeliveryBillNo()))
                    .collect(Collectors.groupingBy(PmsApplyBillDetailPO::getDeliveryBillNo));

            purchBillMap.forEach((purchBillNo, detailPOList) -> resps.add(QueryOrder4PrintResp.builder()
                    .orderId(purchBillNo)
                    .shippingWay(ShippingWayEnum.PURCHASE.getCode())
                    .shipperInfos(detailPOList.stream().map(detailPO -> ShipperInfo.builder()
                            .shipperCode(detailPO.getShipperCode())
                            .shipperName(detailPO.getShipperName())
                            .build()).collect(Collectors.toList()))
                    .build()));

            deliveryBillMap.forEach((deliveryBillNo, detailPOList) -> resps.add(QueryOrder4PrintResp.builder()
                    .orderId(deliveryBillNo)
                    .shippingWay(ShippingWayEnum.DELIVERY.getCode())
                    .shipperInfos(detailPOList.stream().map(detailPO -> ShipperInfo.builder()
                            .shipperCode(detailPO.getShipperCode())
                            .shipperName(detailPO.getShipperName())
                            .build()).collect(Collectors.toList()))
                    .build()));
        }

        return Results.ofSuccess(resps);
    }

    @Override
    public Result<String> exportOrderApplyList(QueryApplyBillDTO queryApplyBillDTO) {
        LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();
        Logs.info("用户:{}开始导出", Jsons.toJson(loginUser));
        ExportSpecification specification = PmsOrderApplyProcessor.class.getAnnotation(ExportSpecification.class);
        ExportDataParams params = ExportDataParams.builder()
                .taskCode(specification.code())
                .app(SpringContextUtil.getApplicationName())
                .query(queryApplyBillDTO)
                .tenant(TenantContext.get())
                .bizUserName(Optional.ofNullable(loginUser.getName()).orElse("SYSTEM"))
                .bizUserId(Optional.ofNullable(loginUser.getUid()).map(Object::toString).orElse("1"))
                .build();
        AgeiTaskClient.exportData(params);
        Logs.info("用户:{}结束导出", Jsons.toJson(loginUser));
        return new Result<>();
    }

    @Override
    public Result<String> exportRefundOrderApplyList(QueryApplyBillDTO queryApplyBillDTO) {
        LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();
        Logs.info("用户:{}开始导出", Jsons.toJson(loginUser));
        ExportSpecification specification = PmsOrderApplyRefundProcessor.class.getAnnotation(ExportSpecification.class);
        ExportDataParams params = ExportDataParams.builder()
                .taskCode(specification.code())
                .app(SpringContextUtil.getApplicationName())
                .query(queryApplyBillDTO)
                .tenant(TenantContext.get())
                .bizUserName(Optional.ofNullable(loginUser.getName()).orElse("SYSTEM"))
                .bizUserId(Optional.ofNullable(loginUser.getUid()).map(Object::toString).orElse("1"))
                .build();
        AgeiTaskClient.exportData(params);
        Logs.info("用户:{}结束导出", Jsons.toJson(loginUser));
        return new Result<>();
    }

    @Override
    public Result<String> exportOrderApplyDetail(QueryAcceptDTO queryAcceptDTO) {
        return null;
    }

    @Override
    public Result<String> exportRefundOrderApplyDetail(QueryAcceptDTO queryAcceptDTO) {
        return null;
    }
}
