package com.meta.supplychain.demand.purch.web.controller;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.demand.purch.application.intf.IPmsApplicationManagerService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.pms.req.accept.*;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyBillResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptBillResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptPriceResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptStatisticResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptanceInfoResp;
import com.meta.supplychain.util.UserUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

/**
 * 采购验收单管理Controller
 *
 * <AUTHOR>
 * @date 2025/03/30 02:44
 **/
@Controller
@RequestMapping("${unit-deploy.prefix-main:}/pms/accept")
@Tag(name = "采购验收单管理", description = "采购验收单相关接口")
public class PmsAcceptController {
    @Autowired
    private IPmsApplicationManagerService IPmsApplicationManagerService;
    @Resource
    private UserUtil userUtil;

    @PostMapping("/create")
    @ResponseBody
    @Operation(summary = "新建采购验收", description = "创建采购验收单")
    public Result<ApplyBillResp> createAcceptBill(@RequestBody @Parameter(description = "验收单信息", required = true) AcceptBillDTO acceptBillDTO) {
        return IPmsApplicationManagerService.getPmsAcceptApplicationService().createAcceptBill(acceptBillDTO);
    }

    @PostMapping("/amend")
    @ResponseBody
    @Operation(summary = "修正验收单", description = "对已有验收单进行修正")
    public Result<ApplyBillResp> amendAcceptBill(@RequestBody @Parameter(description = "验收单修正信息", required = true) AmendAcceptBillDTO acceptBillDTO) {
        return IPmsApplicationManagerService.getPmsAcceptApplicationService().amendAcceptBill(acceptBillDTO);
    }

    @PostMapping("/getPrice")
    @ResponseBody
    @Operation(summary = "获取验收单价", description = "获取验收单商品价格")
    public Result<List<AcceptPriceResp>> getAcceptPrice(@RequestBody @Parameter(description = "验收单价查询参数", required = true) QueryAcceptPriceDTO queryAcceptPriceReq) {
        return IPmsApplicationManagerService.getPmsAcceptApplicationService().getAcceptPrice(queryAcceptPriceReq);
    }

    @PostMapping("/audit")
    @ResponseBody
    @Operation(summary = "审核验收单", description = "审核验收单信息")
    public Result<ApplyBillResp> auditAcceptBill(@RequestBody @Parameter(description = "验收单审核信息", required = true) AuditAcceptBillDTO auditAcceptBillDTO) {
        return IPmsApplicationManagerService.getPmsAcceptApplicationService().auditAcceptBill(auditAcceptBillDTO);
    }

    @PostMapping("/list")
    @ResponseBody
    @Operation(summary = "验收单列表查询(pc)", description = "PC端查询验收单列表")
    public Result<PageResult<AcceptBillResp>> queryAcceptList(@RequestBody @Parameter(description = "验收单查询参数", required = true) QueryAcceptDTO queryAcceptanceDTO) {
        OpInfo operatorInfo = userUtil.getDeptOpInfoWithThrow();
        queryAcceptanceDTO.setOperatorInfo(operatorInfo);
        return IPmsApplicationManagerService.getPmsAcceptApplicationService().queryAcceptList(queryAcceptanceDTO);
    }

    @PostMapping("/list/app")
    @ResponseBody
    @Operation(summary = "验收单列表查询(app)", description = "APP端查询验收单列表")
    public Result<PageResult<AcceptBillResp>> queryAcceptList4App(@RequestBody @Parameter(description = "APP验收单查询参数", required = true) QueryAccept4AppDTO queryAccept4AppDTO) {
        return IPmsApplicationManagerService.getPmsAcceptApplicationService().queryAcceptList4App(queryAccept4AppDTO);
    }

    @PostMapping("/count/stateGroup")
    @ResponseBody
    @Operation(summary = "按状态分组查询验收单数量(app)", description = "APP端按状态分组查询验收单数量")
    public Result<List<AcceptStatisticResp>> queryCount4StateGroup(@RequestBody @Parameter(description = "验收单状态查询参数", required = true) QueryAccept4AppDTO queryAccept4AppDTO) {
        return IPmsApplicationManagerService.getPmsAcceptApplicationService().queryCount4StateGroup(queryAccept4AppDTO);
    }

    @PostMapping("/update")
    @ResponseBody
    @Operation(summary = "更新验收单", description = "更新验收单信息")
    public Result<ApplyBillResp> updateAcceptBill(@RequestBody @Parameter(description = "验收单更新信息", required = true) AcceptBillDTO acceptBillDTO) {
        return IPmsApplicationManagerService.getPmsAcceptApplicationService().updateAcceptBill(acceptBillDTO);
    }

    @PostMapping("/reversal")
    @ResponseBody
    @Operation(summary = "冲红验收单", description = "冲红验收单")
    public Result<ApplyBillResp> reversalAcceptBill(@RequestBody @Parameter(description = "验收单冲红信息", required = true) CancelAcceptBillDTO acceptBillDTO) {
        return IPmsApplicationManagerService.getPmsAcceptApplicationService().reversalAcceptBill(acceptBillDTO);
    }

    @PostMapping("/cancel")
    @ResponseBody
    @Operation(summary = "作废验收单", description = "作废验收单")
    public Result<ApplyBillResp> cancelAcceptBill(@RequestBody @Parameter(description = "验收单作废信息", required = true) CancelAcceptBillDTO acceptBillDTO) {
        return IPmsApplicationManagerService.getPmsAcceptApplicationService().cancelAcceptBill(acceptBillDTO);
    }


    @PostMapping("/detail")
    @ResponseBody
    @Operation(summary = "验收单明细", description = "获取验收单明细")
    public Result<AcceptBillResp> acceptDetail(@RequestBody @Parameter(description = "验收单明细查询参数", required = true) QueryAcceptDetailReq acceptDetailReq) {
        return IPmsApplicationManagerService.getPmsAcceptApplicationService().acceptDetail(acceptDetailReq);
    }

    @PostMapping("/print/detail")
    @ResponseBody
    @Operation(summary = "验收单明细打印", description = "获取验收单明细用于打印")
    public Result<AcceptanceInfoResp> printAcceptDetail(@RequestBody @Parameter(description = "验收单明细查询参数", required = true) QueryAcceptDetailReq acceptDetailReq) {
        return IPmsApplicationManagerService.getPmsAcceptApplicationService().printAcceptDetail(acceptDetailReq);
    }

    @PostMapping("/export")
    @ResponseBody
    @Operation(summary = "导出验收单", description = "导出验收单数据")
    public Result<String> exportAcceptList(@RequestBody @Parameter(description = "验收单导出查询参数", required = true) QueryAcceptDTO queryAcceptanceDTO) {
        return IPmsApplicationManagerService.getPmsAcceptApplicationService().exportAcceptList(queryAcceptanceDTO);
    }

    @PostMapping("/export/detail")
    @ResponseBody
    @Operation(summary = "导出验收单明细", description = "导出验收单明细数据")
    public Result<String> exportAcceptListDetail(@RequestBody @Parameter(description = "验收单明细导出查询参数", required = true) QueryAcceptDetailReq queryAcceptanceDTO) {
        return IPmsApplicationManagerService.getPmsAcceptApplicationService().exportAcceptListDetail(queryAcceptanceDTO);
    }

}
