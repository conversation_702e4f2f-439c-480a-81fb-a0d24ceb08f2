package com.meta.supplychain.demand.purch.domain.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.demand.purch.domain.intf.PmsAcceptDomainService;
import com.meta.supplychain.entity.dto.pms.req.accept.*;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyBillResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptBillResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptDetailResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptPriceResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptStatisticResp;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsAcceptBillDetailRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsAcceptBillRepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/03/30 01:43
 **/
@Service
public class PmsReceiveDomainServiceImpl implements PmsAcceptDomainService {

    @Autowired
    private PmsAcceptBillDetailRepositoryService pmsAcceptBillDetailRepositoryService;

    @Autowired
    private PmsAcceptBillRepositoryService pmsAcceptBillRepositoryService;


    @Override
    public ApplyBillResp createAcceptBill(AcceptBillDTO acceptBillDTO) {
        return null;
    }

    @Override
    public ApplyBillResp amendAcceptBill(AmendAcceptBillDTO acceptBillDTO) {
        return null;
    }

    @Override
    public List<AcceptPriceResp> getAcceptPrice(QueryAcceptPriceDTO queryAcceptPriceReq) {
        return Collections.emptyList();
    }

    @Override
    public ApplyBillResp auditAcceptBill(AuditAcceptBillDTO auditAcceptBillDTO) {
        return null;
    }

    @Override
    public ApplyBillResp updateAcceptBill(AcceptBillDTO acceptBillDTO) {
        return null;
    }

    @Override
    public ApplyBillResp reversalAcceptBill(CancelAcceptBillDTO acceptBillDTO) {
        return null;
    }

    @Override
    public ApplyBillResp cancelAcceptBill(CancelAcceptBillDTO acceptBillDTO) {
        return null;
    }

    @Override
    public Page<AcceptBillResp> queryAcceptList(QueryAcceptDTO queryAcceptanceDTO) {
        return null;
    }

    @Override
    public Page<AcceptBillResp> queryAcceptList4App(QueryAccept4AppDTO queryAccept4AppDTO) {
        return null;
    }

    @Override
    public List<AcceptStatisticResp> queryCount4StateGroup(QueryAccept4AppDTO queryAccept4AppDTO) {
        return Collections.emptyList();
    }

    @Override
    public AcceptBillDTO printAcceptDetail(QueryAcceptDetailReq acceptDetailReq) {
        return null;
    }

    @Override
    public Page<AcceptDetailResp> queryAcceptDetailList(QueryAcceptDetailReq req) {
        return null;
    }

    @Override
    public String exportAcceptList(QueryAcceptDTO queryAcceptanceDTO) {
        return "";
    }

    @Override
    public String exportRefundAcceptList(QueryAcceptDTO queryAcceptanceDTO) {
        return "";
    }

    @Override
    public String exportAcceptListDetail(QueryAcceptDTO queryAcceptanceDTO) {
        return "";
    }

    @Override
    public String exportRefundAcceptListDetail(QueryAcceptDTO queryAcceptanceDTO) {
        return "";
    }
}
