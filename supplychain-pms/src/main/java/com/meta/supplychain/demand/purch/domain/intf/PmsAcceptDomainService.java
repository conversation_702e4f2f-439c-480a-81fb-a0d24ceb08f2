package com.meta.supplychain.demand.purch.domain.intf;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.entity.dto.pms.req.accept.*;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyBillResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptBillResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptDetailResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptPriceResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptStatisticResp;

import java.util.List;

public interface PmsAcceptDomainService {

    //todo 新建采购验收
    ApplyBillResp createAcceptBill(AcceptBillDTO acceptBillDTO);

    //todo 修正验收单
    ApplyBillResp amendAcceptBill(AmendAcceptBillDTO acceptBillDTO);

    //todo 获取验收单价
    List<AcceptPriceResp> getAcceptPrice(QueryAcceptPriceDTO queryAcceptPriceReq);

    //todo 审核验收单
    ApplyBillResp auditAcceptBill(AuditAcceptBillDTO auditAcceptBillDTO);

    //todo 更新验收单
    ApplyBillResp updateAcceptBill(AcceptBillDTO acceptBillDTO);

    //todo 冲红验收单
    ApplyBillResp reversalAcceptBill(CancelAcceptBillDTO acceptBillDTO);

    //todo 作废验收单
    ApplyBillResp cancelAcceptBill(CancelAcceptBillDTO acceptBillDTO);

    //todo 验收单列表查询(pc)
    Page<AcceptBillResp> queryAcceptList(QueryAcceptDTO queryAcceptanceDTO);

    //todo 验收单列表查询(app)
    Page<AcceptBillResp> queryAcceptList4App(QueryAccept4AppDTO queryAccept4AppDTO);

    //todo 按状态分组查询验收单数量(app)
    List<AcceptStatisticResp> queryCount4StateGroup(QueryAccept4AppDTO queryAccept4AppDTO);

    //todo 验收单明细打印
    AcceptBillDTO printAcceptDetail(QueryAcceptDetailReq acceptDetailReq);


    Page<AcceptDetailResp> queryAcceptDetailList(QueryAcceptDetailReq req);

    //todo 导出验收单
    String exportAcceptList(QueryAcceptDTO queryAcceptanceDTO);

    //todo 导出退货单
    String exportRefundAcceptList(QueryAcceptDTO queryAcceptanceDTO);

    //todo 导出验收单明细
    String exportAcceptListDetail(QueryAcceptDTO queryAcceptanceDTO);

    //todo 导出验收单明细
    String exportRefundAcceptListDetail(QueryAcceptDTO queryAcceptanceDTO);
}