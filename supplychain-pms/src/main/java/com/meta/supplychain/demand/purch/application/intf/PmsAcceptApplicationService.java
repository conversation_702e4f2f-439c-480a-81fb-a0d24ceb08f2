package com.meta.supplychain.demand.purch.application.intf;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.entity.dto.pms.req.accept.*;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyBillResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.*;

import java.util.List;

public interface PmsAcceptApplicationService {

    //todo 新建采购验收
    Result<ApplyBillResp> createAcceptBill(AcceptBillDTO acceptBillDTO);

    //todo 修正验收单
    Result<ApplyBillResp> amendAcceptBill(AmendAcceptBillDTO acceptBillDTO);

    //todo 获取验收单价
    Result<List<AcceptPriceResp>> getAcceptPrice(QueryAcceptPriceDTO queryAcceptPriceReq);

    //todo 审核验收单
    Result<ApplyBillResp> auditAcceptBill(AuditAcceptBillDTO auditAcceptBillDTO);

    //todo 更新验收单
    Result<ApplyBillResp> updateAcceptBill(AcceptBillDTO acceptBillDTO);

    //todo 冲红验收单
    Result<ApplyBillResp> reversalAcceptBill(CancelAcceptBillDTO acceptBillDTO);

    //todo 作废验收单
    Result<ApplyBillResp> cancelAcceptBill(CancelAcceptBillDTO acceptBillDTO);

    //todo 验收单列表查询(pc)
    Result<PageResult<AcceptBillResp>> queryAcceptList(QueryAcceptDTO queryAcceptanceDTO);

    //todo 验收单列表查询(app)
    Result<PageResult<AcceptBillResp>> queryAcceptList4App(QueryAccept4AppDTO queryAccept4AppDTO);

    //todo 按状态分组查询验收单数量(app)
    Result<List<AcceptStatisticResp>> queryCount4StateGroup(QueryAccept4AppDTO queryAccept4AppDTO);

    //todo 验收单明细打印
    Result<AcceptanceInfoResp> printAcceptDetail(QueryAcceptDetailReq acceptDetailReq);

    //todo 验收单明细打印
    Result<AcceptBillResp> acceptDetail(QueryAcceptDetailReq acceptDetailReq);

    List<AcceptDetailResp> queryAcceptDetailList(QueryAcceptDetailReq req);

    //todo 导出验收单
    Result<String> exportAcceptList(QueryAcceptDTO queryAcceptanceDTO);

    //todo 导出退货单
    Result<String> exportRefundAcceptList(QueryAcceptDTO queryAcceptanceDTO);

    //todo 导出验收单明细
    Result<String> exportAcceptListDetail(QueryAcceptDetailReq queryAcceptanceDTO);

    //todo 导出验收单明细
    Result<String> exportRefundAcceptListDetail(QueryAcceptDTO queryAcceptanceDTO);

}