package com.meta.supplychain.demand.purch.application.intf;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.entity.dto.pms.req.accept.QueryAcceptDTO;
import com.meta.supplychain.entity.dto.pms.req.addReduce.PmsOrderAllocateReq;
import com.meta.supplychain.entity.dto.pms.req.addReduce.PmsOrderAllocateSubmitReq;
import com.meta.supplychain.entity.dto.pms.req.apply.ApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.ApplyBillGoodsDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.ApplyPriceDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.BatchApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.CancelApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryAddReduceDetailReq;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryApplyBillDetailReq;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryApplyOrder4AppDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryDemandApplyDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryUnfinishedApplyDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.*;
import com.meta.supplychain.entity.dto.pms.resp.ApplyBillDemandResp;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyBillResp;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyPriceResp;
import com.meta.supplychain.entity.dto.pms.resp.addReduce.PmsAddReduceDetailResp;
import com.meta.supplychain.entity.dto.pms.resp.addReduce.PmsOrderAllocateResp;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyStatisticResp;
import com.meta.supplychain.entity.dto.pms.resp.apply.QueryOrder4PrintResp;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface PmsOrderApplyApplicationService {

    // 新增订货申请
    @Transactional
    Result<ApplyBillResp> createOrderApply(ApplyBillDTO applyBillDTO);

    // 更新订货申请
    @Transactional
    Result<ApplyBillResp> updateOrderApply(ApplyBillDTO applyBillDTO);

    // 审核后更新订货申请
    @Transactional
    Result updateAuditOrderApply(ApplyBillDTO applyBillDTO);

    // 查询申请单价
    Result<List<ApplyPriceResp>> getApplyPrice(ApplyPriceDTO applyPriceDTO);

    //  订货申请审核
    @Transactional
    Result<List<ApplyBillResp>> auditOrderApply(BatchApplyBillDTO batchApplyBillDTO);

    // 取消申请单
    @Transactional
    Result<ApplyBillResp> cancelOrderApply(CancelApplyBillDTO cancelApplyBillDTO);

    /**
     * 作废需求单对应申请单
     * @param cancelApplyBillDTOs
     * @return
     */
    @Transactional
    Result<ApplyBillResp> cancelOrderApply4Demand(List<CancelApplyDetail4DemandDTO> cancelApplyBillDTOs);


    Result ladingBillWriteBack(List<LadingBillWriteBackDTO> ladingBillWriteBackDTOS);

    /**
     * 查询申请单列表(app)
     */
    PageResult<ApplyBillDTO> queryOrderList4App(QueryApplyOrder4AppDTO applyOrder4AppDTO);

    /**
     * 查询未完结申请单
     */
    Result<List<ApplyBillGoodsDTO>> queryUnfinishedOrder(QueryUnfinishedApplyDTO queryUnfinishedApplyDTO);

    /**
     * 查询申请单列表
     */
    Result<PageResult<ApplyBillDTO>> queryApplyList(QueryApplyBillDTO queryApplyBillDTO);

    Result<ApplyBillDTO> queryApplyDetailList(QueryApplyBillDetailReq queryApplyBillDetailReq);

    /**
     * 查询追加追减申请明细
     * @param queryAddReduceDetailReq
     * @return
     */
    Result<List<PmsAddReduceDetailResp>> queryAddReduceDetailList(QueryAddReduceDetailReq queryAddReduceDetailReq);

    /**
     * 按验收状态分组查询数量
     * @param applyOrder4AppDTO
     * @return
     */
    Result<List<ApplyStatisticResp>> queryCount4StateGroup(QueryApplyOrder4AppDTO applyOrder4AppDTO);

    /**
     * 订单分配
     * @param pmsOrderAllocateReq
     * @return
     */
    Result<List<PmsOrderAllocateResp>> orderAllocate(PmsOrderAllocateReq pmsOrderAllocateReq);

    /**
     * 追加追减提交
     * @param pmsOrderAllocateReq
     * @return
     */
    Result addReduceSubmit(PmsOrderAllocateSubmitReq pmsOrderAllocateReq);

    /**
     * 查询需求响应列表
     * @param queryDemandApplyDTO
     * @return
     */
    Result<List<ApplyBillDemandResp>> queryDemandApplyList(QueryDemandApplyDTO queryDemandApplyDTO);

    /**
     * 查询需求响应明细列表
     * @param queryDemandApplyDTO
     * @return
     */
    Result<List<ApplyBillDetailDTO>> queryDemandApplyDetailList(QueryDemandApplyDTO queryDemandApplyDTO);

    /**
     * 申请单打印
     */
    Result<ApplyBillDTO> printOrderApply(QueryApplyBillDetailReq queryApplyBillDetailReq);
    Result<List<QueryOrder4PrintResp>> queryOrder4Print(QueryOrder4PrintReq queryApplyBillDetailReq);

    //todo 导出验收单
    Result<String> exportOrderApplyList(QueryApplyBillDTO queryApplyBillDTO);

    //todo 导出验退单
    Result<String> exportRefundOrderApplyList(QueryApplyBillDTO queryApplyBillDTO);

    //todo 导出验收单明细
    Result<String> exportOrderApplyDetail(QueryAcceptDTO queryAcceptDTO);

    //todo 导出验退单明细
    Result<String> exportRefundOrderApplyDetail(QueryAcceptDTO queryAcceptDTO);

}