package com.meta.supplychain.demand.purch.web.controller;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import com.meta.supplychain.demand.purch.application.intf.IPmsApplicationManagerService;
import com.meta.supplychain.entity.dto.pms.req.addReduce.PmsOrderAllocateReq;
import com.meta.supplychain.entity.dto.pms.req.addReduce.PmsOrderAllocateSubmitReq;
import com.meta.supplychain.entity.dto.pms.req.apply.*;
import com.meta.supplychain.entity.dto.pms.resp.ApplyBillDemandResp;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyBillResp;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyPriceResp;
import com.meta.supplychain.entity.dto.pms.resp.addReduce.PmsAddReduceDetailResp;
import com.meta.supplychain.entity.dto.pms.resp.addReduce.PmsOrderAllocateResp;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyStatisticResp;
import com.meta.supplychain.entity.dto.pms.resp.apply.QueryOrder4PrintResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 订货申请单管理Controller
 *
 * <AUTHOR>
 * @date 2025/03/30 02:44
 **/
@Controller
@RequestMapping("${unit-deploy.prefix-main:}/pms/order/apply")
@Tag(name = "订货申请单管理", description = "订货申请单相关接口")
public class PmsOrderApplyController {
    @Autowired
    private IPmsApplicationManagerService IPmsApplicationManagerService;


    @PostMapping("/create")
    @ResponseBody
    @Operation(summary = "新增订货申请", description = "创建订货申请单")
    public Result<ApplyBillResp> createOrderApply(@RequestBody @Parameter(description = "订货申请信息", required = true) ApplyBillDTO applyBillDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().createOrderApply(applyBillDTO);
    }

    @PostMapping("/update")
    @ResponseBody
    @Operation(summary = "更新订货申请", description = "更新订货申请单信息")
    public Result<ApplyBillResp> updateOrderApply(@RequestBody @Parameter(description = "订货申请信息", required = true) ApplyBillDTO applyBillDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().updateOrderApply(applyBillDTO);
    }

    @PostMapping("/update/audit")
    @ResponseBody
    @Operation(summary = "审核后更新订货申请", description = "审核后更新订货申请单信息")
    public Result<ApplyBillResp> updateAuditOrderApply(@RequestBody @Parameter(description = "订货申请信息", required = true) ApplyBillDTO applyBillDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().updateAuditOrderApply(applyBillDTO);
    }


    @PostMapping("/getPrice")
    @ResponseBody
    @Operation(summary = "查询申请单价", description = "获取订货申请单价格")
    public Result<List<ApplyPriceResp>> getApplyPrice(@RequestBody @Parameter(description = "申请单价查询参数", required = true) ApplyPriceDTO applyPriceDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().getApplyPrice(applyPriceDTO);
    }

    @PostMapping("/audit")
    @ResponseBody
    @Operation(summary = "订货申请审核", description = "审核订货申请单")
    public Result<List<ApplyBillResp>> auditOrderApply(@RequestBody @Parameter(description = "批量订货申请审核信息", required = true) BatchApplyBillDTO batchApplyBillDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().auditOrderApply(batchApplyBillDTO);
    }

    @PostMapping("/cancel")
    @ResponseBody
    @Operation(summary = "取消申请单", description = "取消订货申请单")
    public Result<ApplyBillResp> cancelOrderApply(@RequestBody @Parameter(description = "批量订货申请取消信息", required = true) CancelApplyBillDTO cancelApplyBillDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().cancelOrderApply(cancelApplyBillDTO);
    }

    @PostMapping("/cancel4Demand")
    @ResponseBody
    @Operation(summary = "作废需求单对应申请单", description = "作废需求单对应申请单")
    public Result<ApplyBillResp> cancelOrderApply4Demand(@RequestBody @Parameter(description = "作废需求单对应申请单", required = true) List<CancelApplyDetail4DemandDTO> cancelApplyBillDTOs) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().cancelOrderApply4Demand(cancelApplyBillDTOs);
    }

    @PostMapping("/list/app")
    @ResponseBody
    @Operation(summary = "查询申请单列表(app)", description = "APP端查询订货申请单列表")
    public PageResult<ApplyBillDTO> queryOrderList4App(@RequestBody @Parameter(description = "APP订货申请查询参数", required = true) QueryApplyOrder4AppDTO applyOrder4AppDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().queryOrderList4App(applyOrder4AppDTO);
    }

    @PostMapping("/unfinished/order")
    @ResponseBody
    @Operation(summary = "查询未完结申请单", description = "查询未完结的订货申请单")
    public Result<List<ApplyBillGoodsDTO>> queryUnfinishedOrder(@RequestBody @Parameter(description = "未完结申请单查询参数", required = true) QueryUnfinishedApplyDTO queryUnfinishedApplyDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().queryUnfinishedOrder(queryUnfinishedApplyDTO);
    }

    @PostMapping("/list")
    @ResponseBody
    @Operation(summary = "查询申请单列表", description = "查询订货申请单列表")
    public Result<PageResult<ApplyBillDTO>> queryApplyList(@RequestBody @Parameter(description = "订货申请查询参数", required = true) QueryApplyBillDTO queryApplyBillDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().queryApplyList(queryApplyBillDTO);
    }

    @PostMapping("detail/list")
    @ResponseBody
    @Operation(summary = "查询订货申请单明细列表", description = "查询订货申请单明细列表")
    public Result<ApplyBillDTO> queryApplyDetailList(@RequestBody @Parameter(description = "订货申请查询参数", required = true) QueryApplyBillDetailReq queryApplyBillDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().queryApplyDetailList(queryApplyBillDTO);
    }

    @PostMapping("/count/stateGroup")
    @ResponseBody
    @Operation(summary = "按验收状态分组查询数量", description = "按验收状态分组查询订货申请单数量")
    public Result<List<ApplyStatisticResp>> queryCount4StateGroup(@RequestBody @Parameter(description = "APP订货申请查询参数", required = true) QueryApplyOrder4AppDTO applyOrder4AppDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().queryCount4StateGroup(applyOrder4AppDTO);
    }

    @PostMapping("/demand/list")
    @ResponseBody
    @Operation(summary = "查询需求响应列表", description = "查询需求响应的订货申请单列表")
    public Result<List<ApplyBillDemandResp>> queryDemandApplyList(@RequestBody @Parameter(description = "需求响应查询参数", required = true) QueryDemandApplyDTO queryDemandApplyDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().queryDemandApplyList(queryDemandApplyDTO);
    }

    @PostMapping("/orderAllocate")
    @ResponseBody
    @Operation(summary = "订货申请单追加追减订单分配", description = "订货申请单追加追减订单分配")
    public Result<List<PmsOrderAllocateResp>> orderAllocate(@RequestBody @Parameter(description = "订货申请单追加追减订单分配", required = true) PmsOrderAllocateReq pmsOrderAllocateReq) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().orderAllocate(pmsOrderAllocateReq);
    }

    @PostMapping("/addReduceCommit")
    @ResponseBody
    @Operation(summary = "订货申请单追加追减订单提交", description = "订货申请单追加追减订单提交")
    public Result<List<PmsOrderAllocateResp>> addReduceCommit(@RequestBody @Parameter(description = "订货申请单追加追减订单提交", required = true) PmsOrderAllocateSubmitReq pmsOrderAllocateReq) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().addReduceSubmit(pmsOrderAllocateReq);
    }

    @PostMapping("/queryAddReduceDetailList")
    @ResponseBody
    @Operation(summary = "订货申请追加追减明细查询请求", description = "订货申请追加追减明细查询请求")
    public Result<List<PmsAddReduceDetailResp>> queryAddReduceDetailList(@RequestBody @Parameter(description = "订货申请追加追减明细查询请求", required = true) QueryAddReduceDetailReq addReduceDetailReq) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().queryAddReduceDetailList(addReduceDetailReq);
    }

    @PostMapping("/demand/list/detail")
    @ResponseBody
    @Operation(summary = "查询需求响应单据对应的商品明细", description = "查询需求响应单据对应的商品明细")
    public Result<List<ApplyBillDetailDTO>> queryDemandApplyDetailList(@RequestBody @Parameter(description = "需求响应查询参数", required = true) QueryDemandApplyDTO queryDemandApplyDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().queryDemandApplyDetailList(queryDemandApplyDTO);
    }

    @PostMapping("/print")
    @ResponseBody
    @Operation(summary = "申请单打印", description = "获取订货申请单用于打印")
    public Result<ApplyBillDTO> printOrderApply(@RequestBody @Parameter(description = "订货申请打印查询参数", required = true) QueryApplyBillDetailReq queryApplyBillDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().printOrderApply(queryApplyBillDTO);
    }

    @PostMapping("/queryOrder4Print")
    @ResponseBody
    @Operation(summary = "查询申请单对应订单打印信息", description = "查询申请单对应订单信息用于打印")
    public Result<List<QueryOrder4PrintResp>> queryOrder4Print(@RequestBody @Parameter(description = "订货申请打印查询参数", required = true) QueryOrder4PrintReq queryApplyBillDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().queryOrder4Print(queryApplyBillDTO);
    }

    @PostMapping("/exportOrderApplyList")
    @ResponseBody
    @Operation(summary = "订货申请单导出", description = "订货申请单导出")
    public Result<String> exportOrderApplyList(@RequestBody @Parameter(description = "订货申请单导出查询参数", required = true) QueryApplyBillDTO queryApplyBillDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().exportOrderApplyList(queryApplyBillDTO);
    }

    @PostMapping("/exportRefundOrderApplyList")
    @ResponseBody
    @Operation(summary = "退货申请单导出", description = "退货申请单导出")
    public Result<String> exportRefundOrderApplyList(@RequestBody @Parameter(description = "退货申请单导出查询参数", required = true) QueryApplyBillDTO queryApplyBillDTO) {
        return IPmsApplicationManagerService.getPmsOrderApplyApplicationService().exportRefundOrderApplyList(queryApplyBillDTO);
    }

}
