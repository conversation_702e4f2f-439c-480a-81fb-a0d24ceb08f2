package com.meta.supplychain.demand.purch.processor.orderApplyEvent;

import com.meta.supplychain.entity.bo.OrderApplyEventBO;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyBillResp;
import com.meta.supplychain.enums.OrderApplyStatusEventEnum;

import java.util.List;

public abstract class AuditOrderApplyEventProcessor extends OrderApplyEventProcessor{

    @Override
    public  List<ApplyBillResp> doHandle(OrderApplyEventBO<?> eventBO) {
        //参数组装构造后调用审核
        return null;
    }

    @Override
    public  OrderApplyStatusEventEnum getEventType() {
        return OrderApplyStatusEventEnum.AUDIT;
    }
}
