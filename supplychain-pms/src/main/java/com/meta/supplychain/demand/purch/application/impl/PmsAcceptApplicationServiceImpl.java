package com.meta.supplychain.demand.purch.application.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.linkkids.ageiport.client.AgeiTaskClient;
import cn.linkkids.ageiport.params.ExportDataParams;
import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.json.Jsons;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import cn.linkkids.framework.croods.trace.util.TraceIds;
import com.alibaba.ageiport.common.utils.CollectionUtils;
import com.alibaba.ageiport.common.utils.StringUtils;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meta.supplychain.common.component.service.impl.SupplychainBizBillRuleServiceImpl;
import com.meta.supplychain.common.component.service.impl.commonbiz.CommonFranchiseService;
import com.meta.supplychain.common.component.service.intf.ISupplychainBizGoodsRuleService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonGoodsService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonSupplierService;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.pms.AcceptBillCopier;
import com.meta.supplychain.demand.purch.application.intf.IPmsAccountApplicationService;
import com.meta.supplychain.demand.purch.application.intf.PmsAcceptApplicationService;
import com.meta.supplychain.demand.purch.processor.export.AcceptOrderDetailExportProcessor;
import com.meta.supplychain.demand.purch.processor.export.AcceptOrderExportProcessor;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.bds.req.QueryUpDeptListReq;
import com.meta.supplychain.entity.dto.bds.resp.*;
import com.meta.supplychain.entity.dto.franline.req.FranLineAdjustReq;
import com.meta.supplychain.entity.dto.goods.req.GoodsQueryReq;
import com.meta.supplychain.entity.dto.goods.req.ManageAndCirculationReq;
import com.meta.supplychain.entity.dto.goods.req.UpdatePurchPriceReq;
import com.meta.supplychain.entity.dto.goods.resp.GoodsQueryResp;
import com.meta.supplychain.entity.dto.goods.resp.ManageAndCirculationResp;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractGoodsDeptDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.ContractGoodsDeptQueryDTO;
import com.meta.supplychain.entity.dto.md.resp.supplier.AccountBodyVo;
import com.meta.supplychain.entity.dto.md.resp.supplier.MstSupplierVO;
import com.meta.supplychain.entity.dto.pms.req.accept.*;
import com.meta.supplychain.entity.dto.pms.req.account.AccountBillCreateReq;
import com.meta.supplychain.entity.dto.pms.req.account.AccountBillDetailCreateReq;
import com.meta.supplychain.entity.dto.pms.req.batch.BatchUpdateSummary;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyBillResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.*;
import com.meta.supplychain.entity.dto.pms.resp.account.AccountBillResp;
import com.meta.supplychain.entity.dto.promotion.req.QueryAcceptanceRefundReq;
import com.meta.supplychain.entity.dto.promotion.resp.QueryAcceptanceRefundResp;
import com.meta.supplychain.entity.dto.replenishment.req.AutoBuildTscReq;
import com.meta.supplychain.entity.dto.replenishment.req.TranscodingBillDetail;
import com.meta.supplychain.entity.dto.replenishment.req.TranscodingBillReq;
import com.meta.supplychain.entity.dto.replenishment.resp.TranscodingStrategyResp;
import com.meta.supplychain.entity.dto.stock.StkTaskIReleaseExecuteDto;
import com.meta.supplychain.entity.dto.stock.StkTaskItemExecuteDto;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.dto.stock.req.StockQueryWithDeptAndSkusDTO;
import com.meta.supplychain.entity.dto.stock.resp.BatchExecRowVo;
import com.meta.supplychain.entity.dto.stock.resp.BatchRecordResp;
import com.meta.supplychain.entity.dto.stock.resp.StockVO;
import com.meta.supplychain.entity.po.pms.*;
import com.meta.supplychain.enums.*;
import com.meta.supplychain.enums.goods.GoodsSaleModeEnum;
import com.meta.supplychain.enums.goods.MeasurePropertyEnum;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.enums.pms.*;
import com.meta.supplychain.infrastructure.feign.BaseDataSystemFeignClient;
import com.meta.supplychain.infrastructure.feign.PromotionFeignClient;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.*;
import com.meta.supplychain.util.*;
import com.meta.supplychain.util.spring.SpringContextUtil;
import com.metadata.idaas.client.model.LoginUserDTO;
import com.metadata.idaas.client.util.ClientIdentUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/03/30 01:56
 **/
@Service
public class PmsAcceptApplicationServiceImpl implements PmsAcceptApplicationService {

    @Resource
    private UserUtil userUtil;
    @Autowired
    private PmsAcceptBillRepositoryService pmsAcceptBillRepositoryService;
    @Autowired
    private PmsAcceptBillDetailRepositoryService pmsAcceptBillDetailRepositoryService;
    @Autowired
    private PmsPurchaseOrderRepositoryService pmsPurchaseOrderRepositoryService;
    @Autowired
    private PmsPurchaseDetailRepositoryService pmsPurchaseDetailRepositoryService;
    @Autowired
    private ISupplychainControlEngineService supplychainControlEngineService;
    @Autowired
    private ICommonGoodsService commonGoodsService;
    @Autowired
    private UserResourceUtil userResourceUtil;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ICommonStockService iCommonStockService;
    @Autowired
    private ICommonSupplierService commonSupplierService;
    @Autowired
    private BaseDataSystemFeignClient baseDataSystemFeignClient;
    private final AcceptBillCopier acceptBillCopier = AcceptBillCopier.INSTANCE;
    @Autowired
    private PromotionFeignClient promotionFeignClient;
    @Autowired
    private SupplychainBizBillRuleServiceImpl supplychainBizBillRuleService;
    @Autowired
    @Lazy
    private PmsAcceptServiceHelper pmsAcceptServiceHelper;

    @Autowired
    private ISupplychainBizGoodsRuleService supplychainBizGoodsRuleService;

    @Resource
    private ThreadPoolTaskExecutor acceptTranscodingThreadPool;

    @Autowired
    private PmsBillBatchInfoRepositoryService billBatchInfoRepositoryService;

    @Autowired
    CommonFranchiseService commonFranchiseService;

    @Autowired
    private IPmsAccountApplicationService pmsAccountApplicationService;

    @Autowired
    private BaseStoreUtil baseStoreUtil;

    private static final Long EXPIRE_TIME = 5 * 60 * 1000L;
    /**
     * 创建验收单
     *
     * @param acceptBillDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<ApplyBillResp> createAcceptBill(AcceptBillDTO acceptBillDTO) {
        ApplyBillResp resp = new ApplyBillResp();
        //操作人信息
        OpInfo operatorInfo = userUtil.getOpInfoWithThrow();
        List<String> redisWords = Arrays.asList(TenantContext.get(), "createAcceptBill", operatorInfo.getUserId());
        String redisKey = String.join(SysConstants.UNDERLINE_DELIMITER, redisWords);
        if (!redisUtil.tryLock(redisKey, operatorInfo.getUserId(), EXPIRE_TIME)){
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_D001);
        }
        try {
            // 系统参数校验
            Map<String, GoodsQueryResp.GoodsInfo> goodsInfoMap = new HashMap<>();
            createAcceptBillCheck(acceptBillDTO, goodsInfoMap);
            PmsAcceptBillPO acceptBillPO = new PmsAcceptBillPO();
            acceptBillPO.setBillNo(acceptBillDTO.getBillNo());
            if (acceptBillDTO instanceof AmendAcceptBillDTO) {
                AmendAcceptBillDTO amendAcceptBillDTO = (AmendAcceptBillDTO) acceptBillDTO;
                if (StringUtils.isNotBlank(amendAcceptBillDTO.getAmendAcceptBillNo())) {
                    acceptBillPO.setAmendAcceptBillNo(amendAcceptBillDTO.getAmendAcceptBillNo());
                }
            }
            //创建采购验收单
            pmsAcceptBillRepositoryService.save(acceptBillPO);
            // 验收、明细落库
            dataPersistence(acceptBillDTO, goodsInfoMap, acceptBillPO.getId());
            AuditAcceptBillDTO auditAcceptBillDTO = new AuditAcceptBillDTO();
            auditAcceptBillDTO.setBillNoList(Lists.newArrayList(acceptBillDTO.getBillNo()));
            auditAcceptBillDTO.setBillSource(acceptBillDTO.getBillSource());
            //验收状态校验是否直接审核
            if (acceptBillDTO.getAcceptStatus().equals(PmsAcceptBillStateEnum.AUDIT.getBillStateCode())) {
                auditAcceptBill(auditAcceptBillDTO);
            }
            resp.setBillNumber(acceptBillPO.getBillNo());
        } catch (Exception e) {
            Logs.error("创建单据异常：{}",e.getMessage(),e);
            BizExceptions.throwWithMsg(String.format("创建单据异常:%s",e.getMessage()));
        } finally {
            redisUtil.unlock(redisKey);
        }
        return new Result<>(resp);
    }

    /**
     * 修改验收单
     *
     * @param acceptBillDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<ApplyBillResp> amendAcceptBill(AmendAcceptBillDTO acceptBillDTO) {
        return createAcceptBill(acceptBillDTO);
    }

    /**
     * 获取验收单价
     *
     * @param req
     * @return
     */
    @Override
    public Result<List<AcceptPriceResp>> getAcceptPrice(QueryAcceptPriceDTO req) {
        String enableContract = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(PMSSystemParamEnum.ENABLE_CONTRACT, req.getDeptCode());
        List<AcceptPriceResp> acceptPriceResps = new ArrayList<>();
        Map<String, QueryAcceptPriceDTO.GoodsInfo> billDetailMap = req.getGoodsInfos().stream().collect(Collectors.toMap(QueryAcceptPriceDTO.GoodsInfo::getSkuCode, Function.identity()));
        List<String> goodsCodes = Lists.newArrayList(billDetailMap.keySet());
        if (YesOrNoEnum.YES.getCodeStr().equals(enableContract)) {
            List<String> skuContract = req.getGoodsInfos().stream().map(e -> e.getSkuCode() + "_" + e.getContractiumber()).collect(Collectors.toList());
            if (PmsBillDirectionEnum.REVERSE.getCode().equals(req.getBillDirection())) {
                String refundAcceptPriceType = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(PMSSystemParamEnum.REFUND_ACCEPT_PRICE_TYPE, req.getDeptCode());
                switch (refundAcceptPriceType) {//0，申请单价；1，供应商进价；2，最后进价；3，批次价(先进先出)；4，批次价(高价优先)；
                    case "1":
                        acceptPriceResps.addAll(getContractPrice(req.getDeptCode(), billDetailMap, skuContract));
                        break;
                    case "2":
                        for (QueryAcceptPriceDTO.GoodsInfo billDetail : req.getGoodsInfos()) {
                            AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                            acceptPriceResp.setSkuCode(billDetail.getSkuCode());
                            acceptPriceResp.setSinglePrice(billDetailMap.get(billDetail.getSkuCode()).getPrice());
                            acceptPriceResps.add(acceptPriceResp);
                        }
                        break;
                    case "0":
                        if (StringUtils.isNotEmpty(req.getPurchBillNo())) {
                            QueryWrapper<PmsPurchaseOrderPO> applyBillQueryWrapper = new QueryWrapper<>();
                            applyBillQueryWrapper.eq("bill_no", req.getPurchBillNo());
                            PmsPurchaseOrderPO purchaseOrder = pmsPurchaseOrderRepositoryService.getBaseMapper().selectOne(applyBillQueryWrapper);
                            Assert.isNotNull(purchaseOrder,PmsErrorCodeEnum.SC_PMS_004_P004);
                            List<PmsPurchaseBillDetailPO> pmsPurchaseBillDetails = pmsPurchaseDetailRepositoryService.selectDetailList(req.getPurchBillNo(),  purchaseOrder.getCreateTime());
                            Assert.isTrue(!CollectionUtils.isEmpty(pmsPurchaseBillDetails),PmsErrorCodeEnum.SC_PMS_004_P020);
                            Map<String, PmsPurchaseBillDetailPO> detailMap = pmsPurchaseBillDetails.stream()
                                    .filter(f->f.getSkuType()==0)
                                    .collect(Collectors.toMap(PmsPurchaseBillDetailPO::getSkuCode, Function.identity()));
                            List<AcceptPriceResp> finalAcceptPriceResps = acceptPriceResps;
                            req.getGoodsInfos().forEach(acceptBillDetail -> {
                                AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                                acceptPriceResp.setSinglePrice(detailMap.get(acceptBillDetail.getSkuCode()).getPurchPrice());
                                acceptPriceResp.setSkuCode(acceptBillDetail.getSkuCode());
                                finalAcceptPriceResps.add(acceptPriceResp);
                            });
                        } else {
                            acceptPriceResps.addAll(getContractPrice(req.getDeptCode(), billDetailMap, skuContract));
                        }
                        break;
                    case "3":
                    case "4":
                        List<AcceptPriceResp> finalAcceptPriceResps1 = acceptPriceResps;
                        req.getGoodsInfos().forEach(acceptBillDetail -> {
                            AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                            acceptPriceResp.setSkuCode(acceptBillDetail.getSkuCode());
                            if (CollectionUtils.isNotEmpty(acceptBillDetail.getBatchInfos())) {
                                //批次价取平均值
                                Double batchPrice = acceptBillDetail.getBatchInfos().stream()
                                        .map(QueryAcceptPriceDTO.BatchInfo::getBatchCostPrice)
                                        .collect(Collectors.averagingDouble(BigDecimal::doubleValue));
                                acceptPriceResp.setSinglePrice(BigDecimal.valueOf(batchPrice));
                            } else {
                                acceptPriceResp.setSinglePrice(billDetailMap.get(acceptBillDetail.getSkuCode()).getPrice());
                            }
                            finalAcceptPriceResps1.add(acceptPriceResp);
                        });
                        break;
                }
            }
            else if (PmsBillDirectionEnum.NORMAL.getCode().equals(req.getBillDirection())) {
                String acceptPriceType = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(PMSSystemParamEnum.ACCEPT_PRICE_TYPE, req.getDeptCode());
                switch (acceptPriceType) { //0，申请单价；1，供应商进价；2，两者最低价；
                    case "0":
                        if (StringUtils.isNotEmpty(req.getPurchBillNo())) {
                            QueryWrapper<PmsPurchaseOrderPO> applyBillQueryWrapper = new QueryWrapper<>();
                            applyBillQueryWrapper.eq("bill_no", req.getPurchBillNo());
                            PmsPurchaseOrderPO purchaseOrder = pmsPurchaseOrderRepositoryService.getBaseMapper().selectOne(applyBillQueryWrapper);
                            Assert.isNotNull(purchaseOrder,PmsErrorCodeEnum.SC_PMS_004_P004);
                            List<PmsPurchaseBillDetailPO> pmsPurchaseBillDetails = pmsPurchaseDetailRepositoryService.selectDetailList(req.getPurchBillNo(),  purchaseOrder.getCreateTime());
                            Assert.isTrue(!CollectionUtils.isEmpty(pmsPurchaseBillDetails),PmsErrorCodeEnum.SC_PMS_004_P020);
                            Map<String, PmsPurchaseBillDetailPO> detailMap = pmsPurchaseBillDetails.stream()
                                    .filter(f->f.getSkuType()==0)
                                    .collect(Collectors.toMap(PmsPurchaseBillDetailPO::getSkuCode, Function.identity()));
                            List<AcceptPriceResp> finalAcceptPriceResps2 = acceptPriceResps;
                            req.getGoodsInfos().forEach(acceptBillDetail -> {
                                AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                                acceptPriceResp.setSinglePrice(detailMap.get(acceptBillDetail.getSkuCode()).getPurchPrice());
                                acceptPriceResp.setSkuCode(acceptBillDetail.getSkuCode());
                                finalAcceptPriceResps2.add(acceptPriceResp);
                            });
                        } else {
                            acceptPriceResps.addAll(getSpecialOrContractPrice(req.getDeptCode(), goodsCodes, billDetailMap, skuContract));
                        }
                        break;
                    case "1":
                        acceptPriceResps.addAll(getSpecialOrContractPrice(req.getDeptCode(), goodsCodes, billDetailMap, skuContract));
                        break;
                    case "2":
                        if (StringUtils.isNotEmpty(req.getPurchBillNo())) {
                            QueryWrapper<PmsPurchaseOrderPO> applyBillQueryWrapper = new QueryWrapper<>();
                            applyBillQueryWrapper.eq("bill_no", req.getPurchBillNo());
                            PmsPurchaseOrderPO purchaseOrder = pmsPurchaseOrderRepositoryService.getBaseMapper().selectOne(applyBillQueryWrapper);
                            Assert.isNotNull(purchaseOrder,PmsErrorCodeEnum.SC_PMS_004_P004);
                            List<PmsPurchaseBillDetailPO> pmsPurchaseBillDetails = pmsPurchaseDetailRepositoryService.selectDetailList(req.getPurchBillNo(),  purchaseOrder.getCreateTime());
                            Assert.isTrue(!CollectionUtils.isEmpty(pmsPurchaseBillDetails),PmsErrorCodeEnum.SC_PMS_004_P020);
                            Map<String, PmsPurchaseBillDetailPO> detailMap = pmsPurchaseBillDetails.stream()
                                    .filter(f->f.getSkuType()==0)
                                    .collect(Collectors.toMap(PmsPurchaseBillDetailPO::getSkuCode, Function.identity()));
                            List<AcceptPriceResp> specialOrContractPrice = getSpecialOrContractPrice(req.getDeptCode(), goodsCodes, billDetailMap, skuContract);
                            Map<String, BigDecimal> specialPriceMap = specialOrContractPrice.stream().collect(Collectors.toMap(AcceptPriceResp::getSkuCode, AcceptPriceResp::getSinglePrice));
                            for (String goodsCode : goodsCodes) {
                                BigDecimal singlePrice = detailMap.get(goodsCode).getPurchPrice();
                                if (specialPriceMap.containsKey(goodsCode)) {
                                    BigDecimal price = specialPriceMap.get(goodsCode);
                                    singlePrice = singlePrice.compareTo(price) > 0 ? price : singlePrice;
                                }
                                AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                                acceptPriceResp.setSinglePrice(singlePrice);
                                acceptPriceResp.setSkuCode(goodsCode);
                                acceptPriceResps.add(acceptPriceResp);
                            }
                        } else {
                            acceptPriceResps.addAll(getSpecialOrContractPrice(req.getDeptCode(), goodsCodes, billDetailMap, skuContract));
                        }
                        break;
                }
            }

        } else {
            if (PmsBillDirectionEnum.REVERSE.getCode().equals(req.getBillDirection())) {
                String supportSupplier = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(PMSSystemParamEnum.REFUND_ENABLE_SUPPORT_SUPPLIER, req.getDeptCode());
                if (supportSupplier.equals("0")) {
                    String refundAcceptPriceType = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(PMSSystemParamEnum.REFUND_ACCEPT_PRICE_TYPE, req.getDeptCode());
                    switch (refundAcceptPriceType) {
                        case "1": //供应商进价
                            //todo 供应商商品进价优先取
                        case "2": //最后进价
                            for (QueryAcceptPriceDTO.GoodsInfo billDetail : req.getGoodsInfos()) {
                                AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                                acceptPriceResp.setSkuCode(billDetail.getSkuCode());
                                acceptPriceResp.setSinglePrice(billDetail.getPrice());
                                acceptPriceResps.add(acceptPriceResp);
                            }
                            break;
                        case "0": //申请单价
                            if (StringUtils.isNotEmpty(req.getPurchBillNo())) {
                                QueryWrapper<PmsPurchaseOrderPO> applyBillQueryWrapper = new QueryWrapper<>();
                                applyBillQueryWrapper.eq("bill_no", req.getPurchBillNo());
                                PmsPurchaseOrderPO purchaseOrder = pmsPurchaseOrderRepositoryService.getBaseMapper().selectOne(applyBillQueryWrapper);
                                Assert.isNotNull(purchaseOrder,PmsErrorCodeEnum.SC_PMS_004_P004);
                                List<PmsPurchaseBillDetailPO> pmsPurchaseBillDetails = pmsPurchaseDetailRepositoryService.selectDetailList(req.getPurchBillNo(),  purchaseOrder.getCreateTime());
                                Assert.isTrue(!CollectionUtils.isEmpty(pmsPurchaseBillDetails),PmsErrorCodeEnum.SC_PMS_004_P020);
                                Map<String, PmsPurchaseBillDetailPO> detailMap = pmsPurchaseBillDetails.stream()
                                        .filter(f->f.getSkuType()==0)
                                        .collect(Collectors.toMap(PmsPurchaseBillDetailPO::getSkuCode, Function.identity()));
                                for (String goodsCode : goodsCodes) {
                                    BigDecimal singlePrice = detailMap.get(goodsCode).getPurchPrice();
                                    AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                                    acceptPriceResp.setSinglePrice(singlePrice);
                                    acceptPriceResp.setSkuCode(goodsCode);
                                    acceptPriceResps.add(acceptPriceResp);
                                }
                            } else {
                                for (QueryAcceptPriceDTO.GoodsInfo billDetail : req.getGoodsInfos()) {
                                    AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                                    acceptPriceResp.setSkuCode(billDetail.getSkuCode());
                                    acceptPriceResp.setSinglePrice(billDetail.getPrice());
                                    acceptPriceResps.add(acceptPriceResp);
                                }
                            }
                            break;
                        case "3": //批次价
                            for (QueryAcceptPriceDTO.GoodsInfo billDetail : req.getGoodsInfos()) {
                                AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                                acceptPriceResp.setSkuCode(billDetail.getSkuCode());
                                if (CollectionUtils.isNotEmpty(billDetail.getBatchInfos())) {
                                    //批次价取平均值
                                    Double batchPrice = billDetail.getBatchInfos().stream()
                                            .map(QueryAcceptPriceDTO.BatchInfo::getBatchCostPrice)
                                            .collect(Collectors.averagingDouble(BigDecimal::doubleValue));
                                    acceptPriceResp.setSinglePrice(BigDecimal.valueOf(batchPrice));
                                } else {
                                    acceptPriceResp.setSinglePrice(billDetail.getPrice());
                                }
                                acceptPriceResps.add(acceptPriceResp);
                            }
                            break;
                    }
                }
                //todo 非启用合同场景供&供应商场景
            } else if (PmsBillDirectionEnum.NORMAL.getCode().equals(req.getBillDirection())) {
                String supportSupplier = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(PMSSystemParamEnum.REFUND_ENABLE_SUPPORT_SUPPLIER, req.getDeptCode());
                if (supportSupplier.equals("0")) {
                    String acceptPriceType = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(PMSSystemParamEnum.ACCEPT_PRICE_TYPE, req.getDeptCode());
                    switch (acceptPriceType) {
                        case "0":
                            if (StringUtils.isNotEmpty(req.getPurchBillNo())) {
                                QueryWrapper<PmsPurchaseOrderPO> applyBillQueryWrapper = new QueryWrapper<>();
                                applyBillQueryWrapper.eq("bill_no", req.getPurchBillNo());
                                PmsPurchaseOrderPO purchaseOrder = pmsPurchaseOrderRepositoryService.getBaseMapper().selectOne(applyBillQueryWrapper);
                                Assert.isNotNull(purchaseOrder,PmsErrorCodeEnum.SC_PMS_004_P004);
                                List<PmsPurchaseBillDetailPO> pmsPurchaseBillDetails = pmsPurchaseDetailRepositoryService.selectDetailList(req.getPurchBillNo(),  purchaseOrder.getCreateTime());
                                Assert.isTrue(!CollectionUtils.isEmpty(pmsPurchaseBillDetails),PmsErrorCodeEnum.SC_PMS_004_P020);
                                Map<String, PmsPurchaseBillDetailPO> detailMap = pmsPurchaseBillDetails.stream()
                                        .filter(f->f.getSkuType()==0)
                                        .collect(Collectors.toMap(PmsPurchaseBillDetailPO::getSkuCode, Function.identity()));

                                for (QueryAcceptPriceDTO.GoodsInfo acceptBillDetail : req.getGoodsInfos()) {
                                    AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                                    acceptPriceResp.setSinglePrice(detailMap.get(acceptBillDetail.getSkuCode()).getPurchPrice());
                                    acceptPriceResp.setSkuCode(acceptBillDetail.getSkuCode());
                                    acceptPriceResps.add(acceptPriceResp);
                                }
                            } else {
                                for (QueryAcceptPriceDTO.GoodsInfo billDetail : req.getGoodsInfos()) {
                                    AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                                    acceptPriceResp.setSkuCode(billDetail.getSkuCode());
                                    acceptPriceResp.setSinglePrice(billDetail.getPrice());
                                    acceptPriceResps.add(acceptPriceResp);
                                }
                            }
                            break;
                        case "1":
                            for (QueryAcceptPriceDTO.GoodsInfo billDetail : req.getGoodsInfos()) {
                                AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                                acceptPriceResp.setSkuCode(billDetail.getSkuCode());
                                acceptPriceResp.setSinglePrice(billDetail.getPrice());
                                acceptPriceResps.add(acceptPriceResp);
                            }
                            break;
                        case "2":
                            if (StringUtils.isNotEmpty(req.getPurchBillNo())) {
                                QueryWrapper<PmsPurchaseOrderPO> applyBillQueryWrapper = new QueryWrapper<>();
                                applyBillQueryWrapper.eq("bill_no", req.getPurchBillNo());
                                PmsPurchaseOrderPO purchaseOrder = pmsPurchaseOrderRepositoryService.getBaseMapper().selectOne(applyBillQueryWrapper);
                                Assert.isNotNull(purchaseOrder,PmsErrorCodeEnum.SC_PMS_004_P004);
                                List<PmsPurchaseBillDetailPO> pmsPurchaseBillDetails = pmsPurchaseDetailRepositoryService.selectDetailList(req.getPurchBillNo(),  purchaseOrder.getCreateTime());
                                Assert.isTrue(!CollectionUtils.isEmpty(pmsPurchaseBillDetails),PmsErrorCodeEnum.SC_PMS_004_P020);
                                Map<String, PmsPurchaseBillDetailPO> detailMap = pmsPurchaseBillDetails.stream()
                                        .filter(f->f.getSkuType()==0)
                                        .collect(Collectors.toMap(PmsPurchaseBillDetailPO::getSkuCode, Function.identity()));
                                for (String goodsCode : goodsCodes) {
                                    BigDecimal singlePrice = detailMap.get(goodsCode).getPurchPrice();
                                    BigDecimal price = billDetailMap.get(goodsCode).getPrice();
                                    AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                                    acceptPriceResp.setSinglePrice(singlePrice.compareTo(price) > 0 ? price : singlePrice);
                                    acceptPriceResp.setSkuCode(goodsCode);
                                    acceptPriceResps.add(acceptPriceResp);
                                }
                            } else {
                                for (QueryAcceptPriceDTO.GoodsInfo billDetail : req.getGoodsInfos()) {
                                    AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                                    acceptPriceResp.setSkuCode(billDetail.getSkuCode());
                                    acceptPriceResp.setSinglePrice(billDetail.getPrice());
                                    acceptPriceResps.add(acceptPriceResp);
                                }
                            }
                            break;
                    }
                }
            }
        }
        return Results.ofSuccess(acceptPriceResps);
    }

    /**
     * 特供价 >合同商品进价
     *
     * @param deptCode
     * @param goodsCodes
     * @return
     */
    private List<AcceptPriceResp> getSpecialOrContractPrice(String deptCode, List<String> goodsCodes, Map<String, QueryAcceptPriceDTO.GoodsInfo> goodsInfoMap, List<String> skuContract) {
        ContractGoodsDeptQueryDTO contractGoodsDeptQuery = new ContractGoodsDeptQueryDTO();
        contractGoodsDeptQuery.setDeptCode(deptCode);
        contractGoodsDeptQuery.setSkuCodeList(goodsCodes);
        contractGoodsDeptQuery.setApplyCate(ApplyCateEnum.PURCH.getCode());
        List<ContractGoodsDeptDTO> contractGoodsDeptList = supplychainControlEngineService.getSupplychainBizGoodsRuleService().listContractGoodsDept(contractGoodsDeptQuery);
        if(CollectionUtils.isNotEmpty(skuContract)){
            contractGoodsDeptList = contractGoodsDeptList.stream().filter(item->skuContract.contains(item.getSkuCode()+"_"+item.getContractNo())).collect(Collectors.toList());
        }
        Map<String, ContractGoodsDeptDTO> contractGoodsMap = contractGoodsDeptList.stream().collect(Collectors.toMap(ContractGoodsDeptDTO::getSkuCode, Function.identity()));

        List<AcceptPriceResp> acceptPriceResps = new ArrayList<>();
        LocalDateTime date = LocalDateTime.now();
        Map<String, QueryAcceptanceRefundResp.CompensationInfo> compensationInfoMap = new HashMap<>();
        try {
            List<QueryAcceptanceRefundReq.GoodsPriceInfo> skuList = goodsCodes.stream().map(e ->
                            QueryAcceptanceRefundReq.GoodsPriceInfo.builder().skuCode(e).build())
                    .collect(Collectors.toList());
            QueryAcceptanceRefundReq req = QueryAcceptanceRefundReq.builder()
                    .storeCode(deptCode)
                    .skuList(skuList)
                    .build();
            QueryAcceptanceRefundResp queryAcceptanceRefundResp = promotionFeignClient.queryAcceptanceRefund(req);
            List<QueryAcceptanceRefundResp.CompensationInfo> compensationInfoList = new ArrayList<>();
            if (queryAcceptanceRefundResp != null && CollectionUtils.isNotEmpty(queryAcceptanceRefundResp.getCompensationInfoList())) {
                compensationInfoList.addAll(queryAcceptanceRefundResp.getCompensationInfoList());
            }
            compensationInfoMap = compensationInfoList.stream().filter(e -> e.getCompensateValue() != null && e.getCompensateValue() > 0)
                    .collect(Collectors.toMap(QueryAcceptanceRefundResp.CompensationInfo::getSkuCode, Function.identity()));
        } catch (Exception e) {
            Logs.info("查询促销特供价失败", e);
        }
        for (String goodsCode : goodsCodes) {
            if (contractGoodsMap.containsKey(goodsCode)) {
                BigDecimal specialPrice = null;
                ContractGoodsDeptDTO contractGoodsDeptDTO = contractGoodsMap.get(goodsCode);
                if (compensationInfoMap.containsKey(goodsCode)) {
                    QueryAcceptanceRefundResp.CompensationInfo compensationInfo = compensationInfoMap.get(goodsCode);
                    if (compensationInfo != null && (StringUtils.isEmpty(compensationInfo.getSupplierCode()) || contractGoodsDeptDTO.getSupplierCode().equals(compensationInfo.getSupplierCode()))) {
                        specialPrice = (compensationInfo.getCompensateValue() == null || compensationInfo.getCompensateValue() == 0L) ? null : BigDecimal.valueOf(compensationInfo.getCompensateValue()).multiply(BigDecimal.valueOf(100));
                    }
                }
                if (specialPrice == null) {
                    specialPrice = getSpecialPrice(contractGoodsDeptDTO, goodsInfoMap.get(goodsCode).getReferPrice(), date);
                }
                BigDecimal price = specialPrice.compareTo(BigDecimal.ZERO) == 0 ? contractGoodsDeptDTO.getPurchPrice() : specialPrice;
                //经营方式,英文逗号分隔 J经销 D代销 L联营 Z租赁
                if(contractGoodsDeptDTO.getOperateMode().equals("L")){
                    //联营管库存商品价格=0
                    price = BigDecimal.ZERO;
                }
                AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                acceptPriceResp.setSinglePrice(price);
                acceptPriceResp.setSkuCode(goodsCode);
                acceptPriceResps.add(acceptPriceResp);
            }
        }
        return acceptPriceResps;
    }


    private BigDecimal getSpecialPrice(ContractGoodsDeptDTO contractGoodsDeptDTO, BigDecimal referPrice, LocalDateTime date) {
        BigDecimal specialPrice = BigDecimal.ZERO;
        BigDecimal purchPrice = contractGoodsDeptDTO.getPurchPrice();
        if (contractGoodsDeptDTO.getSpecialStartTime() != null && contractGoodsDeptDTO.getSpecialEndTime() != null) {
            String beg = contractGoodsDeptDTO.getSpecialStartTime().with(LocalTime.MIN).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String end = contractGoodsDeptDTO.getSpecialEndTime().with(LocalTime.MAX).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime localDateTime = DateUtil.date2LocalDateTime(beg);
            LocalDateTime localDateTime2 = DateUtil.date2LocalDateTime(end);
            if (!(date.isAfter(localDateTime) && date.isBefore(localDateTime2))) {
                return contractGoodsDeptDTO.getPurchPrice();
            }
        }

        switch (SpecialPriceModeEnum.getEnumByCode(contractGoodsDeptDTO.getSpecialPriceMode())) {
            case NONE:
                specialPrice = contractGoodsDeptDTO.getPurchPrice();
                break;
            case FIXED_PRICE:
                specialPrice = contractGoodsDeptDTO.getSpecialTaxPrice();
                break;
            case ADD_PRICE:
                specialPrice = contractGoodsDeptDTO.getPurchPrice().multiply(BigDecimal.ONE.add(contractGoodsDeptDTO.getSpecialTaxPrice().divide(BigDecimal.valueOf(100))));
                break;
            case REDUCE_PRICE:
                specialPrice = referPrice.multiply(BigDecimal.ONE.subtract(contractGoodsDeptDTO.getSpecialTaxPrice().divide(BigDecimal.valueOf(100))));
                break;
        }
        return specialPrice;
    }

    private List<AcceptPriceResp> getContractPrice(String deptCode, Map<String, QueryAcceptPriceDTO.GoodsInfo> billDetailMap, List<String> skuContract) {
        Map<String, QueryAcceptanceRefundResp.CompensationInfo> compensationInfoMap = new HashMap<>();
        try {
            List<QueryAcceptanceRefundReq.GoodsPriceInfo> skuList = billDetailMap.keySet().stream().map(e ->
                            QueryAcceptanceRefundReq.GoodsPriceInfo.builder().skuCode(e).build())
                    .collect(Collectors.toList());
            QueryAcceptanceRefundReq req = QueryAcceptanceRefundReq.builder()
                    .storeCode(deptCode)
                    .skuList(skuList)
                    .build();
            QueryAcceptanceRefundResp queryAcceptanceRefundResp = promotionFeignClient.queryAcceptanceRefund(req);
            List<QueryAcceptanceRefundResp.CompensationInfo> compensationInfoList = new ArrayList<>();
            if (queryAcceptanceRefundResp != null && CollectionUtils.isNotEmpty(queryAcceptanceRefundResp.getCompensationInfoList())) {
                compensationInfoList.addAll(queryAcceptanceRefundResp.getCompensationInfoList());
            }
            compensationInfoMap = compensationInfoList.stream().filter(e -> e.getCompensateValue() != null && e.getCompensateValue() > 0)
                    .collect(Collectors.toMap(QueryAcceptanceRefundResp.CompensationInfo::getSkuCode, Function.identity()));
        } catch (Exception e) {
            Logs.info("查询促销特供价失败", e);
        }
        ContractGoodsDeptQueryDTO contractGoodsDeptQuery = ContractGoodsDeptQueryDTO.builder()
                .deptCode(deptCode)
                .skuCodeList(skuContract).build();
        List<ContractGoodsDeptDTO> contractGoodsDeptList = supplychainControlEngineService.getSupplychainBizGoodsRuleService().listContractGoodsDept(contractGoodsDeptQuery);
        Map<String, ContractGoodsDeptDTO> contractGoodsMap = contractGoodsDeptList.stream().collect(Collectors.toMap(ContractGoodsDeptDTO::getSkuCode, Function.identity()));


        List<AcceptPriceResp> acceptPriceResps = new ArrayList<>();
        for (String goodsCode : billDetailMap.keySet()) {
            //优先取促销特供价
            if (compensationInfoMap.containsKey(goodsCode)) {
                QueryAcceptanceRefundResp.CompensationInfo compensationInfo = compensationInfoMap.get(goodsCode);
                Long singlePrice = null;
                if (compensationInfo != null && (StringUtils.isEmpty(compensationInfo.getSupplierCode()) || contractGoodsMap.get(goodsCode).getSupplierCode().equals(compensationInfo.getSupplierCode()))) {
                    singlePrice = (compensationInfo.getCompensateValue() == null || compensationInfo.getCompensateValue() == 0L) ? null : compensationInfo.getCompensateValue() * 100;
                }
                if (singlePrice != null) {
                    AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                    acceptPriceResp.setSinglePrice(BigDecimal.valueOf(singlePrice));
                    acceptPriceResp.setSkuCode(goodsCode);
                    acceptPriceResps.add(acceptPriceResp);
                    continue;
                }
            }
            if (contractGoodsMap.containsKey(goodsCode)) {
                ContractGoodsDeptDTO contractGoodsDeptDTO = contractGoodsMap.get(goodsCode);
                AcceptPriceResp acceptPriceResp = new AcceptPriceResp();
                acceptPriceResp.setSinglePrice(contractGoodsDeptDTO.getPurchPrice());
                acceptPriceResp.setSkuCode(goodsCode);
                acceptPriceResps.add(acceptPriceResp);
            }
        }
        return acceptPriceResps;
    }

    /**
     * 审核验收单
     *
     * @param auditAcceptBillDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<ApplyBillResp> auditAcceptBill(AuditAcceptBillDTO auditAcceptBillDTO) {

        //操作人信息
        OpInfo operatorInfo = userUtil.getOpInfoWithThrow();
        List<String> redisWords = Arrays.asList(TenantContext.get(), "auditAcceptBill", operatorInfo.getUserId());
        String redisKey = String.join(SysConstants.UNDERLINE_DELIMITER, redisWords);
        String redisNoKey = "";
        if (!redisUtil.tryLock(redisKey, operatorInfo.getUserId(), EXPIRE_TIME)){
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_D001);
        }
        try {
            // 校验验收单状态是否未审核
            LambdaQueryWrapper<PmsAcceptBillPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(PmsAcceptBillPO::getBillNo, auditAcceptBillDTO.getBillNoList());
            List<PmsAcceptBillPO> acceptBills = pmsAcceptBillRepositoryService.getBaseMapper().selectList(wrapper);
            //校验当前申请单状态是否待审核
            List<PmsAcceptBillPO> canAcceptBills = acceptBills.stream().filter(e ->
                    AcceptStateEnum.PENDING_AUDIT.getBillStateCode().equals(e.getStatus())
                            || AcceptStateEnum.DRAFT.getBillStateCode().equals(e.getStatus())
            ).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(canAcceptBills)) {
                BizExceptions.throwWithMsg("当前无有效待审核单据");
            }

            for (PmsAcceptBillPO acceptBill : canAcceptBills) {
                final String amendAcceptBillNo = acceptBill.getAmendAcceptBillNo();
                // 如果是修正单 需要冲红原验收单
                if (StringUtils.isNotBlank(amendAcceptBillNo)) {
                    try {
                        pmsAcceptServiceHelper.cancelOldAcceptBill(acceptBill);
                    } catch (Exception e) {
    //                sysAlarmRobot.asyncSysAlarm(String.format("验收单号 %s 修正时 冲红原单失败 被修正单 %s", stAcceptBill.getAcceptBillNumber(), amendAcceptBillNumber));
                    }
                }
            }
            List<String> acceptNumbers = acceptBills.stream().map(PmsAcceptBillPO::getBillNo).collect(Collectors.toList());

            List<PmsAcceptBillDetailPO> stAcceptBillDetails = pmsAcceptBillDetailRepositoryService.selectDetailListBatchByBillNoList(acceptNumbers);
            Map<String, List<PmsAcceptBillDetailPO>> billDetailMap = stAcceptBillDetails.stream().collect(Collectors.groupingBy(PmsAcceptBillDetailPO::getBillNo));

            //当前验收单对应的申请单号
            List<PmsAcceptBillPO> acceptBillUpdate = new ArrayList<>();
            for (PmsAcceptBillPO acceptBillPO : canAcceptBills) {
                //进一步枷锁，锁住可以审核的验收单
                List<String> redisNoWords = Arrays.asList(TenantContext.get(), "auditAcceptBill","billNo", acceptBillPO.getBillNo());
                 redisNoKey = String.join(SysConstants.UNDERLINE_DELIMITER, redisNoWords);
                if (!redisUtil.tryLock(redisNoKey, acceptBillPO.getBillNo(), EXPIRE_TIME)){
                    BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_D001);
                }
                PmsAcceptBillPO stApplyBillUpdate = new PmsAcceptBillPO();
                stApplyBillUpdate.setId(acceptBillPO.getId());
                stApplyBillUpdate.setStatus(AcceptStateEnum.AUDIT.getBillStateCode());
                stApplyBillUpdate.setConfirmManCode(operatorInfo.getOperatorCode());
                stApplyBillUpdate.setConfirmManName(operatorInfo.getOperatorName());
                stApplyBillUpdate.setAuditManName(operatorInfo.getOperatorName());
                stApplyBillUpdate.setAuditManCode(operatorInfo.getOperatorCode());
                stApplyBillUpdate.setAuditTime(LocalDateTime.now());
                stApplyBillUpdate.setConfirmTime(LocalDateTime.now());
                if (AcceptStateEnum.DRAFT.getBillStateCode().equals(acceptBillPO.getStatus())) {
                    stApplyBillUpdate.setSubmitTime(LocalDateTime.now());
                    stApplyBillUpdate.setSubmitManCode(operatorInfo.getOperatorCode());
                    stApplyBillUpdate.setSubmitManName(operatorInfo.getOperatorName());
                }
                acceptBillUpdate.add(stApplyBillUpdate);
            }
            pmsAcceptBillRepositoryService.updateBatchById(acceptBillUpdate);
            List<PmsAcceptBillPO> orderAcceptBillList = canAcceptBills.stream().filter(e -> StringUtils.isNotBlank(e.getPurchBillNo())).collect(Collectors.toList());
            Set<String> billNumbers = orderAcceptBillList.stream().map(PmsAcceptBillPO::getPurchBillNo).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            List<PmsPurchaseOrderPO> purchaseOrders = new ArrayList<>();
            //所有验收单对应的申请单
            List<PmsPurchaseBillDetailPO> pmsPurchaseBillDetails = new ArrayList<>();
            if (!billNumbers.isEmpty()) {
                LambdaQueryWrapper<PmsPurchaseOrderPO> query = new LambdaQueryWrapper<>();
                query.in(PmsPurchaseOrderPO::getBillNo, billNumbers);
                purchaseOrders = pmsPurchaseOrderRepositoryService.getBaseMapper().selectList(query);
                pmsPurchaseBillDetails = pmsPurchaseDetailRepositoryService.selectDetailListBatch(Lists.newArrayList(billNumbers), purchaseOrders);
            }
            Map<String, PmsPurchaseOrderPO> applyBillMap = purchaseOrders.stream().collect(Collectors.toMap(PmsPurchaseOrderPO::getBillNo, Function.identity()));
            //本次审核的验收单对应的申请单
            Map<String, List<PmsPurchaseBillDetailPO>> applyDetailMap = pmsPurchaseBillDetails.stream().collect(Collectors.groupingBy(PmsPurchaseBillDetailPO::getBillNo));
            //记录申请单的最后验收单号 申请单号-验收单号
            Map<String, String> accept2ApplyMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(orderAcceptBillList)) {
                LambdaQueryWrapper<PmsAcceptBillPO> stateWrapper = new LambdaQueryWrapper<>();
                stateWrapper.eq(PmsAcceptBillPO::getStatus, AcceptStateEnum.AUDIT.getBillStateCode());
                stateWrapper.in(PmsAcceptBillPO::getPurchBillNo, Lists.newArrayList(billNumbers));
                //查出当前对应申请单的已审核的验收单
                List<PmsAcceptBillPO> pmsAcceptBillList = pmsAcceptBillRepositoryService.getBaseMapper().selectList(stateWrapper);
                Map<String, List<PmsAcceptBillPO>> order2AcceptBillMap = pmsAcceptBillList.stream().collect(Collectors.groupingBy(PmsAcceptBillPO::getBillNo));

                List<PmsPurchaseOrderPO> updateBillList = new ArrayList<>();
                for (PmsAcceptBillPO acceptBill : orderAcceptBillList) {
                    boolean isPerform = true;
                    PmsPurchaseOrderPO orderPO = applyBillMap.get(acceptBill.getPurchBillNo());
                    PmsPurchaseOrderPO applyBill4Update = new PmsPurchaseOrderPO();
                    applyBill4Update.setId(orderPO.getId());
                    applyBill4Update.setBillType(orderPO.getBillType());
                    applyBill4Update.setAuditTime(LocalDateTime.now());
                    applyBill4Update.setAuditCode(operatorInfo.getOperatorCode());
                    applyBill4Update.setAuditName(operatorInfo.getOperatorName());
                    accept2ApplyMap.put(orderPO.getBillNo(), acceptBill.getPurchBillNo());

                    List<PmsPurchaseBillDetailPO> billDetailList = applyDetailMap.get(acceptBill.getPurchBillNo());
                    //查询当前申请单对应的验收单所有明细商品
                    List<PmsAcceptBillPO> acceptBillList = order2AcceptBillMap.get(acceptBill.getBillNo());
                    List<String> billNoList = acceptBillList.stream().map(PmsAcceptBillPO::getBillNo).collect(Collectors.toList());
                    List<PmsAcceptBillDetailPO> acceptBillDetails = pmsAcceptBillDetailRepositoryService.selectDetailListBatchByBillNoList(billNoList);
                    Map<String, List<PmsAcceptBillDetailPO>>  acceptMap = acceptBillDetails.stream().collect(Collectors.groupingBy(PmsAcceptBillDetailPO::getSkuCode));
                    List<PmsPurchaseBillDetailPO> curBillDetails = new ArrayList<>();
                    //获取当前采购订单已经履行数量
                    BigDecimal fulfilTotalQty = pmsPurchaseBillDetails.stream().map(PmsPurchaseBillDetailPO::getFulfilQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    for (PmsPurchaseBillDetailPO stApplyBillDetail : billDetailList) {
                        List<PmsAcceptBillDetailPO> acceptBillDetailList = acceptMap.get(stApplyBillDetail.getSkuCode());
                        if(CollectionUtils.isNotEmpty(acceptBillDetailList)){
                            acceptBillDetailList = acceptBillDetailList.stream()
                                    .filter(acceptBillDetail -> acceptBillDetail.getSkuType()==stApplyBillDetail.getSkuType())
                                    .collect(Collectors.toList());
                            BigDecimal fulfilQty = CollectionUtils.isEmpty(acceptBillDetailList) ? BigDecimal.ZERO :
                                    stApplyBillDetail.getFulfilQty().add(acceptBillDetailList.stream().map(PmsAcceptBillDetailPO::getAcceptQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                            BigDecimal fulfilMoney = CollectionUtils.isEmpty(acceptBillDetailList) ? BigDecimal.ZERO :
                                    stApplyBillDetail.getFulfilMoney().add(acceptBillDetailList.stream().map(PmsAcceptBillDetailPO::getPurchMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
                            BigDecimal fulfilRate = fulfilQty.divide(stApplyBillDetail.getPurchQty(),2,  RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                            fulfilTotalQty = fulfilTotalQty.add(fulfilQty);
                            //已采购总数量小于订货数量
                            if (fulfilTotalQty.compareTo(orderPO.getTotalQty()) < 0) {
                                isPerform = false;
                            }
                            if (CollectionUtils.isNotEmpty(acceptBillDetailList)) {
                                PmsPurchaseBillDetailPO applyBillDetail = PmsPurchaseBillDetailPO.builder()
                                        .id(stApplyBillDetail.getId())
                                        .fulfilQty(fulfilQty)
                                        .fulfilMoney(fulfilMoney)
                                        .fulfilRate(fulfilRate)
                                        .fulfilTime(LocalDateTime.now())
                                        .build();
                                curBillDetails.add(applyBillDetail);
                            }
                        }
                    }

                    if (!PmsBillSourceEnum.WHOLESALE.getBillSource().equals(auditAcceptBillDTO.getBillSource())) {
                        if (PmsBillDirectionEnum.REVERSE.getCode().equals(acceptBill.getBillDirection())) {
                            String applyRefundEndingType = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(PMSSystemParamEnum.APPLY_REFUND_ENDING_TYPE, acceptBill.getDeptCode());
                            if (applyRefundEndingType.equals("0")) {
                                isPerform = true;
                            }
                        } else {
                            String applyEndingType = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(PMSSystemParamEnum.APPLY_ENDING_TYPE, acceptBill.getDeptCode());
                            if (StringUtils.isNotEmpty(applyEndingType) && applyEndingType.equals("0")) {
                                isPerform = true;
                            }
                        }
                    } else {
                        isPerform = true;
                    }
                    //采购订单验收时，部分履行更新采购订单状态为收货中，采退订单不处理中间状态
                    if(PmsBillDirectionEnum.NORMAL.getCode().equals(orderPO.getBillDirection())){
                        applyBill4Update.setStatus(PmsPurchaseBillStatusEnum.PURCH_STATUS_PART_ACCEPT.getCode());
                    }
                    if (isPerform) {
                        applyBill4Update.setStatus(PmsPurchaseBillStatusEnum.PURCH_STATUS_FINISHED.getCode());
                        orderPO.setStatus(PmsPurchaseBillStatusEnum.PURCH_STATUS_FINISHED.getCode());
                    }
                    updateBillList.add(applyBill4Update);
                    pmsPurchaseDetailRepositoryService.updateBatch4SpecialTable(curBillDetails);
                }
                pmsPurchaseOrderRepositoryService.updateBatchById(updateBillList);
            }

            for (PmsAcceptBillPO stAcceptBill : canAcceptBills) {
                // 是否需要改价
                String applyEndingType = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(PMSSystemParamEnum.ENABLE_MODIFY_ACCEPT_LAST_PRICE, stAcceptBill.getDeptCode());
                if (YesOrNoEnum.YES.getCode().toString().equals(applyEndingType)) {
                    List<UpdatePurchPriceReq.GoodsPriceInfo> goodsInfos = stAcceptBillDetails.stream()
                            .map(item -> UpdatePurchPriceReq.GoodsPriceInfo.builder()
                            .purchPrice(item.getPurchPrice().multiply(BigDecimal.valueOf(10000)).longValue())
                            .skuCode(item.getSkuCode())
                            .build()).collect(Collectors.toList());
                    UpdatePurchPriceReq build = UpdatePurchPriceReq.builder()
                            .source("supplychain-pms")
                            .serialNo(stAcceptBill.getPurchBillNo())
                            .storeCode(stAcceptBill.getDeptCode())
                            .goodsList(goodsInfos)
                            .build();
                    commonGoodsService.updatePurchPrice(build);
                }
            }

            for (PmsAcceptBillPO stAcceptBill : canAcceptBills) {
                uploadStockBatch(stAcceptBill, billDetailMap, applyDetailMap);
            }
        } catch (BizException e) {
            BizExceptions.throwWithErrorCode(e.getError());
        } finally {
            redisUtil.unlock(redisKey);
            redisUtil.unlock(redisNoKey);
        }
        return null;
    }

    /**
     * 批次上报 库存上报
     *
     * @param stAcceptBill       验收单
     * @param billDetailMap      验收单明细
     * @param purchBillDetailMap 采购订单明细
     */
    private void uploadStockBatch(PmsAcceptBillPO stAcceptBill,
                                  Map<String, List<PmsAcceptBillDetailPO>> billDetailMap,
                                  Map<String, List<PmsPurchaseBillDetailPO>> purchBillDetailMap) {
        // 调整加盟额度
        try {
            adjustJiaMeng(stAcceptBill,billDetailMap.get(stAcceptBill.getBillNo()),0L);
        } catch (Exception e) {
            Logs.error("调整加盟额度异常:{}", e.getMessage(),e);
            BizExceptions.throwWithMsg(String.format("调整加盟额度异常:%s",e.getMessage()));
        }
        //调用代配过账
        try {
            proxyPost(stAcceptBill,billDetailMap.get(stAcceptBill.getBillNo()));
        } catch (Exception e) {
            adjustJiaMeng(stAcceptBill,billDetailMap.get(stAcceptBill.getBillNo()),1L);
            Logs.error("调用代配过账异常:{}", e.getMessage(),e);
            BizExceptions.throwWithMsg(String.format("调用代配过账异常:%s",e.getMessage()));
        }
        // 获取终止类型  0，保存后终止；1，履行后终止；
        String endingType = determineEndingType(stAcceptBill);
        // 构建库存释放列表
        List<StkTaskIReleaseExecuteDto> releaseSkuList = buildReleaseSkuList(stAcceptBill, billDetailMap, endingType, purchBillDetailMap);
        // 构建库存操作项
        List<StkTaskItemExecuteDto> skuList = buildStkTaskItems(stAcceptBill, billDetailMap);
        // 创建批量记录请求
        BatchRecordReq batchRecord = createBatchRecordRequest(stAcceptBill, skuList, releaseSkuList);
        // 日志记录
        Logs.info("库存上报参数：{}", JSON.toJSONString(batchRecord));
        // 调用库存服务
        try {
            BatchRecordResp resp = iCommonStockService.costStockExecute(batchRecord);
            Logs.info("库存上报结果：{}", JSON.toJSONString(resp));
            //日结
            dayAccount(stAcceptBill, resp);
            //批次回写
            batchCallback(resp, stAcceptBill, billDetailMap.get(stAcceptBill.getBillNo()));
            // 更新验收单并异步生成转码单
            updateBillAndGenerateTsc(stAcceptBill, batchRecord.getBillNo());
        } catch (Exception e) {
            adjustJiaMeng(stAcceptBill,billDetailMap.get(stAcceptBill.getBillNo()),1L);
            Logs.error("库存上报失败:{}", e.getMessage(),e);
            BizExceptions.throwWithMsg(e.getMessage());
        }
    }

    /**
     * 批次回写
     * @param resp 批次回写结果
     * @param acceptBill 验收订单
     * @param applyBill 采购订单
     * @param acceptBillDetails 验收订单明细
     * @param isCancelBill   是否冲红单
     * @return
     */
    private void batchCallback(BatchRecordResp resp,PmsAcceptBillPO acceptBill, List<PmsAcceptBillDetailPO> acceptBillDetails){
        if (Objects.isNull(resp) || CollectionUtils.isEmpty(resp.getSkuList())) {
            BizExceptions.throwWithMsg("批次接口返回异常");
        }
        // 批次信息按照商品行进行分组
        Map<Long, BatchExecRowVo> execRowVoMap = resp.getSkuList().stream().collect(Collectors.toMap(BatchExecRowVo::getInsideId, Function.identity()));
        //验收明细按照商品行进行分组
        Map<Long, PmsAcceptBillDetailPO> acceptBillDetailMap = acceptBillDetails.stream().collect(Collectors.toMap(PmsAcceptBillDetailPO::getInsideId, Function.identity()));
        List<BatchUpdateSummary> summaryList = new ArrayList<>();
        Map<Long, List<BatchExecRowVo>> listMap = resp.getSkuList().stream().collect(Collectors.groupingBy(BatchExecRowVo::getInsideId));
        listMap.forEach((insideId, execRowVo) -> {
            PmsAcceptBillDetailPO acceptBillDetail = acceptBillDetailMap.get(insideId);
            if(Objects.isNull(acceptBillDetail)){
                return;
            }
            List<BatchExecRowVo> execRowVos = execRowVo.stream().collect(Collectors.toList());
            BatchExecRowVo batchExecRowVo = execRowVos.get(0);
            BatchUpdateSummary batchUpdateSummary = BatchUpdateSummary.builder()
                    .costPrice(batchExecRowVo.getCostPrice())
                    .totalMoney(batchExecRowVo.getCostTaxMoney())
                    .totalTax(batchExecRowVo.getCostTax())
                    .build();
            batchUpdateSummary.setInsideId(insideId);
            batchUpdateSummary.setBatchInfo(BatchUpdateSummary.BatchInfo.builder()
                    .batchQty(batchExecRowVo.getRealQty())
                    .oldPrice(acceptBillDetail.getPurchPrice())
                    .costPrice(batchExecRowVo.getCostPrice())
                    .taxCostMoney(batchExecRowVo.getCostTaxMoney())
                    .costMoney(batchExecRowVo.getCostMoney())
                    .taxMoney(batchExecRowVo.getCostTax())
                    .build());
            summaryList.add(batchUpdateSummary);
            //效期商品
//            if(YesOrNoEnum.YES.getCode().equals(acceptBillDetail.getPeriodFlag())){
//                batchExecRowVo.getBatchSkuList().forEach(item -> {
//                    item.setBatchCostPrice(batchExecRowVo.getCostPrice());
//                });
//            }
            acceptBillDetail.setBatchCostPrice(batchExecRowVo.getCostPrice());
        });


        Map<Long, BatchUpdateSummary> summaryMap = summaryList.stream().collect(Collectors.toMap(BatchUpdateSummary::getInsideId, Function.identity()));
        Logs.info("批次回写参数：{}", JSON.toJSONString(summaryMap));
        acceptBillDetails.forEach(item->{
            BatchUpdateSummary batchUpdateSummary = summaryMap.get(item.getInsideId());
            if (Objects.nonNull(batchUpdateSummary)) {
                item.setBatchInfo(Jsons.toJson(item.getBatchInfo()));
                item.setPurchPrice(batchUpdateSummary.getCostPrice());
                item.setPurchTaxMoney(batchUpdateSummary.getTotalMoney());
                item.setPurchTax(batchUpdateSummary.getTotalTax());
            }
        });
        pmsAcceptBillDetailRepositoryService.updateDetailByBillNumber(acceptBill, acceptBillDetails);
        BigDecimal totalMoney =acceptBillDetails.stream().map(item -> item.getPurchTaxMoney()).reduce(BigDecimal.ZERO,  BigDecimal::add);
        PmsAcceptBillPO  acceptBillPO = new PmsAcceptBillPO();
        acceptBillPO.setId(acceptBill.getId());
        acceptBillPO.setTotalMoney(totalMoney);
        pmsAcceptBillRepositoryService.updateById(acceptBillPO);
    }

    /**
     * 日结逻辑 更新入账时间
     *
     * @param acceptBill
     * @param batchRecordResp
     */
    public void dayAccount(PmsAcceptBillPO acceptBill, BatchRecordResp batchRecordResp) {
        String costSamMode = getSystemParam(PMSSystemParamEnum.COST_SAM_MODE);
        boolean useBatch = AccountingMethodEnum.useBatch(costSamMode);
        PmsAcceptBillPO updateAcceptBill = new PmsAcceptBillPO();
        if (useBatch) {
            // 2.9.1.1.如果系统参数.成本核算方式=进价成本，则发货入账日期=审核日期；
            //2.9.1.2.如果系统参数.成本核算方式<>进价成本，则发货入账日期=批次流水返回的入账日期；
            LocalDate accDate = batchRecordResp.getAccDate();
            //  批次返回的入账日期
            if(accDate!=null){
                updateAcceptBill.setAccDate(accDate);
            }
        } else {
            updateAcceptBill.setAccDate(acceptBill.getAuditTime().toLocalDate());
        }
        updateAcceptBill.setId(acceptBill.getId());
        pmsAcceptBillRepositoryService.updateById(updateAcceptBill);
    }

    /**
     * 判断退货验收单是否需要修改价格
     * @param acceptBill 验收单信息
     * @param acceptBillDetail 验收单明细
     * @param refundAcceptPriceType 退货价格类型：0-申请单价，1-供应商进价，2-最后进价，3-批次价(先进先出)，4-批次价(高价优先)
     * @return 是否需要修改价格
     */
    private Integer changePriceFlag(PmsAcceptBillPO acceptBill, PmsAcceptBillDetailPO acceptBillDetail, String refundAcceptPriceType) {
        // 正向验收单不需要改价
        if (PmsBillDirectionEnum.NORMAL.getCode().equals(acceptBill.getBillDirection())) {
            return YesOrNoEnum.NO.getCode();
        }
        // 获取商品额外信息
        AcceptBillGoodsExtra extData = acceptBillDetail.getParseExtData();
        // 根据不同的价格类型决定是否需要改价
        switch (refundAcceptPriceType) {
            case "0": // 申请单价
            case "1": // 供应商进价
            case "2": // 最后进价
                return YesOrNoEnum.YES.getCode();
            case "3": // 批次价(先进先出)
            case "4": // 批次价(高价优先)
                return extData.isChangePriceFlag()?YesOrNoEnum.YES.getCode():YesOrNoEnum.NO.getCode();
            default:
                return YesOrNoEnum.NO.getCode();
        }
    }
    // 1. 确定终止类型
    private String determineEndingType(PmsAcceptBillPO stAcceptBill) {
        String applyEndingType = getSystemParam(PMSSystemParamEnum.APPLY_ENDING_TYPE, stAcceptBill.getDeptCode());
        String refundEndingType = getSystemParam(PMSSystemParamEnum.APPLY_REFUND_ENDING_TYPE, stAcceptBill.getDeptCode());
        return PmsBillDirectionEnum.NORMAL.getCode().equals(stAcceptBill.getBillDirection()) ? applyEndingType : refundEndingType;
    }

    // 2. 获取系统参数
    private String getSystemParam(PMSSystemParamEnum paramEnum, String deptCode) {
        return supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(paramEnum, deptCode);
    }

    // 2. 获取系统参数
    private String getSystemParam(PMSSystemParamEnum paramEnum) {
        return supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(paramEnum);
    }

    // 3. 构建库存释放列表
    private List<StkTaskIReleaseExecuteDto> buildReleaseSkuList(PmsAcceptBillPO stAcceptBill,
                                                                Map<String, List<PmsAcceptBillDetailPO>> billDetailMap,
                                                                String engingType,
                                                                Map<String, List<PmsPurchaseBillDetailPO>> purchBillDetailMap) {
        String deptCode = stAcceptBill.getDeptCode();
        String purchBillNo = stAcceptBill.getPurchBillNo();
        String srcBillType = determineSrcBillType(stAcceptBill);
        boolean reversalFlag = YesOrNoEnum.YES.getCode().equals(stAcceptBill.getReversalBillSign());
        if(reversalFlag){
            return Collections.emptyList();
        }
        if (StringUtils.isNotBlank(purchBillNo) && "0".equals(engingType) && purchBillDetailMap != null) {
            return Optional.ofNullable(purchBillDetailMap.get(purchBillNo))
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(item -> StkTaskIReleaseExecuteDto.builder()
                            .whCode(item.getDeptCode())
                            .operateCode(CommonOperateEnum.DECR.getCode())
                            .skuCode(item.getSkuCode())
                            .skuType(item.getSkuType())
                            .realQty(item.getPurchQty())
                            .outWhCode(item.getDeptCode())
                            .srcBillNo(purchBillNo)
                            .srcBillType(srcBillType)
                            .build())
                    .collect(Collectors.toList());
        } else {
            return getAcceptBillDetails(stAcceptBill, billDetailMap)
                    .stream()
                    .filter(item -> StringUtils.isNotBlank(item.getPurchBillNo()))
                    .map(item -> StkTaskIReleaseExecuteDto.builder()
                            .whCode(item.getDeptCode())
                            .operateCode(CommonOperateEnum.DECR.getCode())
                            .skuCode(item.getSkuCode())
                            .skuType(item.getSkuType())
                            .realQty(item.getAcceptQty())
                            .outWhCode(item.getDeptCode())
                            .srcBillNo(item.getPurchBillNo())
                            .srcBillType(srcBillType)
                            .build())
                    .collect(Collectors.toList());
        }
    }

    // 4. 获取验收单明细
    private List<PmsAcceptBillDetailPO> getAcceptBillDetails(PmsAcceptBillPO stAcceptBill,
                                                             Map<String, List<PmsAcceptBillDetailPO>> billDetailMap) {
        return Optional.ofNullable(billDetailMap.get(stAcceptBill.getBillNo()))
                .orElse(Collections.emptyList());
    }

    // 5. 构建库存操作项
    private List<StkTaskItemExecuteDto> buildStkTaskItems(PmsAcceptBillPO stAcceptBill,
                                                          Map<String, List<PmsAcceptBillDetailPO>> billDetailMap) {
        String billType = determineBillType(stAcceptBill);
        String operateCode = determineOperateCode(stAcceptBill);
        boolean reversalFlag = YesOrNoEnum.YES.getCode().equals(stAcceptBill.getReversalBillSign());
        //获取系统参数：采购退货是否允许负库存 0，否；1，是；
        String arStockControlValue = getSystemParam(PMSSystemParamEnum.AR_STOCK_CONTROL,stAcceptBill.getDeptCode());
        // 0，申请单价；1，供应商进价；2，最后进价；3，批次价(先进先出)；4，批次价(高价优先)；
        String refundAcceptPriceType = getSystemParam(PMSSystemParamEnum.REFUND_ACCEPT_PRICE_TYPE, stAcceptBill.getDeptCode());
        return getAcceptBillDetails(stAcceptBill, billDetailMap)
                .stream()
                .map(item -> StkTaskItemExecuteDto.builder()
                        .insideId(item.getInsideId())
                        .skuCode(item.getSkuCode())
                        .skuName(item.getSkuName())
                        .skuType(item.getSkuType())
                        .deptCode(item.getDeptCode())
                        .deptName(item.getDeptName())
                        .whCode(item.getDeptCode())
                        .whName(item.getDeptName())
                        .billType(billType)
                        .operateCode(operateCode)
                        .locationCode(item.getLocationCode())
                        .locationName(item.getLocationName())
                        .supplierCode(stAcceptBill.getSupplierCode())
                        .supplierName(stAcceptBill.getSupplierName())
                        .contractNo(item.getContractNo())
                        .barcode(item.getBarcode())
                        .skuModel(item.getSkuModel())
                        .salePrice(item.getSalePrice())
                        .saleTaxMoney(item.getSaleMoney())
                        .saleMoney(item.getSaleMoney())
                        .saleMode(item.getSaleMode())
                        .periodFlag(item.getPeriodFlag())
                        .inTaxRate(item.getInputTaxRate().divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP))
                        .outTaxRate(item.getOutputTaxRate().divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP))
                        .skuType(item.getSkuType())
                        .saleMode(item.getSaleMode())
                        .saleTax(item.getOutputTaxRate())
                        .srcBillType(reversalFlag ? billType : null)
                        .srcBillNo(reversalFlag ? item.getSrcBillNo() : null)
                        .srcInsideId(reversalFlag ? item.getInsideId().intValue() : null)
                        .costTaxPrice(item.getPurchPrice())
                        .costTaxMoney(item.getPurchTaxMoney())
                        .costMoney(item.getPurchMoney())
                        .periodBarcode(item.getPeriodBarcode())
                        .periodBatchNo(item.getPeriodBatchNo())
                        .productDate(item.getProductDate())
                        .expiryDate(item.getExpireDate())
                        .costTax(item.getPurchTax())
                        .realQty(item.getAcceptQty())
                        .negativeAllowedFlag(Integer.valueOf(arStockControlValue))
                        .changePriceFlag(changePriceFlag(stAcceptBill, item,refundAcceptPriceType))
                        .batchNo(item.getBatchNo())
                        .build())
                .collect(Collectors.toList());
    }

    // 6. 确定业务类型
    private String determineBillType(PmsAcceptBillPO stAcceptBill) {
        return PmsBillDirectionEnum.NORMAL.getCode().equals(stAcceptBill.getBillDirection())
                ? CommonBillTypeEnum.GR.getCode()
                : CommonBillTypeEnum.RNS.getCode();
    }

    // 7. 确定操作类型
    private String determineOperateCode(PmsAcceptBillPO stAcceptBill) {
        return YesOrNoEnum.YES.getCode().equals(stAcceptBill.getReversalBillSign())
                ? CommonOperateEnum.RED.getCode()
                : CommonOperateEnum.POST.getCode();
    }

    // 8. 确定源单类型
    private String determineSrcBillType(PmsAcceptBillPO stAcceptBill) {
        return PmsBillDirectionEnum.NORMAL.getCode().equals(stAcceptBill.getBillDirection())
                ? CommonBillTypeEnum.PO.getCode()
                : CommonBillTypeEnum.RNSR.getCode();
    }

    // 9. 创建批量记录请求
    private BatchRecordReq createBatchRecordRequest(PmsAcceptBillPO stAcceptBill,
                                                    List<StkTaskItemExecuteDto> skuList,
                                                    List<StkTaskIReleaseExecuteDto> releaseSkuList) {
        String deptCode = stAcceptBill.getDeptCode();
        String billNo = stAcceptBill.getBillNo();
        Integer arpricemode = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getIntValue(PMSSystemParamEnum.REFUND_ACCEPT_PRICE_TYPE, deptCode);
        return BatchRecordReq.builder()
                .tenantId(TenantContext.get())
                .deptCode(deptCode)
                .deptName(stAcceptBill.getDeptName())
                .whCode(deptCode)
                .whName(stAcceptBill.getDeptName())
                .billNo(billNo)
                .billType(determineBillType(stAcceptBill))
                .billTime(stAcceptBill.getCreateTime())
                .accReturnResourceType(PmsBillDirectionEnum.NORMAL.getCode().equals(stAcceptBill.getBillDirection())?null:1) // 使用常量代替硬编码
                .skuList(skuList)
                .releaseSkuList(releaseSkuList)
                .accReturnType(arpricemode == 4 ? 2 : 1)
                .inventoryRefundType(0)
                .build();
    }

    // 11. 更新单据并生成转码单
    private void updateBillAndGenerateTsc(PmsAcceptBillPO stAcceptBill, String billNo) {
        stAcceptBill.setAuditTime(LocalDateTime.now());
        try {
            // 查询最新验收单信息
            PmsAcceptBillPO newAcceptBill = queryNewAcceptBill(stAcceptBill, billNo);
            List<PmsAcceptBillDetailPO> newAcceptBillDetails = queryNewBillDetails(newAcceptBill);
            //配送采购不生成转码单
            if (!stAcceptBill.getBillType().equals(PmsBillTypeEnum.DC_PURCH.getCode())) {
                // 异步生成转码单
                autoBuildTscBillFromAcceptBill(newAcceptBill, newAcceptBillDetails);
            }
        } catch (Exception e) {
            Logs.error("更新验收单失败", e);
            throw new BizException("1000", "更新验收单失败:" + e.getMessage());
        }
    }

    // 12. 查询最新验收单
    private PmsAcceptBillPO queryNewAcceptBill(PmsAcceptBillPO stAcceptBill, String billNo) {
        QueryWrapper<PmsAcceptBillPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bill_no", billNo);
        PmsAcceptBillPO newAcceptBill = pmsAcceptBillRepositoryService.getBaseMapper().selectOne(queryWrapper);
        Assert.isNotNull(newAcceptBill, PmsErrorCodeEnum.SC_PMS_004_P001);
        return newAcceptBill;
    }

    // 13. 查询最新验收单明细
    private List<PmsAcceptBillDetailPO> queryNewBillDetails(PmsAcceptBillPO newAcceptBill) {
        return pmsAcceptBillDetailRepositoryService.selectDetailList(newAcceptBill.getBillNo());
    }


    private static PmsAcceptBillDetailPO mergePmsAcceptBillDetailPO(PmsAcceptBillDetailPO existing, PmsAcceptBillDetailPO replacement) {
        PmsAcceptBillDetailPO merged = CglibCopier.copy(existing, PmsAcceptBillDetailPO.class);
        // 合并需要相加的字段
        merged.setStockQty(existing.getStockQty().add(replacement.getStockQty()));
        merged.setAcceptQty(existing.getAcceptQty().add(replacement.getAcceptQty()));
        merged.setPeriodQty(existing.getPeriodQty().add(replacement.getPeriodQty()));
        merged.setOddQty(existing.getOddQty().add(replacement.getOddQty()));
        return merged;
    }

    private void autoBuildTscBillFromAcceptBill(PmsAcceptBillPO newAcceptBill, List<PmsAcceptBillDetailPO> newAcceptBillDetails) {
        String tenantId = TenantContext.get();
        String nowTraceId = TraceIds.get();
        CompletableFuture.runAsync(() -> {
                TenantContext.set(tenantId);
                TraceIds.set(nowTraceId);
            try {
                final Integer billDirection = newAcceptBill.getBillDirection();
                final boolean refundFlag = PmsBillDirectionEnum.NORMAL.getCode().equals(billDirection);
                // 根据验收单商品 查询转码策略 得到 自动生成方式等于 自动生成的所有商品
//            Map<String, PmsAcceptBillDetailPO> acceptBillDetailMap = newAcceptBillDetails.stream().collect(Collectors.toMap(PmsAcceptBillDetailPO::getSkuCode, Function.identity()));
                Map<String, PmsAcceptBillDetailPO> acceptBillDetailMap = newAcceptBillDetails.stream()
                        .collect(Collectors.toMap(
                                PmsAcceptBillDetailPO::getSkuCode,
                                Function.identity(),
                                (existing, replacement) -> mergePmsAcceptBillDetailPO(existing, replacement)
                        ));
                List<String> skuCodeList = new ArrayList<>(acceptBillDetailMap.keySet());
                List<TranscodingStrategyResp> transcodingStrategyListAll = supplychainBizBillRuleService.getStrategyListFromGoodsCode(skuCodeList, true);
                Logs.info("验收自动转码，单号 {} 是否退货 {} 所有涉及策略数量 {}", newAcceptBill.getBillNo(), refundFlag, transcodingStrategyListAll.size());
                final List<TranscodingStrategyResp> transcodingStrategyList = transcodingStrategyListAll.stream()
                        .filter(item -> item.getState() == 0)
//                        .filter(item -> YesOrNoEnum.YES.getCode().equals(item.getAutoBuildType()))
                        .collect(Collectors.toList());
                Logs.info("验收自动转码，单号 {} 是否退货 {} 需要验收自动生成的策略数量 {}", newAcceptBill.getBillNo(), refundFlag, transcodingStrategyList.size());
                if (CollectionUtils.isEmpty(transcodingStrategyList)) {
                    return;
                }
                List<String> fpGoodsCodeList = transcodingStrategyList.stream()
                        .map(TranscodingStrategyResp::getFpGoodsCode).distinct().collect(Collectors.toList());
                // 批量查询商品 库存
                final ArrayList<String> allSkuCodeList = new ArrayList<>(fpGoodsCodeList);
                allSkuCodeList.addAll(skuCodeList);
                List<GoodsQueryResp> goodsQueryRespList = commonGoodsService.getGoodsInfo(GoodsQueryReq.builder()
                        .storeCode(newAcceptBill.getDeptCode()).goodsCodeList(allSkuCodeList).attributeNameFlag(false).build());
                Map<String, GoodsQueryResp.GoodsInfo> goodsInfoMap = goodsQueryRespList.stream().map(GoodsQueryResp::getGoodsInfo)
                        .collect(Collectors.toMap(GoodsQueryResp.GoodsInfo::getSkuCode, Function.identity()));
                // 库存信息
                StockQueryWithDeptAndSkusDTO stockQueryWithDeptAndSkusDTO = new StockQueryWithDeptAndSkusDTO();
                stockQueryWithDeptAndSkusDTO.setDeptCode(newAcceptBill.getDeptCode());
                stockQueryWithDeptAndSkusDTO.setSkuCodeList(allSkuCodeList);
                stockQueryWithDeptAndSkusDTO.setNeedPeriodStk(true);
                Logs.info("获取商品库存入参：{}", JSON.toJSONString(stockQueryWithDeptAndSkusDTO));
                List<StockVO> goodsStockList = iCommonStockService.getWhStockQquery(stockQueryWithDeptAndSkusDTO);
                final Map<String, StockVO> skuStockDtoMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(goodsStockList)) {
                    skuStockDtoMap.putAll(goodsStockList.stream().collect(Collectors.toMap(StockVO::getSkuCode, Function.identity())));
                }

                // 根据原料分组 每个组去第一个
                Map<String, List<TranscodingStrategyResp>> rmStrategyMap = transcodingStrategyList.stream().collect(Collectors.groupingBy(TranscodingStrategyResp::getRmGoodsCode));
                // 根据转码策略自动生成转码单

                OpInfo operatorInfo = new OpInfo();
                operatorInfo.setOperatorCode(newAcceptBill.getAuditManCode());
                operatorInfo.setOperatorName(newAcceptBill.getAuditManName());
                TranscodingBillReq req = new TranscodingBillReq();
                req.setDeptCode(newAcceptBill.getDeptCode());
                req.setDeptName(newAcceptBill.getDeptName());
                req.setSubmitTime(new Date());
                req.setSubmitManCode(operatorInfo.getOperatorCode());
                req.setSubmitManName(operatorInfo.getOperatorName());
                req.setBillState(MachinSplitStateEnum.AUDITED.getCode());
                req.setRemark(refundFlag ? "验收退货自动生成" : "验收自动生成");
                String tscBillNo = supplychainBizBillRuleService.getBillNo(MdBillNoBillTypeEnum.PRODUCT_CONVERSION, newAcceptBill.getDeptCode());
                req.setBillNumber(tscBillNo);
                // 组装商品
                List<TranscodingBillDetail> detailList = new ArrayList<>();
                rmStrategyMap.keySet().forEach(rmGoodsCode -> {
                    List<TranscodingStrategyResp> strategyList = rmStrategyMap.get(rmGoodsCode);
                    PmsAcceptBillDetailPO acceptBillDetail = acceptBillDetailMap.get(rmGoodsCode);
                    // 倒序 第一
                    final List<TranscodingStrategyResp> reversedList = strategyList.stream()
                            .filter(item -> {
                                final GoodsQueryResp.GoodsInfo goodsInfo = goodsInfoMap.get(item.getFpGoodsCode());
                                return YesOrNoEnum.YES.getCode().equals(goodsInfo.getInCatalog());
                            })
                            .sorted(Comparator.comparingLong(TranscodingStrategyResp::getId).reversed())
                            .collect(Collectors.toList());
                    Logs.info("验收自动转码，单号 {} 是否退货 {} 原料 {} 成品在目录策略数量 {}", newAcceptBill.getBillNo(), refundFlag,
                            rmGoodsCode, reversedList.size());
                    if (CollectionUtils.isEmpty(reversedList)) {
                        return;
                    }
                    TranscodingStrategyResp stTranscodingStrategy = reversedList.get(0);

                    String rmGoodsCodeReal = refundFlag ? stTranscodingStrategy.getFpGoodsCode() : rmGoodsCode;
                    String fpGoodsCodeReal = refundFlag ? rmGoodsCode : stTranscodingStrategy.getFpGoodsCode();

                    BigDecimal rmSkuRealStock = Objects.isNull(skuStockDtoMap.get(rmGoodsCodeReal)) ? BigDecimal.ZERO : skuStockDtoMap.get(rmGoodsCodeReal).getRealQty();
                    BigDecimal fpSkuRealStock = Objects.isNull(skuStockDtoMap.get(fpGoodsCodeReal)) ? BigDecimal.ZERO : skuStockDtoMap.get(fpGoodsCodeReal).getRealQty();
                    GoodsQueryResp.GoodsInfo rmGoodsInfo = goodsInfoMap.get(rmGoodsCodeReal);
                    if (Objects.isNull(rmGoodsInfo)) {
                        BizExceptions.throwWithMsg(String.format("自动转码失败，原料商品%s不存在", rmGoodsCodeReal));
                    }
                    GoodsQueryResp.GoodsInfo fpGoodsInfo = goodsInfoMap.get(fpGoodsCodeReal);
                    if (Objects.isNull(fpGoodsInfo)) {
                        BizExceptions.throwWithMsg(String.format("自动转码失败，成品商品%s不存在", fpGoodsCodeReal));
                    }
                    long rmStock;
                    if (MeasurePropertyEnum.WEIGH.getCode().equals(rmGoodsInfo.getMesureProperty())) {
                        rmStock = MoneyUtil.long2BigDecimal(rmSkuRealStock.longValue()).longValue();
                    } else {
                        rmStock = MoneyUtil.valTo10Hundred(rmSkuRealStock.longValue());
                    }
                    long fpStock;
                    if (MeasurePropertyEnum.WEIGH.getCode().equals(fpGoodsInfo.getMesureProperty())) {
                        fpStock = MoneyUtil.long2BigDecimal(fpSkuRealStock.longValue()).longValue();
                    } else {
                        fpStock = MoneyUtil.valTo10Hundred(fpSkuRealStock.longValue());
                    }
                    TranscodingBillDetail.TranscodingBillDetailBuilder builder = TranscodingBillDetail.builder()
                            .billNumber(req.getBillNumber())
                            .rmGoodsCode(rmGoodsInfo.getSkuCode())
                            .rmGoodsName(rmGoodsInfo.getSkuName())
                            .rmBarcode(rmGoodsInfo.getSkuBarcode())
                            .rmGoodsNo(rmGoodsInfo.getSkuGoodsNo())
                            .rmGoodsSpec(refundFlag ? stTranscodingStrategy.getFpGoodsSpec() : stTranscodingStrategy.getRmGoodsSpec())
                            .rmBasicUnit(rmGoodsInfo.getUnitName())
                            .rmTaxRate(MoneyUtil.int2BigDecimal(rmGoodsInfo.getInputTaxRate()).longValue())
                            .rmIsExpiration(rmGoodsInfo.getExpiryDateControlFlag())
                            .rmIsWeight(MeasurePropertyEnum.WEIGH.getCode().equals(rmGoodsInfo.getMesureProperty())
                                    ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode())
                            .rmStockNum(rmStock)
                            .fpGoodsCode(fpGoodsInfo.getSkuCode())
                            .fpGoodsName(fpGoodsInfo.getSkuName())
                            .fpBarcode(fpGoodsInfo.getSkuBarcode())
                            .fpGoodsNo(fpGoodsInfo.getSkuGoodsNo())
                            .fpGoodsSpec(refundFlag ? stTranscodingStrategy.getRmGoodsSpec() : stTranscodingStrategy.getFpGoodsSpec())
                            .fpBasicUnit(fpGoodsInfo.getUnitName())
                            .fpTaxRate(MoneyUtil.int2BigDecimal(fpGoodsInfo.getInputTaxRate()).longValue())
                            .fpIsExpiration(fpGoodsInfo.getExpiryDateControlFlag())
                            .fpIsWeight(MeasurePropertyEnum.WEIGH.getCode().equals(fpGoodsInfo.getMesureProperty())
                                    ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode())
                            .fpStockNum(fpStock)
                            .scaleFactor(stTranscodingStrategy.getScaleFactor())
                            .buildManCode(operatorInfo.getOperatorCode())
                            .buildManName(operatorInfo.getOperatorName());

                    // 此处保存的比例 是 * 10000 需要还原
                    BigDecimal scaleFactor = MoneyUtil.longMilliToYuan(stTranscodingStrategy.getScaleFactor());
                    final AutoBuildTscReq.AutoBuildTscReqBuilder tscReqBuilder = AutoBuildTscReq.builder()
                            .acceptBillDetail(acceptBillDetail).scaleFactor(scaleFactor)
                            .refundFlag(refundFlag)
                            .builder(builder);
                    // 原料
                    setAutoAscGoodsPriceInfo(tscReqBuilder.rmFlag(true).goodsInfo(rmGoodsInfo).build());
                    // 成品
                    setAutoAscGoodsPriceInfo(tscReqBuilder.rmFlag(false).goodsInfo(fpGoodsInfo).build());
                    final TranscodingBillDetail build = builder.build();
                    detailList.add(build);
                });
                req.setDetailList(detailList);
                Logs.info("验收自动转码，单号 {} 是否退货 {} 开始自动生成转码单 {} 商品数量 {}", newAcceptBill.getBillNo(), refundFlag,
                        req.getBillNumber(), req.getDetailList().size());
                Map<String, Object> map = BeanUtil.beanToMap(operatorInfo);
                map.put("operateTime",new Date());
                req.setOperatorInfo(map);
                supplychainBizBillRuleService.saveTscBill(req);
                // 将验收主表  记录本次转码单单号
                PmsAcceptBillPO acceptBill = new PmsAcceptBillPO();
                acceptBill.setId(newAcceptBill.getId());
                acceptBill.setTscBillNo(tscBillNo);
                pmsAcceptBillRepositoryService.updateById(acceptBill);
            } catch (Exception e) {
                Logs.error("验收单号 {} 自动生成转码单 异常", newAcceptBill.getBillNo());
                throw e;
            }
        }, acceptTranscodingThreadPool);
    }


    /**
     * 修改验收单
     *
     * @param acceptBillDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<ApplyBillResp> updateAcceptBill(AcceptBillDTO acceptBillDTO) {
        //操作人信息
        OpInfo operatorInfo = userUtil.getOpInfoWithThrow();
        List<String> redisWords = Arrays.asList(TenantContext.get(), "createAcceptBill", acceptBillDTO.getBillNo());
        String redisKey = String.join(SysConstants.UNDERLINE_DELIMITER, redisWords);
        if (!redisUtil.tryLock(redisKey, acceptBillDTO.getBillNo(), EXPIRE_TIME)){
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_D001);
        }
        try {
            QueryWrapper<PmsAcceptBillPO> billQueryWrapper = new QueryWrapper<>();
            billQueryWrapper.eq("bill_no", acceptBillDTO.getBillNo());

            // 系统参数校验
            Map<String, GoodsQueryResp.GoodsInfo> goodsInfoMap = new HashMap<>();
            PmsAcceptBillPO acceptBillPO = pmsAcceptBillRepositoryService.getBaseMapper().selectOne(billQueryWrapper);
            acceptBillDTO.setBillSource(acceptBillPO.getBillSource());
            createAcceptBillCheck(acceptBillDTO, goodsInfoMap);
            Assert.isTrue(AcceptStateEnum.canUpdate(acceptBillPO.getStatus()), PmsErrorCodeEnum.SC_PMS_004_P011);

            PmsAcceptBillPO stAcceptBill = acceptBillCopier.convertUpdate2Accept(acceptBillDTO);
            stAcceptBill.setId(acceptBillPO.getId());
            if (PmsAcceptBillStateEnum.PENDING_AUDIT.getBillStateCode().equals(acceptBillDTO.getAcceptStatus())) {
                stAcceptBill.setStatus(PmsAcceptBillStateEnum.PENDING_AUDIT.getBillStateCode());
                stAcceptBill.setSubmitTime(LocalDateTime.now());
                stAcceptBill.setSubmitManCode(operatorInfo.getOperatorCode());
                stAcceptBill.setSubmitManName(operatorInfo.getOperatorName());
            }

            List<PmsAcceptBillDetailPO> detailList = new ArrayList<>();
            AtomicLong batchInsideId = new AtomicLong(1L);
            List<PmsBillBatchInfoPO> billBatchInfoList = new ArrayList<>();
            AtomicLong insideId = new AtomicLong(1L);
            List<PmsAcceptBillDetailPO> acceptBillDetailList = acceptBillDTO.getAcceptDetails().stream().map(e -> {
                GoodsQueryResp.GoodsInfo goodsInfo = goodsInfoMap.get(e.getSkuCode());
                return convertAcceptBillDetail(acceptBillDTO,e,goodsInfo,insideId.getAndIncrement());
            }).collect(Collectors.toList());
            Logs.info("保存验收单明细:{}", JSON.toJSONString(acceptBillDetailList));
            BigDecimal totalMoney = acceptBillDetailList.stream().map(PmsAcceptBillDetailPO::getPurchTaxMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalQty = acceptBillDetailList.stream().map(PmsAcceptBillDetailPO::getAcceptQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            long productInsideId = 0L;
            LambdaQueryWrapper<PmsAcceptBillDetailPO> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.eq(PmsAcceptBillDetailPO::getBillNo, acceptBillDTO.getBillNo());
            detailWrapper.ge(PmsAcceptBillDetailPO::getCreateTime, acceptBillPO.getCreateTime().with(LocalTime.MIN).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            detailWrapper.le(PmsAcceptBillDetailPO::getCreateTime, acceptBillPO.getCreateTime().with(LocalTime.MAX).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            pmsAcceptBillDetailRepositoryService.getBaseMapper().delete(detailWrapper);
            pmsAcceptBillDetailRepositoryService.saveBatch(acceptBillDetailList);
            stAcceptBill.setTotalQty(totalQty);
            stAcceptBill.setTotalMoney(totalMoney);
            pmsAcceptBillRepositoryService.updateById(stAcceptBill);

            if (PmsAcceptBillStateEnum.AUDIT.getBillStateCode().equals(acceptBillDTO.getAcceptStatus())) {
                AuditAcceptBillDTO auditAcceptBillDTO = new AuditAcceptBillDTO();
                auditAcceptBillDTO.setBillNoList(Lists.newArrayList(acceptBillDTO.getBillNo()));
                auditAcceptBillDTO.setBillSource(acceptBillDTO.getBillSource());
                auditAcceptBill(auditAcceptBillDTO);
            }
        } catch (Exception e) {
            Logs.error("更新单据异常:{}",e.getMessage(),e);
            BizExceptions.throwWithMsg(String.format("更新单据异常:%s",e.getMessage()));
//            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_004_P017);
        } finally {
            redisUtil.unlock(redisKey);
        }
        return Results.ofSuccess(new ApplyBillResp(acceptBillDTO.getBillNo(), null));
    }

    /**
     * 冲红验收单
     *
     * @param acceptBillDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<ApplyBillResp> reversalAcceptBill(CancelAcceptBillDTO acceptBillDTO) {
        ApplyBillResp resp = new ApplyBillResp();
        //操作人信息
        OpInfo operatorInfo = userUtil.getOpInfoWithThrow();
        List<String> redisWords = Arrays.asList(TenantContext.get(), "reversalAcceptBill", acceptBillDTO.getBillNo());
        String redisKey = String.join(SysConstants.UNDERLINE_DELIMITER, redisWords);
        if (!redisUtil.tryLock(redisKey, acceptBillDTO.getBillNo(), EXPIRE_TIME)){
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_D001);
        }
        try {
            //校验单据状态&是否存在
            QueryWrapper<PmsAcceptBillPO> wrapper = new QueryWrapper<>();
            wrapper.eq("bill_no", acceptBillDTO.getBillNo());
            PmsAcceptBillPO acceptBills = pmsAcceptBillRepositoryService.getBaseMapper().selectOne(wrapper);
            //校验当前验收单是否存在
            Assert.isNotNull(acceptBills, PmsErrorCodeEnum.SC_PMS_004_P001);
            Assert.isBlank(acceptBills.getReversalBillNo(), PmsErrorCodeEnum.SC_PMS_004_P013);
            try {
                // 开启独立事务
                // 3.冲红验收单时（验收时记录转码单号），自动冲红原转码单，当转码单冲红成功之后再生成验收冲红单；
                // 转码单单号
                if (StringUtils.isNotBlank(acceptBills.getTscBillNo()) && !PmsBillTypeEnum.DC_PURCH.getCode().equals(acceptBills.getBillType())) {
                    Logs.info("验收单号 {} 存在转码单 {} 需要冲红", acceptBills.getBillNo(), acceptBills.getTscBillNo());
                    pmsAcceptServiceHelper.reverseTscBill(acceptBills,operatorInfo);
                }
            } catch (Exception e) {
                //            sysAlarmRobot.asyncSysAlarm(String.format("验收单号 %s 冲红转码单失败", acceptBills.getAcceptBillNumber()));
            }
            List<PmsAcceptBillDetailPO> stAcceptBillDetails = pmsAcceptBillDetailRepositoryService.selectDetailList(acceptBillDTO.getBillNo());
            MdBillNoBillTypeEnum mdBillNoBillTypeEnum = PmsBillDirectionEnum.NORMAL.getCode().equals(acceptBills.getBillDirection()) ? MdBillNoBillTypeEnum.PROCUREMENT_ACCEPTANCE : MdBillNoBillTypeEnum.ACCEPTANCE_RETURN;
            String billNo = supplychainBizBillRuleService.getBillNo(mdBillNoBillTypeEnum, acceptBillDTO.getDeptCode());
            //将原单采购验收单冲红
            PmsAcceptBillPO acceptBill = new PmsAcceptBillPO();
            acceptBill.setId(acceptBills.getId());
            acceptBill.setReversalBillNo(billNo);
            acceptBill.setReversalFlag(YesOrNoEnum.YES.getCode());
            pmsAcceptBillRepositoryService.updateById(acceptBill);

            PmsAcceptBillPO cancelStAcceptBill = CglibCopier.copy(acceptBills, PmsAcceptBillPO.class);
            cancelStAcceptBill.setTenantId(null);
            cancelStAcceptBill.setReversalBillSign(YesOrNoEnum.YES.getCode());
            cancelStAcceptBill.setPurchBillNo(acceptBills.getPurchBillNo());
            cancelStAcceptBill.setBillNo(billNo);
            cancelStAcceptBill.setSubmitTime(LocalDateTime.now());
            cancelStAcceptBill.setSubmitManCode(operatorInfo.getOperatorCode());
            cancelStAcceptBill.setSubmitManName(operatorInfo.getOperatorName());
            cancelStAcceptBill.setAuditTime(LocalDateTime.now());
            cancelStAcceptBill.setAuditManCode(operatorInfo.getOperatorCode());
            cancelStAcceptBill.setAuditManName(operatorInfo.getOperatorName());
            cancelStAcceptBill.setCreateTime(LocalDateTime.now());
            cancelStAcceptBill.setUpdateTime(LocalDateTime.now());
            cancelStAcceptBill.setRemark("冲红自动生成");
            cancelStAcceptBill.setOriginBillNo(acceptBills.getBillNo());
            cancelStAcceptBill.setPurchBillNo(null);
            pmsAcceptBillRepositoryService.save(cancelStAcceptBill);
            List<PmsAcceptBillDetailPO> cancelAcceptBillDetails = CglibCopier.copy(stAcceptBillDetails, PmsAcceptBillDetailPO.class);
            for (PmsAcceptBillDetailPO cancelAcceptBillDetail : cancelAcceptBillDetails) {
                cancelAcceptBillDetail.setBillNo(billNo);
                cancelAcceptBillDetail.setId(null);
                cancelAcceptBillDetail.setRemark(null);
                cancelAcceptBillDetail.setTenantId(null);
                cancelAcceptBillDetail.setSrcBillNo(acceptBills.getBillNo());
            }
            pmsAcceptBillDetailRepositoryService.saveBatch(cancelAcceptBillDetails);
            uploadStockBatch(cancelStAcceptBill, cancelAcceptBillDetails.stream().collect(Collectors.groupingBy(PmsAcceptBillDetailPO::getBillNo)), null);
            resp.setBillNumber(cancelStAcceptBill.getBillNo());
        } catch (BizException e) {
            Logs.info("冲红订单；{} 发生异常:{}",acceptBillDTO.getBillNo(), e.getMessage(), e);
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_004_P019);
        }finally {
            redisUtil.unlock(redisKey);
        }
        return new Result<>(resp);
    }

    /**
     * 作废验收单
     *
     * @param acceptBillDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<ApplyBillResp> cancelAcceptBill(CancelAcceptBillDTO acceptBillDTO) {
        //操作人信息
        OpInfo operatorInfo = userUtil.getOpInfoWithThrow();
        List<String> redisWords = Arrays.asList(TenantContext.get(), "cancelAcceptBill", acceptBillDTO.getBillNo());
        String redisKey = String.join(SysConstants.UNDERLINE_DELIMITER, redisWords);
        if (!redisUtil.tryLock(redisKey, acceptBillDTO.getBillNo(), EXPIRE_TIME)){
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_D001);
        }
        try {
            // 校验单据状态&是否存在
            QueryWrapper<PmsAcceptBillPO> wrapper = new QueryWrapper<>();
            wrapper.eq("bill_no", acceptBillDTO.getBillNo());
            PmsAcceptBillPO acceptBillPO = pmsAcceptBillRepositoryService.getBaseMapper().selectOne(wrapper);
            Assert.isNotNull(acceptBillPO, PmsErrorCodeEnum.SC_PMS_004_P001);
            Assert.isTrue(AcceptStateEnum.canCancel(acceptBillPO.getStatus()), PmsErrorCodeEnum.SC_PMS_004_P002);
            PmsAcceptBillPO acceptBill = new PmsAcceptBillPO();
            acceptBill.setId(acceptBillPO.getId());
            acceptBill.setStatus(AcceptStateEnum.VOIDED.getBillStateCode());
            acceptBill.setCancelManCode(operatorInfo.getOperatorCode());
            acceptBill.setCancelManName(operatorInfo.getOperatorName());
            pmsAcceptBillRepositoryService.updateById(acceptBill);
        } catch (Exception e) {
            Logs.error(e.getMessage(), e);
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_004_P018);
        } finally {
            redisUtil.unlock(redisKey);
        }
        return null;
    }

    /**
     * 获取验收单列表-pc
     *
     * @param req
     * @return
     */
    @Override
    public Result<PageResult<AcceptBillResp>> queryAcceptList(QueryAcceptDTO req) {
        LambdaQueryWrapper<PmsAcceptBillPO> wrapper = new LambdaQueryWrapper<>();
        OpInfo operatorInfo = req.getOperatorInfo();
        if (operatorInfo.getOriginDeptFlag()) {
            wrapper.eq(StringUtils.isNotBlank(req.getDeptCode()), PmsAcceptBillPO::getDeptCode, req.getDeptCode());
        } else {
            if (StringUtils.isNotEmpty(req.getDeptCode())) {
                if (CollectionUtils.isNotEmpty(operatorInfo.getManageDeptCodeList()) && operatorInfo.getManageDeptCodeList().contains(req.getDeptCode())) {
                    wrapper.eq(PmsAcceptBillPO::getDeptCode, req.getDeptCode());
                } else {
                    BizExceptions.throwWithMsg("当前分管部门没有查询权限");
                }
            } else {
                wrapper.in(PmsAcceptBillPO::getDeptCode, operatorInfo.getManageDeptCodeList());
            }
        }
        wrapper.in(StringUtils.isNotBlank(req.getSupplierCode()), PmsAcceptBillPO::getSupplierCode, req.getSupplierCode());
        wrapper.ge(StringUtils.isNotBlank(req.getStartTime()), PmsAcceptBillPO::getCreateTime, req.getStartTime() + " 00:00:00");
        wrapper.le(StringUtils.isNotBlank(req.getEndTime()), PmsAcceptBillPO::getCreateTime, req.getEndTime() + " 23:59:59");
        wrapper.in(CollectionUtils.isNotEmpty(req.getStatus()), PmsAcceptBillPO::getStatus, req.getStatus());
        wrapper.and(StringUtils.isNotBlank(req.getBillNo()),
                w -> w.like(PmsAcceptBillPO::getBillNo, req.getBillNo())
                        .or()
                        .like(PmsAcceptBillPO::getPurchBillNo, req.getBillNo()));
        wrapper.eq(req.getBillDirection() != null, PmsAcceptBillPO::getBillDirection, req.getBillDirection());
        wrapper.eq(req.getReversalFlag() != null, PmsAcceptBillPO::getReversalFlag, req.getReversalFlag());
        wrapper.and(StringUtils.isNotBlank(req.getBuildManName()),
                w -> w.like(PmsAcceptBillPO::getCreateName, req.getBuildManName())
                        .or()
                        .like(PmsAcceptBillPO::getCreateCode, req.getBuildManName()));
        wrapper.like(StringUtils.isNotBlank(req.getRemark()), PmsAcceptBillPO::getRemark, req.getRemark());
        wrapper.eq(StringUtils.isNotEmpty(req.getManageCategoryClass()) && CollectionUtils.isNotEmpty(req.getManageCategory()),
                PmsAcceptBillPO::getManageCategoryClass, req.getManageCategoryClass());
        wrapper.in(StringUtils.isNotEmpty(req.getManageCategoryClass()) && CollectionUtils.isNotEmpty(req.getManageCategory()),
                PmsAcceptBillPO::getManageCategoryCode, req.getManageCategory());
        wrapper.eq(req.getBillType()!=null,PmsAcceptBillPO::getBillType,req.getBillType());
        wrapper.orderByDesc(PmsAcceptBillPO::getCreateTime);
        Page<PmsAcceptBillPO> page = new Page<>(req.getCurrent(), req.getPageSize());
        Page<PmsAcceptBillPO> acceptBillPage = pmsAcceptBillRepositoryService.getBaseMapper().selectPage(page, wrapper);

        PageResult result = CglibCopier.copy(acceptBillPage, PageResult.class);
        if (acceptBillPage.getSize() > 0) {
            List<AcceptBillResp> queryApplyBillResps = acceptBillCopier.convert2RespList(acceptBillPage.getRecords());
            queryApplyBillResps.forEach(item -> {
                if (Objects.nonNull(item.getBillSource())) {
                    item.setBillSourceDesc(PmsBillSourceEnum.getByType(item.getBillSource()).getBillSourceDesc());
                }
            });

            result.setRows(queryApplyBillResps);
        }
        result.setTotal(acceptBillPage.getTotal());
        return Results.ofPage(result);
    }

    /**
     * 获取验收单列表-app
     *
     * @param req
     * @return
     */
    @Override
    public Result<PageResult<AcceptBillResp>> queryAcceptList4App(QueryAccept4AppDTO req) {
        Page<PmsAcceptBillPO> page = new Page<>(req.getCurrent(), req.getPageSize());
        req.setStartTime(req.getStartTime() + " 00:00:00");
        req.setEndTime(req.getEndTime() + " 23:59:59");
        Page<PmsAcceptBillPO> stApplyBills = pmsAcceptBillRepositoryService.selectList4App(req, page);
        PageResult result = CglibCopier.copy(stApplyBills, PageResult.class);
        if (stApplyBills.getSize() > 0) {
            List<AcceptBillResp> queryApplyBillResps = acceptBillCopier.convert2RespList(stApplyBills.getRecords());
            queryApplyBillResps.forEach(item -> {
                if (Objects.nonNull(item.getBillSource())) {
                    item.setBillSourceDesc(PmsBillSourceEnum.getByType(item.getBillSource()).getBillSourceDesc());
                }
            });
            result.setRows(queryApplyBillResps);
        }
        result.setTotal(stApplyBills.getTotal());
        return Results.ofPage(result);
    }

    /**
     * 按状态分组查询验收单数量
     *
     * @param queryAccept4AppDTO
     * @return
     */
    @Override
    public Result<List<AcceptStatisticResp>> queryCount4StateGroup(QueryAccept4AppDTO queryAccept4AppDTO) {
        queryAccept4AppDTO.setStartTime(queryAccept4AppDTO.getStartTime() + " 00:00:00");
        queryAccept4AppDTO.setEndTime(queryAccept4AppDTO.getEndTime() + " 23:59:59");
        List<AcceptStatisticResp> statisticData = pmsAcceptBillRepositoryService.getStatisticData(queryAccept4AppDTO);
        return Results.ofSuccess(pmsAcceptBillRepositoryService.getStatisticData(queryAccept4AppDTO).stream().map(item -> {
            item.setStatusDesc(AcceptStateEnum.getEnumByCode(item.getStatus()).getBillStateName());
            return item;
        }).collect(Collectors.toList()));
    }

    @Override
    public Result<AcceptBillResp> acceptDetail(QueryAcceptDetailReq acceptDetailReq) {
        AcceptBillResp resp = queryDetailList(acceptDetailReq);
        return Results.ofSuccess(resp);
    }

    /**
     * 验收单打印
     *
     * @param req
     * @return
     */
    @Override
    public Result<AcceptanceInfoResp> printAcceptDetail(QueryAcceptDetailReq req) {

        AcceptanceInfoResp response = new AcceptanceInfoResp();
        AcceptBillResp resp = queryDetailList(req);
            response.convertResp(response, resp);
        //组装门店信息，供应商信息
        try {
            StoreDetailResp storeInfo = baseDataSystemFeignClient.getSingleDeptInfo(req.getDeptCode());
            if (!Objects.isNull(storeInfo)) {
                response.setDeptAddress(storeInfo.getWholeAddress());
                response.setDeptManage(storeInfo.getManagerUserName());
                response.setDeptHotline(storeInfo.getServiceHotline());
            }
        } catch (Exception e) {
            Logs.info("打印验收单，获取门店基础信息异常...", e);
        }

        if (CollectionUtils.isNotEmpty(resp.getDetailList())) {
            response.setContractNumber(resp.getDetailList().get(0).getContractNo());
            response.setContractManageMode(resp.getDetailList().get(0).getManageModel());
        }
        //单据方向
        Integer billDirection = checkBillDirection(resp.getBillDirection(), resp.getReversalBillSign());
        Boolean showPriceFlag = checkPriceFlag(billDirection);
        if(YesOrNoEnum.YES.getCode().equals(req.getOperateType())){
            //打印次数+1
            pmsAcceptBillRepositoryService.increasePrintCountByBillNumber(req.getBillNo());
        }
        return Results.ofSuccess(response);
    }



    /**
     * 获取申请单据的查看进价权限
     *
     * @param billDirection 0-验收，1-退货
     * @return
     */
    private boolean checkPriceFlag(Integer billDirection) {
        OpInfo operatorInfo = userUtil.getDeptOpInfoWithThrow();
        String source = ServletContextHolder.getValueByCookieName("source");
        Boolean showPriceFlag = false;
        if (PmsSourceEnum.isApp(source)) {
            if (PmsBillDirectionEnum.NORMAL.getCode().equals(billDirection)) {
                showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_MD_DIST_PRICE_AUDIT_BUTTON));
            } else {
                showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_MD_DIST_PRICE_AUDIT_BUTTON));
            }
        } else {
            if (PmsBillDirectionEnum.NORMAL.getCode().equals(billDirection)) {
                showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_MD_DIST_PRICE_AUDIT_BUTTON));
            } else {
                showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_MD_DIST_PRICE_AUDIT_BUTTON));
            }
        }
        return showPriceFlag;
    }

    /**
     * 采购验收单的单据方向
     */
    public static Integer checkBillDirection(Integer billDirection, Integer isCancelBill) {
        boolean isNORMALDirection = PmsBillDirectionEnum.NORMAL.getCode().equals(billDirection);
        boolean isNotCancel = YesOrNoEnum.NO.getCode().equals(isCancelBill);
        // 验收单方向与冲红标志的组合判断
        return (isNORMALDirection && isNotCancel) ? PmsBillDirectionEnum.NORMAL.getCode() :
                (isNORMALDirection && !isNotCancel) ? PmsBillDirectionEnum.REVERSE.getCode() :
                        (!isNORMALDirection && isNotCancel) ? PmsBillDirectionEnum.REVERSE.getCode() :
                                PmsBillDirectionEnum.NORMAL.getCode();
    }

    @Override
    public List<AcceptDetailResp> queryAcceptDetailList(QueryAcceptDetailReq req) {
        return pmsAcceptBillDetailRepositoryService.getAcceptDetailList(req.getBillNos());
    }

    /**
     * 验收单明细
     *
     * @param req
     * @return
     */
    public AcceptBillResp queryDetailList(QueryAcceptDetailReq req) {
        // 单据状态校验
        QueryWrapper<PmsAcceptBillPO> wrapper = new QueryWrapper<>();
        wrapper.eq("bill_no", req.getBillNo());
        PmsAcceptBillPO acceptBill = pmsAcceptBillRepositoryService.getBaseMapper().selectOne(wrapper);
        if (acceptBill == null) {
            BizExceptions.throwWithMsg("当前单据不存在");
        }
        AcceptBillResp acceptanceInfoResp = acceptBillCopier.convertToResp(acceptBill);
        acceptanceInfoResp.setBillSourceDesc(PmsBillSourceEnum.getByType(acceptanceInfoResp.getBillSource()).getBillSourceDesc());
        List<PmsAcceptBillDetailPO> acceptBillDetailList = pmsAcceptBillDetailRepositoryService.selectDetailList(req.getBillNo());
        acceptanceInfoResp.setDetailList(Lists.transform(acceptBillDetailList, new com.google.common.base.Function<PmsAcceptBillDetailPO, AcceptDetailResp>() {
            @Override
            public AcceptDetailResp apply(PmsAcceptBillDetailPO input) {
                AcceptDetailResp acceptDetailResp = new AcceptDetailResp();
                BeanUtil.copyProperties(input,acceptDetailResp);
                return acceptDetailResp;
            }
        }));
        return acceptanceInfoResp;
    }

    @Override
    public Result<String> exportAcceptList(QueryAcceptDTO req) {
        OpInfo opInfo = userUtil.getDeptOpInfoWithThrow();
        req.setOperatorInfo(opInfo);
        LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();
        ExportSpecification specification = AcceptOrderExportProcessor.class.getAnnotation(ExportSpecification.class);
        ExportDataParams params = ExportDataParams.builder()
                .taskCode(specification.code())
                .app(SpringContextUtil.getApplicationName())
                .query(req)
                .tenant(TenantContext.get())
                .bizUserName(Optional.ofNullable(loginUser.getName()).orElse("SYSTEM"))
                .bizUserId(Optional.ofNullable(loginUser.getUid()).map(Object::toString).orElse("1"))
                .build();
        AgeiTaskClient.exportData(params);
        return new Result<>(specification.code());
    }

    @Override
    public Result<String> exportRefundAcceptList(QueryAcceptDTO req) {
        OpInfo opInfo = userUtil.getOpInfoWithThrow();
        ExportSpecification specification = AcceptOrderDetailExportProcessor.class.getAnnotation(ExportSpecification.class);
        ExportDataParams params = ExportDataParams.builder()
                .taskCode(specification.code())
                .app(SpringContextUtil.getApplicationName())
                .query(req)
                .tenant(TenantContext.get())
                .bizUserName(Optional.ofNullable(opInfo.getOperatorName()).orElse("SYSTEM"))
                .bizUserId(Optional.ofNullable(opInfo.getOperatorCode()).map(Object::toString).orElse("1"))
                .build();
        AgeiTaskClient.exportData(params);
        return new Result<>(specification.code());
    }

    @Override
    public Result<String> exportAcceptListDetail(QueryAcceptDetailReq req) {
        LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();
        ExportSpecification specification = AcceptOrderDetailExportProcessor.class.getAnnotation(ExportSpecification.class);
        ExportDataParams params = ExportDataParams.builder()
                .taskCode(specification.code())
                .app(SpringContextUtil.getApplicationName())
                .query(req)
                .tenant(TenantContext.get())
                .bizUserName(Optional.ofNullable(loginUser.getName()).orElse("SYSTEM"))
                .bizUserId(Optional.ofNullable(loginUser.getUid()).map(Object::toString).orElse("1"))
                .build();
        AgeiTaskClient.exportData(params);
        return new Result<>(specification.code());
    }

    @Override
    public Result<String> exportRefundAcceptListDetail(QueryAcceptDTO req) {
        LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();
        ExportSpecification specification = AcceptOrderDetailExportProcessor.class.getAnnotation(ExportSpecification.class);
        ExportDataParams params = ExportDataParams.builder()
                .taskCode(specification.code())
                .app(SpringContextUtil.getApplicationName())
                .query(req)
                .tenant(TenantContext.get())
                .bizUserName(Optional.ofNullable(loginUser.getName()).orElse("SYSTEM"))
                .bizUserId(Optional.ofNullable(loginUser.getUid()).map(Object::toString).orElse("1"))
                .build();
        AgeiTaskClient.exportData(params);
        return new Result<>(specification.code());
    }


    private void createAcceptBillCheck(AcceptBillDTO acceptBillDTO, Map<String, GoodsQueryResp.GoodsInfo> goodsInfoMap) {
        // 校验商品数量异常阀值&商品金额异常阀值
        boolean isNORMAL = PmsBillDirectionEnum.NORMAL.getCode().equals(acceptBillDTO.getBillDirection());

        //2.金额异常阀值 数量异常阀值
        BigDecimal maxAmount = PmsBillDirectionEnum.NORMAL.getCode().equals(acceptBillDTO.getBillDirection())
                ? supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(PMSSystemParamEnum.PA_MAX_AMOUNT, acceptBillDTO.getDeptCode())
                : supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(PMSSystemParamEnum.ARM_MAX_AMOUNT, acceptBillDTO.getDeptCode());
        BigDecimal maxMoney = PmsBillDirectionEnum.NORMAL.getCode().equals(acceptBillDTO.getBillDirection())
                ? supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(PMSSystemParamEnum.PA_MAX_MONEY, acceptBillDTO.getDeptCode())
                : supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(PMSSystemParamEnum.ARM_MAX_MONEY, acceptBillDTO.getDeptCode());
        PmsPurchaseOrderPO pmsPurchaseOrderPO = null;
        if (StringUtils.isNotBlank(acceptBillDTO.getPurchBillNo())) {
            //校验当前订货/退货申请是否已过期
            LambdaQueryWrapper<PmsPurchaseOrderPO> query = new LambdaQueryWrapper<>();
            query.eq(PmsPurchaseOrderPO::getBillNo, acceptBillDTO.getPurchBillNo());
            pmsPurchaseOrderPO = pmsPurchaseOrderRepositoryService.getBaseMapper().selectOne(query);
            Assert.isNotNull(pmsPurchaseOrderPO, PmsErrorCodeEnum.SC_PMS_004_P004);
            Assert.isTrue(!(PmsSendModEnum.TOCUSTOMER.getCode().equals(pmsPurchaseOrderPO.getSendMode())
                            && !PmsBillSourceEnum.WHOLESALE.getBillSource().equals(pmsPurchaseOrderPO.getBillSource())),
                    PmsErrorCodeEnum.SC_PMS_004_P005);

            LambdaQueryWrapper<PmsAcceptBillPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PmsAcceptBillPO::getBillNo, acceptBillDTO.getBillNo());
            wrapper.in(PmsAcceptBillPO::getStatus, Lists.newArrayList(AcceptStateEnum.DRAFT.getBillStateCode(), AcceptStateEnum.PENDING_AUDIT.getBillStateCode()));
            List<PmsAcceptBillPO> acceptBills = pmsAcceptBillRepositoryService.getBaseMapper().selectList(wrapper);

            if (!CollectionUtils.isEmpty(acceptBills) && acceptBills.size() == 1
                    && !acceptBillDTO.getBillNo().equals(acceptBills.get(0).getBillNo())) {
                if (PmsBillDirectionEnum.REVERSE.getCode().equals(pmsPurchaseOrderPO.getBillDirection())) {
                    BizExceptions.throwWithMsg("已被采购退货单:[" + acceptBills.stream()
                            .map(PmsAcceptBillPO::getBillNo).collect(Collectors.joining(",")) + "]调入，不允许再次调入");
                } else {
                    BizExceptions.throwWithMsg("已被采购验收单:[" + acceptBills.stream()
                            .map(PmsAcceptBillPO::getBillNo).collect(Collectors.joining(",")) + "]调入，不允许再次调入");
                }
            }
        }
        //校验验收单明细
        List<AcceptBillDetailDTO> acceptDetails = acceptBillDTO.getAcceptDetails();
        List<String> workStateCodes = new ArrayList<>();
        List<String> circulationModeCodes = new ArrayList<>();
        for (AcceptBillDetailDTO acceptBillDetail : acceptDetails) {
            Assert.isTrue(acceptBillDetail.getAcceptQty().compareTo(maxAmount) <= 0, PmsErrorCodeEnum.SC_PMS_004_P006);
            Assert.isTrue(acceptBillDetail.getPurchTaxMoney().compareTo(maxMoney) <= 0, PmsErrorCodeEnum.SC_PMS_004_P007);
            workStateCodes.add(acceptBillDetail.getWorkStatusCode());
            circulationModeCodes.add(acceptBillDetail.getCirculationModeCode());
        }
        // 批量查询流转途径 经营状态
        ManageAndCirculationReq circulationReq = ManageAndCirculationReq.builder()
                .workStateCodes(workStateCodes)
                .circulationModeCodes(circulationModeCodes)
                .build();
        ManageAndCirculationResp manageAndCirculation = commonGoodsService.getManageAndCircul(circulationReq);
        if (manageAndCirculation != null) {
            Map<String, ManageAndCirculationResp.CirculationMode> circulationModeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(manageAndCirculation.getCirculationList())) {
                circulationModeMap = manageAndCirculation.getCirculationList().stream()
                        .collect(Collectors.toMap(ManageAndCirculationResp.CirculationMode::getCirculationWayNo, Function.identity()));
            }
            Map<String, ManageAndCirculationResp.WorkState> workStateMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(manageAndCirculation.getManageStateList())) {
                workStateMap = manageAndCirculation.getManageStateList().stream()
                        .collect(Collectors.toMap(ManageAndCirculationResp.WorkState::getManagementStateNo, Function.identity()));
            }
            Map<String, ManageAndCirculationResp.WorkState> finalWorkStateMap = workStateMap;
            Map<String, ManageAndCirculationResp.CirculationMode> finalCirculationModeMap = circulationModeMap;
            acceptDetails = acceptDetails.stream().filter(e -> {
                ManageAndCirculationResp.CirculationMode circulationMode = finalCirculationModeMap.get(e.getCirculationModeCode());
                ManageAndCirculationResp.WorkState workState = finalWorkStateMap.get(e.getWorkStatusCode());
                return (PmsBillDirectionEnum.NORMAL.getCode().equals(acceptBillDTO.getBillDirection()) && (circulationMode == null || YesOrNoEnum.YES.getCode().equals(circulationMode.getAllowFromSupplierFlag())))
                        || (PmsBillDirectionEnum.REVERSE.getCode().equals(acceptBillDTO.getBillDirection()) && (circulationMode == null || YesOrNoEnum.YES.getCode().equals(circulationMode.getAllowBackSupplierFlag())))
                        || (PmsBillDirectionEnum.NORMAL.getCode().equals(acceptBillDTO.getBillDirection()) && (workState == null || YesOrNoEnum.YES.getCode().equals(workState.getAllowPurchaseFlag())))
                        || (PmsBillDirectionEnum.REVERSE.getCode().equals(acceptBillDTO.getBillDirection()) && (workState == null || YesOrNoEnum.YES.getCode().equals(workState.getAllowPurchaseBackFlag())));
            }).collect(Collectors.toList());
            Assert.isTrue(!CollectionUtils.isEmpty(acceptDetails), PmsErrorCodeEnum.SC_PMS_004_P008);
        }
        List<String> skuCodeList = acceptDetails.stream().map(AcceptBillDetailDTO::getSkuCode).collect(Collectors.toList());
        GoodsQueryReq goodsQueryReq = GoodsQueryReq.builder()
                .storeCode(acceptBillDTO.getDeptCode())
                .goodsCodeList(skuCodeList)
                .attributeNameFlag(false)
                .build();
        List<GoodsQueryResp> goodsInfos = commonGoodsService.getGoodsInfo(goodsQueryReq);
        for (GoodsQueryResp goodsQueryResp : goodsInfos) {
            goodsInfoMap.put(goodsQueryResp.getGoodsInfo().getSkuCode(), goodsQueryResp.getGoodsInfo());
        }
        List<AcceptBillDetailDTO> billDetailList = acceptDetails.stream().
                filter(e -> YesOrNoEnum.YES.getCode().equals(goodsInfoMap.get(e.getSkuCode()).getInCatalog()))
                .collect(Collectors.toList());
        Assert.isTrue(!CollectionUtils.isEmpty(billDetailList), PmsErrorCodeEnum.SC_PMS_004_P009);
        if (!PmsBillSourceEnum.WHOLESALE.getBillSource().equals(acceptBillDTO.getBillSource()) && Objects.nonNull(pmsPurchaseOrderPO)) {
            LambdaQueryWrapper<PmsPurchaseBillDetailPO> queryDetail = new LambdaQueryWrapper<PmsPurchaseBillDetailPO>()
                    .eq(PmsPurchaseBillDetailPO::getBillNo, acceptBillDTO.getBillNo());
            List<PmsPurchaseBillDetailPO> pmsPurchaseBillDetails = pmsPurchaseDetailRepositoryService.getBaseMapper().selectList(queryDetail);
            Assert.isTrue(checkAcceptWithApply(pmsPurchaseOrderPO, pmsPurchaseBillDetails, billDetailList), PmsErrorCodeEnum.SC_PMS_004_P010);
        }

        for (AcceptBillDetailDTO acceptBillDetail : billDetailList) {
            if (goodsInfoMap.containsKey(acceptBillDetail.getSkuCode())) {
                GoodsQueryResp.GoodsInfo goodsInfo = goodsInfoMap.get(acceptBillDetail.getSkuCode());
                acceptBillDetail.setOutputTaxRate(new BigDecimal(goodsInfo.getOutputTaxRate()));
                acceptBillDetail.setInputTaxRate(new BigDecimal(goodsInfo.getInputTaxRate()));
            }
        }
        acceptBillDTO.setAcceptDetails(billDetailList);
    }

    private boolean checkAcceptWithApply(PmsPurchaseOrderPO stApplyBill, List<PmsPurchaseBillDetailPO> pmsPurchaseBillDetails, List<AcceptBillDetailDTO> acceptBillDetails) {
        if (Objects.isNull(stApplyBill)) {
            return true;
        }
        if (PmsBillSourceEnum.WHOLESALE.getBillSource().equals(stApplyBill.getBillSource())) {
            if (CollectionUtils.isNotEmpty(pmsPurchaseBillDetails) && CollectionUtils.isNotEmpty(acceptBillDetails)) {
                if (pmsPurchaseBillDetails.size() < acceptBillDetails.size()) {
                    return false;
                }
                return acceptBillDetails.stream().allMatch(p -> {
                    PmsPurchaseBillDetailPO pmsPurchaseBillDetail = pmsPurchaseBillDetails.stream()
                            .filter(apply -> apply.getSkuCode().equals(p.getSkuCode())).findFirst().orElse(null);
                    if (Objects.isNull(pmsPurchaseBillDetail)) return false;
                    return pmsPurchaseBillDetail.getPurchQty().compareTo(p.getAcceptQty()) >= 0;
                });
            }
        }
        return true;
    }

    private void dataPersistence(AcceptBillDTO acceptBillDTO, Map<String, GoodsQueryResp.GoodsInfo> goodsInfoMap, Long id) {
        OpInfo operatorInfo = userUtil.getOpInfoWithThrow();

        PmsAcceptBillPO acceptBillPO = acceptBillCopier.convertStAcceptBill(acceptBillDTO);
        acceptBillPO.setId(id);
        acceptBillPO.setPurchBillNo(acceptBillDTO.getPurchBillNo());
        acceptBillPO.setStatus(PmsAcceptBillStateEnum.DRAFT.getBillStateCode().equals(acceptBillDTO.getAcceptStatus()) ? AcceptStateEnum.DRAFT.getBillStateCode() : AcceptStateEnum.PENDING_AUDIT.getBillStateCode());
        if (acceptBillDTO.getAcceptStatus().equals(PmsAcceptBillStateEnum.PENDING_AUDIT.getBillStateCode())
                || acceptBillDTO.getAcceptStatus().equals(PmsAcceptBillStateEnum.AUDIT.getBillStateCode())) {
            acceptBillPO.setSubmitTime(LocalDateTime.now());
            acceptBillPO.setSubmitManCode(operatorInfo.getOperatorCode());
            acceptBillPO.setSubmitManName(operatorInfo.getOperatorName());
        }
        //2.落明细表
        List<PmsAcceptBillDetailPO> detailList = new ArrayList<>();
        StoreDetailResp singleDeptInfo = baseDataSystemFeignClient.getSingleDeptInfo(acceptBillDTO.getDeptCode());
        if (singleDeptInfo != null) {
            //没有部门名称的补充
            if (StringUtils.isEmpty(acceptBillDTO.getDeptName())) {
                acceptBillDTO.setDeptName(singleDeptInfo.getName());
                acceptBillPO.setDeptName(singleDeptInfo.getName());
            }
            // 设置 购进类型
            convertPurchaseType(acceptBillPO, singleDeptInfo);
        }
        AtomicLong insideId = new AtomicLong(1L);
        List<PmsBillBatchInfoPO> billBatchInfoList = new ArrayList<>();
        List<PmsAcceptBillDetailPO> acceptBillDetailList = acceptBillDTO.getAcceptDetails().stream().map(e -> {
            GoodsQueryResp.GoodsInfo goodsInfo = goodsInfoMap.get(e.getSkuCode());
            return convertAcceptBillDetail(acceptBillDTO,e,goodsInfo,insideId.getAndIncrement());
        }).collect(Collectors.toList());
        Logs.info("保存验收单明细:{}", JSON.toJSONString(acceptBillDetailList));
        BigDecimal totalMoney = acceptBillDetailList.stream().map(PmsAcceptBillDetailPO::getPurchTaxMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalQty = acceptBillDetailList.stream().map(PmsAcceptBillDetailPO::getAcceptQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        acceptBillPO.setTotalQty(totalQty);
        acceptBillPO.setTotalMoney(totalMoney);
        Logs.info("保存验收单:{}", JSON.toJSONString(acceptBillPO));
        pmsAcceptBillRepositoryService.updateById(acceptBillPO);
        pmsAcceptBillDetailRepositoryService.saveBatch(acceptBillDetailList);
    }


    /**
     * 转换验收单明细
     * @param acceptBillDTO
     * @param detailDTO
     * @param goodsInfo
     * @param insideId
     * @return
     */
    private PmsAcceptBillDetailPO convertAcceptBillDetail(AcceptBillDTO acceptBillDTO,AcceptBillDetailDTO detailDTO,GoodsQueryResp.GoodsInfo goodsInfo,Long insideId) {
        PmsAcceptBillDetailPO detail = acceptBillCopier.convertBillDetailDtoToPo(detailDTO);
        detail.setDeptCode(acceptBillDTO.getDeptCode());
        detail.setDeptName(acceptBillDTO.getDeptName());
        detail.setBillNo(acceptBillDTO.getBillNo());
        detail.setInsideId(insideId);
        detail.setPurchMoney(detailDTO.getPurchMoney());
        detail.setPurchTaxMoney(detailDTO.getPurchTaxMoney());
        detail.setPurchTax(detailDTO.getPurchTax());
        detail.setUomAttr(goodsInfo.getMesureProperty());
        detail.setBillDirection(acceptBillDTO.getBillDirection());
        detail.setSupplierCode(acceptBillDTO.getSupplierCode());
        detail.setSupplierName(acceptBillDTO.getSupplierName());
        detail.setBillType(acceptBillDTO.getBillType());
        detail.setSaleMode(goodsInfo.getOperationModel());
        detail.setSkuModel(goodsInfo.getModel());
        detail.setWorkStatusCode(goodsInfo.getWorkStateCode());
        detail.setWorkStatusName(goodsInfo.getWorkStateName());
        detail.setParseExtData(AcceptBillGoodsExtra.builder()
                .oldPurchPrice(detailDTO.getOldPurchPrice())
                .changePriceFlag(detailDTO.isChangePriceFlag())
                .build());

        return detail;
    }

    /**
     * 提交/审核时，判断当前门店核算单位和供应商的结算模式
     *
     * @param stAcceptBill
     * @param singleDeptInfo 门店信息
     */
    public void convertPurchaseType(PmsAcceptBillPO stAcceptBill, StoreDetailResp singleDeptInfo) {
        String accountCode = singleDeptInfo.getAccountCode();
        String supplierCode = stAcceptBill.getSupplierCode();
        MstSupplierVO shipperInfo = commonSupplierService.queryByCode(supplierCode);
        if (Objects.isNull(shipperInfo)) {
            return;
        }
        SettleModeEnum settleModeEnum = SettleModeEnum.getByCode(shipperInfo.getSettleMode());
        if (Objects.isNull(settleModeEnum)) {
            return;
        }
        List<AccountBodyVo> accountBodyList = shipperInfo.getAccountBodyList();
        String shipperAccountCode = "";
        switch (settleModeEnum) {
            case LOCAL_ACCOUNT:
                shipperAccountCode = accountCode;
                break;
            case COMMON_ACCOUNT:
                if (CollectionUtils.isNotEmpty(accountBodyList)) {
                    shipperAccountCode = accountBodyList.get(0).getAccountCode();
                }
                break;
            case AREA_ACCOUNT:
                shipperAccountCode = findAreaAccountCode(stAcceptBill, accountBodyList);
                break;
            default:
                return;
        }
        // 统一设置采购类型和核算编码
        setPurchaseTypeAndAccountCode(stAcceptBill, accountCode, shipperAccountCode);
    }

    /**
     * 根据门店所属区域查找供应商对应的区域核算单位编码
     */
    private String findAreaAccountCode(PmsAcceptBillPO stAcceptBill, List<AccountBodyVo> accountBodyList) {
        QueryUpDeptListReq queryUpDeptListReq = new QueryUpDeptListReq();
        queryUpDeptListReq.setDeptCode(stAcceptBill.getDeptCode());
        queryUpDeptListReq.setClassCode(GroupDeptEnum.AREA_DEPT_GROUP.getCode());
        queryUpDeptListReq.setOpenStatus(1);
        QueryUpDeptListResp resp = baseDataSystemFeignClient.queryUpDeptList(queryUpDeptListReq);
        if (Objects.isNull(resp) || CollectionUtils.isEmpty(resp.getRows()) || resp.getRows().size() != 1) {
            return "";
        }
        String areaGroupCode = resp.getRows().get(0).getCode();
        if (CollectionUtils.isEmpty(accountBodyList)) {
            return "";
        }
        return accountBodyList.stream()
                .filter(item -> CollectionUtils.isNotEmpty(item.getGroupCodeList()) &&
                        item.getGroupCodeList().contains(areaGroupCode))
                .map(AccountBodyVo::getAccountCode)
                .findFirst()
                .orElse("");
    }

    /**
     * 设置采购类型和核算单位编码
     */
    private void setPurchaseTypeAndAccountCode(PmsAcceptBillPO stAcceptBill, String accountCode, String shipperAccountCode) {
        if (StringUtils.isBlank(shipperAccountCode)) {
            return;
        }
        if (shipperAccountCode.equals(accountCode)) {
            stAcceptBill.setPurchType(CommonDpgzTypeEnum.DPGZ_0.getCode());
            stAcceptBill.setAccCode(accountCode);
            Logs.info("验收单号 {} 使用本地配送中心 {}", stAcceptBill.getPurchBillNo(), accountCode);
        } else {
            stAcceptBill.setPurchType(CommonDpgzTypeEnum.DPGZ_1.getCode());
            stAcceptBill.setAccCode(shipperAccountCode);
            Logs.info("验收单号 {} 使用跨区域配送中心 {}", stAcceptBill.getPurchBillNo(), shipperAccountCode);
        }
    }
    /**
     * 处理 原料或者成品的价格 数量
     *
     * @param autoBuildTscReq
     */
    private void setAutoAscGoodsPriceInfo(AutoBuildTscReq autoBuildTscReq) {
        final PmsAcceptBillDetailPO acceptBillDetail = autoBuildTscReq.getAcceptBillDetail();
        final TranscodingBillDetail.TranscodingBillDetailBuilder builder = autoBuildTscReq.getBuilder();
        final GoodsQueryResp.GoodsInfo goodsInfo = autoBuildTscReq.getGoodsInfo();
        final BigDecimal scaleFactor = autoBuildTscReq.getScaleFactor();
        final Boolean rmFlag = autoBuildTscReq.getRmFlag();
        final Boolean refundFlag = autoBuildTscReq.getRefundFlag();
        BigDecimal inputTaxRate = MoneyUtil.int2BigDecimal(goodsInfo.getInputTaxRate());
        if (rmFlag) {
            BigDecimal referPrice = acceptBillDetail.getSalePrice();
            BigDecimal purchasePrice = acceptBillDetail.getPurchPrice();
            BigDecimal amountBig = acceptBillDetail.getAcceptQty();
            if (refundFlag) {
                // 退货 时原料 是策略中的 成品 需要 * 比例
                amountBig = amountBig.multiply(scaleFactor);
                referPrice = MoneyUtil.long2BigDecimal(goodsInfo.getReferPrice());
                purchasePrice = acceptBillDetail.getPurchPrice();
            }
            BigDecimal amount = MoneyUtil.longMilliToYuan(amountBig.longValue());
            BigDecimal taxMoneyTotal = MoneyUtil.getTaxMoneyTotal(purchasePrice, inputTaxRate, amount);
            builder.rmSaleMod(goodsInfo.getOperationModel())
                    .rmAmount(amountBig.longValue())
                    .rmCostPrice(purchasePrice.longValue())
                    .rmCostMoney(amount.multiply(purchasePrice).longValue())
                    .rmCostTax(taxMoneyTotal.longValue())
                    .rmSalePrice(referPrice.longValue())
                    .rmSaleMoney(referPrice.multiply(amount).longValue());
        } else {
            BigDecimal referPrice = MoneyUtil.long2BigDecimal(goodsInfo.getReferPrice());
            BigDecimal purchasePrice = MoneyUtil.long2BigDecimal(goodsInfo.getPurchasePrice());
            BigDecimal amountBig = acceptBillDetail.getAcceptQty();
            if (!refundFlag) {
                // 只有非退货 时原料 是策略中的 原料 需要 * 比例  退货时不需要
                amountBig = amountBig.multiply(scaleFactor);
            } else {
                referPrice = acceptBillDetail.getSalePrice();
                purchasePrice = acceptBillDetail.getPurchPrice();
            }
            BigDecimal amount = MoneyUtil.longMilliToYuan(amountBig.longValue());
            BigDecimal taxMoneyTotal = MoneyUtil.getTaxMoneyTotal(purchasePrice, inputTaxRate, amount);
            builder.fpSaleMod(goodsInfo.getOperationModel())
                    .fpAmount(amountBig.longValue())
                    .fpCostPrice(purchasePrice.longValue())
                    .fpCostMoney(amount.multiply(purchasePrice).longValue())
                    .fpCostTax(taxMoneyTotal.longValue())
                    .fpSalePrice(referPrice.longValue())
                    .fpSaleMoney(referPrice.multiply(amount).longValue());
        }

    }

    /**
     * 调用代配过账
     * @param acceptBillPO 验收单
     * @param detailList 验收单明细
     */
    private void proxyPost(PmsAcceptBillPO acceptBillPO, List<PmsAcceptBillDetailPO> detailList){
        if(!CommonDpgzTypeEnum.DPGZ_1.getCode().equals(acceptBillPO.getPurchType())){
            Logs.info("非代配单据 {} 不需要代配过账", acceptBillPO.getBillNo());
            return;
        }
        PmsAccountEnum accountBillDirection = getAccountBillDirection(acceptBillPO);
        SupplierByCodeResp supplierCache = baseStoreUtil.getSupplierCache(acceptBillPO.getSupplierCode());
        Assert.isNotNull(supplierCache, PmsErrorCodeEnum.SC_PMS_008_B002);
        ComplexSupplierInfo.AccountBodyList accountBody = supplierCache.getAccountBodyList().stream().findFirst().get();

        //如果商品类型=联营管库存，验收数量>0但是验收金额=0的，不做代配过账
        List<PmsAcceptBillDetailPO> billDetailList = detailList.stream().map(goods->{
            if(GoodsSaleModeEnum.JOINT_OPERATION_WITH_STOCK.getCode().equals(goods.getSkuType())
                    && goods.getAcceptQty().compareTo(BigDecimal.ZERO)>0 && goods.getPurchTaxMoney().compareTo(BigDecimal.ZERO)==0){
                return null;
            }
            return goods;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isNotEmpty(billDetailList), PmsErrorCodeEnum.SC_PMS_004_P022);
        AccountBillCreateReq req = AccountBillCreateReq.builder()
                .srcBillNo(acceptBillPO.getBillNo())
                .dcCode(StringUtils.isNotBlank(accountBody.getDeptCode())?accountBody.getDeptCode():acceptBillPO.getDeptCode())
                .dcName(StringUtils.isNotBlank(accountBody.getDeptName())?accountBody.getDeptName():acceptBillPO.getDeptName())
                .dcAccCode(accountBody.getAccountCode())
                .billType(PmsAccountEnum.ACCOUNT_BILL_TYPE_YS.getCode())
                .billDirection(accountBillDirection.getCode())
                .supplierCode(acceptBillPO.getSupplierCode())
                .supplierName(acceptBillPO.getSupplierName())
                .contractNo(acceptBillPO.getContractNo())
                .repairSignId(acceptBillPO.getRepairSignId())
                .accDeptCode(acceptBillPO.getDeptCode())
                .accDeptName(acceptBillPO.getDeptName())
                .detailList(detailList.stream().map(this::buildAccountBillDetail).collect(Collectors.toList()))
                .build();
        Logs.info("代配单 {} 创建代配过账单 {}", acceptBillPO.getBillNo(), JSON.toJSONString(req));
        Result<AccountBillResp> accountBill = pmsAccountApplicationService.createAccountBill(req);
        Logs.info("代配单 {} 创建代配过账单 结果 {}", acceptBillPO.getBillNo(), JSON.toJSONString(accountBill));
    }

    /**
     * 获取过账单据方向
     * @param acceptBillPO
     * @return
     */
    private PmsAccountEnum getAccountBillDirection(PmsAcceptBillPO acceptBillPO) {
        boolean isNormalDirection = PmsBillDirectionEnum.NORMAL.getCode().equals(acceptBillPO.getBillDirection());
        boolean isReversalBill = YesOrNoEnum.YES.getCode().equals(acceptBillPO.getReversalBillSign());
        if (isNormalDirection) {
            return isReversalBill ? PmsAccountEnum.ACCOUNT_BILL_DIRECT_PAR : PmsAccountEnum.ACCOUNT_BILL_DIRECT_PA;
        } else {
            return isReversalBill ? PmsAccountEnum.ACCOUNT_BILL_DIRECT_PRR : PmsAccountEnum.ACCOUNT_BILL_DIRECT_PR;
        }
    }
    private AccountBillDetailCreateReq buildAccountBillDetail(PmsAcceptBillDetailPO acceptBillDetailPO){
        AccountBillDetailCreateReq req = new AccountBillDetailCreateReq();
            BeanUtil.copyProperties(acceptBillDetailPO,req);
            req.setPackageUnit(acceptBillDetailPO.getPackageUnitName());
            req.setSrcBillNo(acceptBillDetailPO.getBillNo());
            req.setSrcInsideId(acceptBillDetailPO.getInsideId());
            req.setAccQty(acceptBillDetailPO.getAcceptQty());
            req.setAccPrice(acceptBillDetailPO.getPurchPrice());
            req.setAccMoney(acceptBillDetailPO.getPurchTaxMoney());
            req.setAccTax(acceptBillDetailPO.getPurchTax());
            req.setSalePrice(acceptBillDetailPO.getSalePrice());
            req.setSaleMoney(acceptBillDetailPO.getSaleMoney());
            req.setSrcInsideId(acceptBillDetailPO.getSrcInsideId());
        return req;
    }

    /**
     * 调整加盟额度
     * @param acceptBill 验收单
     * @param acceptBillDetailList 验收单明细
     */
    private void adjustJiaMeng(PmsAcceptBillPO acceptBill, List<PmsAcceptBillDetailPO> acceptBillDetailList,Long rollback){
        //获取供应商信息
        SupplierByCodeResp supplier = baseStoreUtil.getSupplierCache(acceptBill.getSupplierCode());
        //是否加盟
        boolean isJiaMeng = DeptOperateModeEnum.JM.getCode().equals(acceptBill.getDeptOperateMode())
                && Objects.nonNull(supplier)
                && !SettleModeEnum.SELF_ACCOUNT.getCode().equals(supplier.getSettleMode());
        //非加盟 不处理
        if(!isJiaMeng){
            return;
        }
        //获取加盟调整操作枚举类
        FranLineTypeEnum franLineType = getFranLineType(acceptBill);
        //组装调整加盟额度入参
        FranLineAdjustReq adjustReq = FranLineAdjustReq.builder()
                .timeStamp(System.currentTimeMillis())
                .createCode(acceptBill.getCreateCode())
                .createName(acceptBill.getCreateName())
                .type(franLineType.getCode())
                .billNumber(acceptBill.getBillNo())
                .amount(acceptBill.getTotalMoney().doubleValue())
                .storeCode(acceptBill.getDeptCode())
                .rollBack(rollback)
                .build();
        if(PmsBillDirectionEnum.NORMAL.getCode().equals(acceptBill.getBillDirection())){
            List<FranLineAdjustReq.OrderNumber> orderNumberList = acceptBillDetailList.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getPurchBillNo()))
                    .map(item ->{
                FranLineAdjustReq.OrderNumber orderNumber = FranLineAdjustReq.OrderNumber.builder()
                        .orderNumber(item.getPurchBillNo())
                        .amount(item.getPurchTaxMoney().doubleValue()).build();
                return orderNumber;
            }).collect(Collectors.toList());
            adjustReq.setOrderNumberList(orderNumberList);
        }
        commonFranchiseService.adjustFranLine(adjustReq);
    }

    /**
     * 获取 fraLineType
     * @param acceptBill
     * @param acceptBillDetail
     * @return
     */
    private FranLineTypeEnum getFranLineType(PmsAcceptBillPO acceptBill) {
        //是否冲红单
        boolean isReversal = YesOrNoEnum.YES.getCode().equals(acceptBill.getReversalBillSign());
        //是否正向单
        boolean isNormalDirection = PmsBillDirectionEnum.NORMAL.getCode().equals(acceptBill.getBillDirection());
        //是否有采购单号
        boolean hasPurchaseBillNo = StringUtils.isNotBlank(acceptBill.getPurchBillNo());
        if (isReversal) {
            return isNormalDirection ? FranLineTypeEnum.PURCH_ACCEPT_REVERSAL : FranLineTypeEnum.PURCH_RETURN_REVERSAL;
        }
        if (hasPurchaseBillNo) {
            return isNormalDirection ? FranLineTypeEnum.PURCH_SHIP_WITH_APPLY : FranLineTypeEnum.PURCH_RETURN_WITH_APPLY;
        } else {
            return isNormalDirection ? FranLineTypeEnum.PURCH_ACCEPT : FranLineTypeEnum.PURCH_RETURN;
        }
    }

}
