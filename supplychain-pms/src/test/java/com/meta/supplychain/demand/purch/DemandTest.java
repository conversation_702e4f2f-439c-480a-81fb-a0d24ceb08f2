package com.meta.supplychain.demand.purch;

import cn.linkkids.framework.croods.common.context.ThreadLocals;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.ageiport.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.meta.supplychain.common.component.service.impl.commonbiz.XxlJobService;
import com.meta.supplychain.common.component.service.intf.ISupplychainPmsBizRuleEngineService;
import com.meta.supplychain.demand.purch.application.intf.IPmsApplicationManagerService;
import com.meta.supplychain.demand.purch.domain.intf.PmsDemandDomainService;
import com.meta.supplychain.demand.purch.domain.intf.PmsOrderApplyDomainService;
import com.meta.supplychain.entity.dto.md.component.goodsrule.DemandBatchRecordGoodsDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GeneratePurchBatchDTO;
import com.meta.supplychain.entity.dto.pms.demand.AutoApplyToDemandDTO;
import com.meta.supplychain.entity.dto.pms.demand.DemandCheckApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.req.billconvert.BillConvertDataDTO;
import com.meta.supplychain.entity.dto.pms.req.demand.*;
import com.meta.supplychain.entity.dto.pms.resp.demand.PmsDemandBillResultResp;
import com.meta.supplychain.entity.dto.pms.resp.demand.PmsDemandPruchDeliveryRefResp;
import com.meta.supplychain.entity.dto.xxjob.XxlJobReq;
import com.meta.supplychain.entity.po.pms.*;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import com.meta.supplychain.enums.OrderAttributeEnum;
import com.meta.supplychain.enums.pms.PmsErrorCodeEnum;
import com.meta.supplychain.enums.pms.ShippingWayEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.*;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillDetailRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillRepository;
import com.metadata.idaas.client.model.LoginUserDTO;
import com.metadata.idaas.client.util.ClientIdentUtil;
import org.apache.commons.lang.ObjectUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description:
 * @date 2024年08月26日 10:54
 */
@SpringBootTest
public class DemandTest {
    @Autowired
    private IPmsDemandDetailSourceRefRepositoryService pmsDemandDetailSourceRefRepositoryService;

    @Autowired
    private PmsDemandDomainService pmsDemandDomainService;

    @Autowired
    private PmsApplyBillRepositoryService pmsApplyBillRepositoryService;

    @Autowired
    private PmsApplyBillDetailRepositoryService pmsApplyBillDetailRepositoryService;

    @Autowired
    private XxlJobService xxlJobService;

    @Autowired
    private PmsOrderApplyDomainService pmsOrderApplyDomainService;

    @Autowired
    private ISupplychainPmsBizRuleEngineService iSupplychainPmsBizRuleEngineService;

    @Autowired
    private IPmsApplicationManagerService pmsApplicationManagerService;

    @Autowired
    private IPmsDemandPruchDeliveryRefRepositoryService pmsDemandPruchDeliveryRefRepositoryService;

    @Autowired
    private IWdDeliveryBillDetailRepository wdDeliveryBillDetailRepository;

    @Autowired
    private IWdDeliveryBillRepository wdDeliveryBillRepository;

    @Autowired
    private PmsPurchaseOrderRepositoryService pmsPurchaseRepositoryService;

    @Autowired
    private PmsPurchaseDetailRepositoryService pmsPurchaseDetailRepositoryService;



    @Test
    public void testCheckApplyBill(){
        TenantContext.set("153658");
        DemandCheckApplyBillDTO demandCheckApplyBillDTO = new DemandCheckApplyBillDTO();
        demandCheckApplyBillDTO.setDemandBillNo("123");
        List<DemandCheckApplyBillDTO.ApplyInfo> applyInfoList = new ArrayList<>();
        for(int i =0;i < 10;i++){
            DemandCheckApplyBillDTO.ApplyInfo applyInfo = new DemandCheckApplyBillDTO.ApplyInfo();
            applyInfo.setApplyBillNo("111");
            applyInfo.setSrcInsideId((long) i);
            applyInfoList.add(applyInfo);
        }

        demandCheckApplyBillDTO.setApplyInfoList(applyInfoList);

        List<PmsDemandDetailSourceRefPO> pmsDemandDetailSourceRefPOS = pmsDemandDetailSourceRefRepositoryService.getPmsDemandDetailSourceRefMapper().checkApplyBill(demandCheckApplyBillDTO);
        System.out.println("testCheckApplyBill:" + JSON.toJSONString(pmsDemandDetailSourceRefPOS));
    }

    @Test
    public void tet30(){
        PmsErrorCodeEnum[] values = PmsErrorCodeEnum.values();
        for (PmsErrorCodeEnum value : values) {
            System.out.println(value.getCode() + "," + value.getDesc());
        }
    }

    @Test
    public void testHanlerAutoApplyToDemand(){
        System.out.println("testHanlerAutoApplyToDemand.start");
        TenantContext.set("153658");

        AutoApplyToDemandDTO autoApplyToDemandDTO = new AutoApplyToDemandDTO();
//        try (BufferedReader reader = new BufferedReader(new FileReader("F:\\json.txt"))) {
//            StringBuilder builder = new StringBuilder();
//            String line;
//            String s = reader.readLine();
//            while (s != null ){
//                builder.append(s);
//                s = reader.readLine();
//            }
//            autoApplyToDemandDTO = JSONObject.parseObject(builder.toString(),AutoApplyToDemandDTO.class);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

        PmsApplyBillPO pmsApplyBillPO = pmsApplyBillRepositoryService.getByBillNo("OR250616000007");
        List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = pmsApplyBillDetailRepositoryService.queryListByBillNo("OR250616000007");
        autoApplyToDemandDTO.getApplyBillList().add(pmsApplyBillPO);
        autoApplyToDemandDTO.getApplyBillDetailList().addAll(pmsApplyBillDetailPOS);

        Map<String,PmsApplyBillPO> applyBillMap = new HashMap<>();
        applyBillMap.put("OR250616000007",pmsApplyBillPO);
        autoApplyToDemandDTO.setApplyBillMap(applyBillMap);

        pmsDemandDomainService.hanlerAutoApplyToDemand(autoApplyToDemandDTO);
        System.out.println("testHanlerAutoApplyToDemand.end");
    }

    @Test
    public void createFormData(){
        TenantContext.set("153658");
        XxlJobReq xxlJob = new XxlJobReq();
        xxlJob.setJobDesc("任务描述");
        xxlJob.setAuthor("system");
        JSONObject json = new JSONObject();
        json.put("tenantId",TenantContext.get());
        json.put("purchBatchNo","purchBatchNo");
        json.put("deptCode","deptCode");

        xxlJob.setExecutorParam(json.toJSONString());//执行器，任务参数

        xxlJob.setStartTime(LocalDateTime.now());//计划时间
        xxlJob.setMin(5);//计划时间前 min分钟后执行
        xxlJob.setExecutorHandler("execDemandJob");//JobHandler
        xxlJob.setIsStart(true);
        xxlJobService.addJob(xxlJob);
    }

    @Test
    public void testApply(){
        TenantContext.set("153658");

        String billNo = "OR250609000015";

        LocalDateTime now = LocalDateTime.now();
        PmsApplyBillPO applyBillPO = pmsApplyBillRepositoryService.getByBillNo(billNo);
        List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = pmsApplyBillDetailRepositoryService.queryListByBillNo(billNo);
        List<DemandBatchRecordGoodsDTO> demandBatchRecordGoodsDTOS = pmsApplyBillDetailPOS.stream().filter(e -> ObjectUtils.equals(ShippingWayEnum.DELIVERY.getCode(), e.getShippingWay())).map(e ->
                DemandBatchRecordGoodsDTO.builder()
                        .categoryCode(e.getCategoryCode())
                        .skuCode(e.getSkuCode())
                        .deptName(e.getShipperName())
                        .deptCode(e.getShipperCode())
                        .build()
        ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(demandBatchRecordGoodsDTOS)) {
            GeneratePurchBatchDTO generatePurchBatch = GeneratePurchBatchDTO.builder()
                    .date(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()))
                    .deliverDays((int) ChronoUnit.DAYS.between(applyBillPO.getCreateTime().toLocalDate(), applyBillPO.getDeliverDate()))
                    .demandBatchRecordGoodsList(demandBatchRecordGoodsDTOS)
                    .build();
            if (!OrderAttributeEnum.isZJ(applyBillPO.getAttributeCode())) {
                iSupplychainPmsBizRuleEngineService.generateProcurementBatch(generatePurchBatch);
            }
        }
    }

    @Test
    public void testException(){
        BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_008_B005, new String[]{"123"});
    }

    @Test
    public void taskApplyToDemand(){
        //{"tenantId":"153658","deptCode":"001426","purchBatchNo":"20250611011433"}
        TenantContext.set("153658");

        TaskApplyToDemandReq taskApplyToDemandReq = new TaskApplyToDemandReq();
        taskApplyToDemandReq.setDeptCode("001426");
        taskApplyToDemandReq.setPurchBatchNo("20250611011433");

        pmsApplicationManagerService.getPmsDemandApplicationService().taskApplyToDemand(taskApplyToDemandReq);
    }

    @Test
    public void listPmsDemandPruchDeliveryByDemandBillNo(){
        //{"tenantId":"153658","deptCode":"001426","purchBatchNo":"20250611011433"}
        TenantContext.set("153658");
        PmsDemandPruchDeliveryRefReq pmsDemandPruchDeliveryRefReq = new PmsDemandPruchDeliveryRefReq();
        pmsDemandPruchDeliveryRefReq.setBillNo("DMD250620000008");


        List<PmsDemandPruchDeliveryRefResp> pmsDemandPruchDeliveryRefResps = pmsDemandPruchDeliveryRefRepositoryService.listPmsDemandPruchDeliveryByDemandBillNo(pmsDemandPruchDeliveryRefReq);
        System.out.println("listPmsDemandPruchDeliveryByDemandBillNo:" + JSON.toJSONString(pmsDemandPruchDeliveryRefResps));
    }

    @Test
    public void syncApplyDetailState(){
        //{"tenantId":"153658","deptCode":"001426","purchBatchNo":"20250611011433"}
        TenantContext.set("153658");
        LambdaQueryWrapper<PmsApplyBillDetailPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PmsApplyBillDetailPO::getBillNo,"OR250614000017");
        List<PmsApplyBillDetailPO> list = pmsApplyBillDetailRepositoryService.list(queryWrapper);

        pmsOrderApplyDomainService.syncApplyDetailState(list);
    }

    @Test
    public void auditApplyToDemand(){
        //{"tenantId":"153658","deptCode":"001426","purchBatchNo":"20250611011433"}
        TenantContext.set("153658");
        List<String> applyBillNoList = new ArrayList<>();
        applyBillNoList.add("OR250616000007");
        pmsDemandDomainService.auditApplyToDemand(applyBillNoList);
    }

    @Test
    public void cancel(){
        TenantContext.set("153658");
        CancelDemandBillReq param = new CancelDemandBillReq();
        param.setBillNo("DMD250619000011");
        param.setCancelType(2);
        pmsDemandDomainService.cancel(param);
    }

    @Test
    public void autoAuditBill(){
        TenantContext.set("153658");
        LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();
        if(null == loginUser){
            loginUser = new LoginUserDTO();
            loginUser.setUid(0L);
            loginUser.setCode("system");
            loginUser.setName("system");
            ThreadLocals.setValue("_login_user_idaas", loginUser);
        }

        String billNo = "DMD250619000062";

        QueryDemandDetailReq queryDemandDetailReq = new QueryDemandDetailReq();
        queryDemandDetailReq.setBillNo(billNo);
        PmsDemandBillResultResp pmsDemandBillResultResp = pmsDemandDomainService.queryDemandDetail(queryDemandDetailReq);

        LambdaQueryWrapper<WdDeliveryBillPO> deliveryBillQueryWrapper = new LambdaQueryWrapper();
        deliveryBillQueryWrapper.eq(WdDeliveryBillPO::getBillNo,"DO015920250619000038");
        List<WdDeliveryBillPO> deliveryBillPOList = wdDeliveryBillRepository.list(deliveryBillQueryWrapper);

        LambdaQueryWrapper<WdDeliveryBillDetailPO> deliveryBillDetailQueryWrapper = new LambdaQueryWrapper();
        deliveryBillDetailQueryWrapper.eq(WdDeliveryBillDetailPO::getBillNo,"DO015920250619000038");
        List<WdDeliveryBillDetailPO> deliveryBillDetailPOList = wdDeliveryBillDetailRepository.list(deliveryBillDetailQueryWrapper);


//        LambdaQueryWrapper<PmsPurchaseOrderPO> purchaseOrderQueryWrapper = new LambdaQueryWrapper();
//        purchaseOrderQueryWrapper.eq(PmsPurchaseOrderPO::getBillNo,"PO022819250619000050");
//        List<PmsPurchaseOrderPO> purchaseOrderList = pmsPurchaseRepositoryService.list(purchaseOrderQueryWrapper);
//
//        LambdaQueryWrapper<PmsPurchaseBillDetailPO> purchaseOrderDetailQueryWrapper = new LambdaQueryWrapper();
//        purchaseOrderDetailQueryWrapper.eq(PmsPurchaseBillDetailPO::getBillNo,"PO022819250619000050");
//        List<PmsPurchaseBillDetailPO> purchaseOrderDetailList = pmsPurchaseDetailRepositoryService.list(purchaseOrderDetailQueryWrapper);

        BillConvertDataDTO billConvertData = new BillConvertDataDTO();
        billConvertData.setDeliveryBillList(deliveryBillPOList);
        billConvertData.setDeliveryBillDetailList(deliveryBillDetailPOList);
//        billConvertData.setPurchBillList(purchaseOrderList);
//        billConvertData.setPurchBillDetailList(purchaseOrderDetailList);

        LambdaQueryWrapper<PmsDemandPruchDeliveryRefPO> pruchDeliveryRefQueryWrapper = new LambdaQueryWrapper();
        pruchDeliveryRefQueryWrapper.eq(PmsDemandPruchDeliveryRefPO::getBillNo,billNo);
        List<PmsDemandPruchDeliveryRefPO> pruchDeliveryRefList = pmsDemandPruchDeliveryRefRepositoryService.list(pruchDeliveryRefQueryWrapper);
        billConvertData.setPruchDeliveryRefList(pruchDeliveryRefList);


        pmsDemandDomainService.autoAuditBill(pmsDemandBillResultResp,billConvertData);
    }

    @Test
    public void submitDemandBill(){
        TenantContext.set("153658");
        PmsDemandBillReq pmsDemandBillReq = new PmsDemandBillReq();
        pmsDemandBillReq.setBillNo("DMD250624000001");
        pmsDemandBillReq.setOpType(2);
        pmsDemandDomainService.submitDemandBill(pmsDemandBillReq);
    }
}
