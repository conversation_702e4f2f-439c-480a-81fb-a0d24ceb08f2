package com.meta.supplychain.wds.domain.delivery.impl;

import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.meta.supplychain.common.component.service.intf.BillEventServiceFactory;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonFranchiseService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.franline.req.FranLineAdjustReq;
import com.meta.supplychain.entity.dto.franline.resp.FranLineAdjustResp;
import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandPruchDeliveryRefReq;
import com.meta.supplychain.entity.dto.pms.resp.demand.PmsDemandPruchDeliveryRefResp;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchBillOptResp;
import com.meta.supplychain.entity.dto.stock.StkTaskIReleaseExecuteDto;
import com.meta.supplychain.entity.dto.stock.StkTaskItemExecuteDto;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.dto.stock.resp.BatchExecRowVo;
import com.meta.supplychain.entity.dto.stock.resp.BatchRecordResp;
import com.meta.supplychain.entity.po.pms.PmsApplyBillPO;
import com.meta.supplychain.entity.po.wds.ShipBillPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import com.meta.supplychain.enums.*;
import com.meta.supplychain.enums.pms.PmsBillDirectionEnum;
import com.meta.supplychain.enums.wds.*;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsDemandPruchDeliveryRefRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsApplyBillRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillRepository;
import com.meta.supplychain.util.MoneyUtil;
import com.meta.supplychain.util.UserUtil;
import com.meta.supplychain.util.BizExceptionUtil;
import feign.RetryableException;
import feign.codec.DecodeException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.SocketTimeoutException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DeliveryOrderAuditService {

    private final IWdDeliveryBillRepository iWdDeliveryBillRepository;

    private final UserUtil userUtil;

    final ICommonStockService iCommonStockService;

    private final ICommonFranchiseService commonFranchiseService;

    final PmsApplyBillRepositoryService pmsApplyBillRepositoryService;

    private final ISupplychainControlEngineService supplychainControlEngineService;
    private final IPmsDemandPruchDeliveryRefRepositoryService pmsDemandPruchDeliveryRefRepositoryService;


    private final BillEventServiceFactory billEventServiceFactory;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Result<Boolean> doAuditForBatch(OpInfo opInfo,WdDeliveryBillPO wdDeliveryBillPO, List<WdDeliveryBillDetailPO> deliveryBillDetailPOList) {
        // 可以将业务逻辑移到这里或继续调用原 doAudit
        return doAudit(opInfo,wdDeliveryBillPO, deliveryBillDetailPOList);
    }


    public FranLineAdjustReq buildFranLineAdjustReq(WdDeliveryBillPO wdDeliveryBillPO, OpInfo operatorInfo) {
        FranLineAdjustReq adjustReq = FranLineAdjustReq.builder()
                .timeStamp(System.currentTimeMillis())
                .createCode(operatorInfo.getOperatorCode())
                .createName(operatorInfo.getOperatorName())
                .type(FranLineTypeEnum.DELIVERY.getCode())
                .billNumber(wdDeliveryBillPO.getBillNo())
                .amount(wdDeliveryBillPO.getTotalTaxMoney().doubleValue())
                .storeCode(wdDeliveryBillPO.getInDeptCode())
                .build();
        return adjustReq;
    }

    /**
     * 具体审核操作
     *
     * @param wdDeliveryBillPO
     * @param deliveryBillDetailPOList
     */
    public Result<Boolean> doAudit(OpInfo opInfo,WdDeliveryBillPO wdDeliveryBillPO, List<WdDeliveryBillDetailPO> deliveryBillDetailPOList) {
        wdDeliveryBillPO.setStatus(WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_APPROVED.getCode());
        wdDeliveryBillPO.setApproveManCode(opInfo.getOperatorCode());
        wdDeliveryBillPO.setApproveManName(opInfo.getOperatorName());

        BatchRecordReq batchRecordReq = null;
        FranLineAdjustReq adjustReq = buildFranLineAdjustReq(wdDeliveryBillPO, opInfo);
        // 需求单号  根据需求单号获取申请单号
        List<PmsDemandPruchDeliveryRefResp> pmsDemandPruchDeliveryRefResps = null;
        if (StringUtils.isNotBlank(wdDeliveryBillPO.getSrcBillNo())) {
            //  申请单号不能为空
            PmsDemandPruchDeliveryRefReq demandPruchDeliveryRefReq = new PmsDemandPruchDeliveryRefReq();
            demandPruchDeliveryRefReq.setBillNo(wdDeliveryBillPO.getSrcBillNo());
            pmsDemandPruchDeliveryRefResps = pmsDemandPruchDeliveryRefRepositoryService.listPmsDemandPruchDeliveryByDemandBillNo(demandPruchDeliveryRefReq);
        }
        try {
            //加盟额度扣减
            if (wdDeliveryBillPO.handleJiaMeng()) {
                if (CollectionUtils.isNotEmpty(pmsDemandPruchDeliveryRefResps)) {
                    Map<String, Double> applyBillNoMoneyMap = pmsDemandPruchDeliveryRefResps.stream()
                            .collect(Collectors.groupingBy(PmsDemandPruchDeliveryRefResp::getApplyBillNo,
                                    Collectors.summingDouble(item ->
                                            MoneyUtil.round2HalfUp(MoneyUtil.bigDecimalNotNullVal(item.getSrcDemandQty())
                                                    .multiply(MoneyUtil.bigDecimalNotNullVal(item.getSrcOrderPrice()))).doubleValue()
                                    )
                            ));
                    adjustReq.setOrderNumberList(applyBillNoMoneyMap.keySet().stream()
                            .map(applyBillNo->{
                                return FranLineAdjustReq.OrderNumber.builder()
                                        .orderNumber(applyBillNo)
                                        .amount(applyBillNoMoneyMap.get(applyBillNo))
                                        .build();
                            })
                            .collect(Collectors.toList()));
                }
                commonFranchiseService.adjustFranLine(adjustReq);
            }
        } catch (Exception e) {
            Logs.error("加盟额度扣减失败，{}", e.getMessage());
            return Results.of(WDErrorCodeEnum.SC_WDS_001_P014.getErrorCode(), WDErrorCodeEnum.SC_WDS_001_P014.getErrorMsg() + "," + e.getMessage(), false);
        }
        Result<Boolean> booleanResult = Results.of(WDErrorCodeEnum.SC_WDS_001_P010, false);
        try {
            // 同步成本、库存
            batchRecordReq = convertStockReq(wdDeliveryBillPO, deliveryBillDetailPOList, WDDeliveryOptTypeEnum.AUDIT);
            // 需求单号  根据需求单号获取申请单号
            //  申请单号不能为空
            if (CollectionUtils.isNotEmpty(pmsDemandPruchDeliveryRefResps)) {
//                 配送订单 不需要 releaseBillType  释放单据类型
                //srcBillType 原单类型
                batchRecordReq.setReleaseSkuList(convertStockReleaseReq(wdDeliveryBillPO, pmsDemandPruchDeliveryRefResps, CommonOperateEnum.DECR));
            }
            BatchRecordResp batchRecordResp = iCommonStockService.costStockExecute(batchRecordReq);
            // todo 回写 商品成本单价
            backWriteGoodsCostPrice(batchRecordResp, deliveryBillDetailPOList);
            // 执行更新逻辑
            WdDeliveryBillPO update = WdDeliveryBillPO.builder()
                    .billNo(wdDeliveryBillPO.getBillNo())
                    .id(wdDeliveryBillPO.getId())
                    .approveManCode(opInfo.getOperatorCode())
                    .approveManName(opInfo.getOperatorName())
                    .approveTime(LocalDateTime.now())
                    .approveRemark(wdDeliveryBillPO.getApproveRemark())
                    .status(wdDeliveryBillPO.getStatus())
                    .build();
            update.setStatus(wdDeliveryBillPO.getStatus());
            update.setUpdateCode(opInfo.getOperatorCode());
            update.setUpdateName(opInfo.getOperatorName());
            update.setUpdateTime(opInfo.getOperateTime());
            iWdDeliveryBillRepository.updateById(update);
            return Results.ofSuccess(true);
        } catch (BizException | DecodeException e) {
            // 业务或者解析异常  停留在待审核
            Logs.error("成本库存同步失败，{}", e.getMessage());
//            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_001_P010);
//            iCommonStockService.costStockRollback(batchRecordReq);
            booleanResult.setMsg(e.getMessage());
            franLineRollback(wdDeliveryBillPO, adjustReq);
        } catch (RetryableException retryableException) {
            if (retryableException.getCause() instanceof SocketTimeoutException) {
                // 超时认为库存成功
                return Results.ofSuccess(true);
            }else {
                // 其它异常 停留在处理中
                booleanResult.setMsg(retryableException.getMessage());
                franLineRollback(wdDeliveryBillPO, adjustReq);
            }
        } catch (Exception e) {
            // 其它异常 停留在处理中
            booleanResult.setMsg(e.getMessage());
            franLineRollback(wdDeliveryBillPO, adjustReq);
        }
        return booleanResult;
    }

    public void franLineRollback(WdDeliveryBillPO bill, FranLineAdjustReq rollbackReq) {
        if (bill.handleJiaMeng()) {
            commonFranchiseService.franLineAdjustRollback(rollbackReq);
        }
    }

    /**
     * 回写 商品成本单价
     *
     * @param batchRecordResp
     * @param wdDeliveryBillDetailPOList
     * @return
     */
    public void backWriteGoodsCostPrice(BatchRecordResp batchRecordResp, List<WdDeliveryBillDetailPO> wdDeliveryBillDetailPOList) {
        // 回写单价信息 暂时不用
        List<BatchExecRowVo> batchStocksGoods = batchRecordResp.getSkuList();
        Map<Long, BatchExecRowVo> batch = batchStocksGoods.stream().collect(Collectors.toMap(BatchExecRowVo::getInsideId, Function.identity()));
        for (WdDeliveryBillDetailPO wdDeliveryBillDetailPO : wdDeliveryBillDetailPOList) {


        }
    }


    public BatchRecordReq convertStockReq(WdDeliveryBillPO wdDeliveryBillPO, List<WdDeliveryBillDetailPO> wdDeliveryBillDetailPOList,
                                          WDDeliveryOptTypeEnum optTypeEnum) {
        CommonBillTypeEnum billTypeEnum = wdDeliveryBillPO.getBillDirection() < 0 ? CommonBillTypeEnum.DR : CommonBillTypeEnum.DO;
        CommonDpgzTypeEnum dpgzTypeEnum = wdDeliveryBillPO.getBillDirection() < 0 ? CommonDpgzTypeEnum.DPGZ_6 : CommonDpgzTypeEnum.DPGZ_4;
        CommonOperateEnum operateCode = null;
        switch (optTypeEnum) {
            case AUDIT:
                operateCode = CommonOperateEnum.POST;
                break;
            case CANCEL:
                operateCode = CommonOperateEnum.CANCEL;
                break;
            case EXPIRE:
                operateCode = CommonOperateEnum.EXPIRE;
                break;
            case DELIVERY_END:
                operateCode = CommonOperateEnum.COMPLETE;
                break;
            default:
        }
        Integer negativeAllowedFlag;
        if (wdDeliveryBillPO.getBillDirection() < 0) {
            negativeAllowedFlag = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getIntValue(WDSystemParamEnum.DRSTOCKCONTROL, wdDeliveryBillPO.getInDeptCode());
        } else {
            negativeAllowedFlag = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getIntValue(WDSystemParamEnum.DOSTOCKCONTROL, wdDeliveryBillPO.getWhCode());
        }
        String deptCode = wdDeliveryBillPO.getInDeptCode();
        String deptName = wdDeliveryBillPO.getInDeptName();
        final CommonOperateEnum operateCodeEnum = operateCode;
        BatchRecordReq build = BatchRecordReq.builder()
                .tenantId(String.valueOf(wdDeliveryBillPO.getTenantId()))
                .whCode(deptCode)
                .whName(deptName)
                .deptCode(deptCode)
                .deptName(deptName)
                .billNo(wdDeliveryBillPO.getBillNo())
                .billType(billTypeEnum.getCode())
                .dpgzType(dpgzTypeEnum.getCode())
                .build();
        List<StkTaskItemExecuteDto> collect = wdDeliveryBillDetailPOList.stream().map(item -> {
            return StkTaskItemExecuteDto.builder()
                    .negativeAllowedFlag(negativeAllowedFlag)
                    .whCode(deptCode)
                    .whName(deptName)
                    .deptCode(deptCode)
                    .deptName(deptName)
                    .outWhCode(wdDeliveryBillPO.getWhCode())
                    .billType(billTypeEnum.getCode())
                    .insideId(item.getInsideId())
//                                    .saleMode(item.getSaleMode())
                    .skuCode(item.getSkuCode())
                    .skuName(item.getSkuName())
                    .skuType(item.getSkuType())
                    .inTaxRate(item.getInputTaxRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                    .operateCode(operateCodeEnum.getCode())
                    //.purchPrice(item.getLastPurchPrice())
                    .realQty(item.getDeliveryQty())
                    .build();
        }).collect(Collectors.toList());
        if (CommonOperateEnum.COMPLETE.equals(operateCode)) {
            collect = wdDeliveryBillDetailPOList.stream()
                    .filter(item -> item.getDeliveryQty().subtract(MoneyUtil.bigDecimalNotNullVal(item.getShipQty())).compareTo(BigDecimal.ZERO) > 0)
                    .map(item -> {
                        return StkTaskItemExecuteDto.builder()
                                .negativeAllowedFlag(negativeAllowedFlag)
                                .whCode(deptCode)
                                .whName(deptName)
                                .deptCode(deptCode)
                                .deptName(deptName)
                                .outWhCode(wdDeliveryBillPO.getWhCode())
                                .billType(billTypeEnum.getCode())
                                .insideId(item.getInsideId())
//                                    .saleMode(item.getSaleMode())
                                .skuCode(item.getSkuCode())
                                .skuName(item.getSkuName())
                                .skuType(item.getSkuType())
                                .inTaxRate(item.getInputTaxRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                                .operateCode(operateCodeEnum.getCode())
                                //.purchPrice(item.getLastPurchPrice())
                                .realQty(item.getDeliveryQty().subtract(MoneyUtil.bigDecimalNotNullVal(item.getShipQty())))
                                .build();
                    }).collect(Collectors.toList());
            // 履行完不需要终止
            if (CollectionUtils.isEmpty(collect)) {
                Logs.warn("配送订单 {} 全部履行完毕 不需要终止",  wdDeliveryBillPO.getBillNo());
                return null;
            }
        }
        build.setSkuList(collect);
        return build;
    }

    public List<StkTaskIReleaseExecuteDto> convertStockReleaseReq(WdDeliveryBillPO wdDeliveryBillPO, List<PmsDemandPruchDeliveryRefResp> pmsDemandPruchDeliveryRefResps
            , CommonOperateEnum operateCode) {
        return pmsDemandPruchDeliveryRefResps.stream().map(item -> {
            return StkTaskIReleaseExecuteDto.builder()
                    .whCode(wdDeliveryBillPO.getInDeptCode())
                    .operateCode(operateCode.getCode())
                    .outWhCode(wdDeliveryBillPO.getWhCode())
                    .skuCode(item.getSkuCode())
                    .skuType(item.getSkuType())
                    .realQty(item.getSrcDemandQty())
                    .srcBillNo(item.getApplyBillNo())
                    .srcBillType(WDDeliveryOrderDirectionEnum.PEI_SONG.getCode().equals(wdDeliveryBillPO.getBillDirection())?CommonBillTypeEnum.AO.getCode():CommonBillTypeEnum.RO.getCode())
                    .build();
        }).collect(Collectors.toList());
    }
}
