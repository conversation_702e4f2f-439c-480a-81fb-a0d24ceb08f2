package com.meta.supplychain.wds;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;

/**
 * <AUTHOR>
 */
@MapperScan("com.meta.supplychain.infrastructure.repository.mapper.*")
@ComponentScan("com.meta.supplychain.*")
@EnableFeignClients("com.meta.supplychain.*")
@EnableRetry
@SpringBootApplication
public class SupplychainWDSiteApplication {

    public static void main(String[] args) {
        SpringApplication.run(SupplychainWDSiteApplication.class, args);
    }

}
