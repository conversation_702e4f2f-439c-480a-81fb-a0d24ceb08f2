package com.meta.supplychain.wds.domain.delivery.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.AtomicDouble;
import com.meta.supplychain.common.component.service.intf.BillEventServiceFactory;
import com.meta.supplychain.common.component.service.intf.ISupplychainBizBillRuleService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonGoodsService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.constants.RedisKeyConstant;
import com.meta.supplychain.convert.wds.ShipAcceptConvert;
import com.meta.supplychain.convert.wds.ShipOrderConvert;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.bds.req.QueryDeptListReq;
import com.meta.supplychain.entity.dto.bds.resp.StoreDetailListResp;
import com.meta.supplychain.entity.dto.bds.resp.StoreDetailResp;
import com.meta.supplychain.entity.dto.goods.req.UpdatePurchPriceReq;
import com.meta.supplychain.entity.dto.stock.StkTaskIReleaseExecuteDto;
import com.meta.supplychain.entity.dto.stock.StkTaskItemExecuteDto;
import com.meta.supplychain.entity.dto.stock.WhLocationDTO;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.dto.stock.resp.BatchRecordResp;
import com.meta.supplychain.entity.dto.stock.resp.WhLocationResp;
import com.meta.supplychain.entity.dto.wds.SaveShipAcceptBillDTO;
import com.meta.supplychain.entity.dto.wds.WdShipAcceptSumDTO;
import com.meta.supplychain.entity.dto.wds.req.*;
import com.meta.supplychain.entity.dto.wds.resp.*;
import com.meta.supplychain.entity.po.wds.*;
import com.meta.supplychain.enums.*;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.enums.pms.PMSSystemParamEnum;
import com.meta.supplychain.enums.wds.*;
import com.meta.supplychain.infrastructure.feign.BaseDataSystemFeignClient;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.*;
import com.meta.supplychain.util.*;
import com.meta.supplychain.util.spring.SpringContextUtil;
import com.meta.supplychain.util.spring.SpringUtil;
import com.meta.supplychain.wds.domain.delivery.IShipAcceptDomainService;
import com.meta.supplychain.wds.util.ShipAcceptBillConvertUtil;
import feign.codec.DecodeException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ShipAcceptDomainServiceImpl implements IShipAcceptDomainService {

    private final IWdShipAcceptBillRepository shipAcceptBillRepository;

    private final ICommonGoodsService commonGoodsService;
    @Resource
    private ICommonStockService commonStockService;
    private final BaseDataSystemFeignClient baseDataSystemFeignClient;
    private final IWdDeliveryDetailPickRefRepository pickRefRepository;
    private final IWdShipAcceptBatchDetailRepository shipAcceptBatchDetailRepository;
    private final ISupplychainControlEngineService supplychainControlEngineService;
    private final IShipBillRepository shipOrderRepository;
    private final IShipBatchDetailRepository shipBatchDetailRepository;
    private final ISupplychainBizBillRuleService supplychainBizBillRuleService;
    private final IWdShipAcceptSumRepository shipAcceptSumRepository;
    final ICommonStockService iCommonStockService;
    private final BillEventServiceFactory billEventServiceFactory;
    final UserUtil userUtil;
    final RedisUtil redisUtil;

    @Autowired
    @Lazy
    private ShipAcceptDomainServiceImpl self;

    @Override
    public PageResult<WdShipAcceptBillResp> queryShipAcceptBillList(WdShipAcceptBillQueryReq queryParams) {
        // 构建查询条件
        LambdaQueryWrapper<WdShipAcceptBillPO> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (StringUtils.isNotBlank(queryParams.getDeptCode())) {
            queryWrapper.eq(WdShipAcceptBillPO::getDeptCode, queryParams.getDeptCode());
        }

        if (StringUtils.isNotBlank(queryParams.getWhCode())) {
            queryWrapper.eq(WdShipAcceptBillPO::getWhCode, queryParams.getWhCode());
        }

        if (StringUtils.isNotBlank(queryParams.getBillNo())) {
            queryWrapper.like(WdShipAcceptBillPO::getBillNo, queryParams.getBillNo());
        }

        if (StringUtils.isNotBlank(queryParams.getShipBillNo())) {
            queryWrapper.like(WdShipAcceptBillPO::getShipBillNo, queryParams.getShipBillNo());
        }

        if (CollectionUtils.isNotEmpty(queryParams.getStatusList())) {
            queryWrapper.in(WdShipAcceptBillPO::getStatus, queryParams.getStatusList());
        }

        if (CollectionUtils.isNotEmpty(queryParams.getDeptCodeList())) {
            queryWrapper.in(WdShipAcceptBillPO::getDeptCode, queryParams.getReqDeptCodeList());
        }

        if (queryParams.getBillDirection() != null) {
            queryWrapper.eq(WdShipAcceptBillPO::getBillDirection, queryParams.getBillDirection());
        }

        if (queryParams.getDirectSign() != null) {
            queryWrapper.eq(WdShipAcceptBillPO::getDirectSign, queryParams.getDirectSign());
        }

        // 入库核算日期范围
        if (queryParams.getInAccDateStart() != null) {
            queryWrapper.ge(WdShipAcceptBillPO::getInAccDate, queryParams.getInAccDateStart());
        }

        if (queryParams.getInAccDateEnd() != null) {
            queryWrapper.le(WdShipAcceptBillPO::getInAccDate, queryParams.getInAccDateEnd());
        }

        // 出库核算日期范围
        if (queryParams.getOutAccDateStart() != null) {
            queryWrapper.ge(WdShipAcceptBillPO::getOutAccDate, queryParams.getOutAccDateStart());
        }

        if (queryParams.getOutAccDateEnd() != null) {
            queryWrapper.le(WdShipAcceptBillPO::getOutAccDate, queryParams.getOutAccDateEnd());
        }

        // 按创建时间倒序排序
        queryWrapper.orderByDesc(WdShipAcceptBillPO::getCreateTime);

        // 分页查询
        Page<WdShipAcceptBillPO> page = new Page<>(queryParams.getCurrent(), queryParams.getPageSize());
        Page<WdShipAcceptBillPO> pageResult = shipAcceptBillRepository.page(page, queryWrapper);

        // 转换结果
        List<WdShipAcceptBillResp> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pageResult.getRecords())) {
            resultList = ShipAcceptConvert.INSTANCE.convertToResultList(pageResult.getRecords());
        }
        return new PageResult<>(pageResult.getTotal(), resultList);
    }

    @Override
    public PageResult<QueryShipBillForAcceptResp> queryShipAcceptBillList(QueryShipReq query){
        IPage<QueryShipBillForAcceptResp> convert = shipAcceptBillRepository.queryShipAcceptBillList(query).convert(ShipAcceptConvert.INSTANCE::convertDtoToQueryShipBillForAcceptResp);
        return new PageResult<>(convert.getTotal(), convert.getRecords());
    }


    @Override
    public WdShipAcceptBatchDetailForAcceptResp acceptDetail(String shipBillNo) {
        ShipBillPO shipBillPO = shipOrderRepository.queryShipBillPO(shipBillNo);
        if (Objects.isNull(shipBillPO)) {
            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_005_D001);
        }
        QueryShipBillResp queryShipBillResp = ShipOrderConvert.INSTANCE.shipPO2VO(shipBillPO);
        if (WDBillStatusEnum.SHIP_STATUS_3.getStatus().equals(shipBillPO.getStatus())
                || WDBillStatusEnum.SHIP_STATUS_4.getStatus().equals(shipBillPO.getStatus())
        ) {
            // 验证中 需要将验收单 返回给前端
            // 拉取所有暂存状态拨入
            List<WdShipAcceptBillPO> wdShipAcceptBillPOS = shipAcceptBillRepository.queryListByShipBillNo(shipBillPO.getBillNo());
            // 暂存或者待审核拨入单
            List<WdShipAcceptBillPO> alreadyList = wdShipAcceptBillPOS.stream()
                    .filter(e -> e.getStatus().equals(WDShipAcceptBillStatusEnum.DRAFT.getCode())
                            || e.getStatus().equals(WDShipAcceptBillStatusEnum.WAIT_AUDIT.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(alreadyList) || alreadyList.size() > 1) {
                BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_005_D004);
            }
            WdShipAcceptBillPO billPO = alreadyList.get(0);
            List<WdShipAcceptBatchDetailPO> detailPOList = shipAcceptBatchDetailRepository.queryByBillNo(billPO.getBillNo());
            return convertInfoForAccept(billPO, queryShipBillResp, detailPOList);
        } else if (WDBillStatusEnum.SHIP_STATUS_5.getStatus().equals(shipBillPO.getStatus())) {
            List<WdShipAcceptBillPO> wdShipAcceptBillPOS = shipAcceptBillRepository.queryListByShipBillNo(shipBillPO.getBillNo());
            if (CollectionUtils.isEmpty(wdShipAcceptBillPOS)) {
                BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_005_D006);
            }
            WdShipAcceptBillPO billPO = wdShipAcceptBillPOS.get(0);
            List<WdShipAcceptBatchDetailPO> detailPOList = shipAcceptBatchDetailRepository.queryByBillNo(billPO.getBillNo());
            return convertInfoForAccept(billPO, queryShipBillResp, detailPOList);
        } else {
            // 已发货 需要转换  ShipBillPO 转 WdShipAcceptBillPO
            WdShipAcceptBillPO acceptBillPO = ShipAcceptBillConvertUtil.convertFromShipBill(shipBillPO);
            List<ShipBatchDetailPO> shipBatchDetailPOS = shipBatchDetailRepository.queryShipBatchDetailList(shipBillPO.getBillNo());
            // List<ShipBatchDetailPO> 转 List<WdShipAcceptBatchDetailPO>
            List<WdShipAcceptBatchDetailPO> acceptBatchDetailPOList = ShipAcceptBillConvertUtil.convertFromShipBillDetailAll(acceptBillPO, shipBatchDetailPOS);
            return WdShipAcceptBatchDetailForAcceptResp.builder()
                    .acceptBill(ShipAcceptConvert.INSTANCE.convertToQueryShipBillForAcceptResp(queryShipBillResp))
                    //WdShipAcceptBatchDetailPO 转 WdShipAcceptBatchDetailInlineResp
                    .detailList(ShipAcceptConvert.INSTANCE.convertToInlineRespList(acceptBatchDetailPOList))
                    .build();
        }
    }

    /**
     * 转换配送单信息 为验收使用
     *
     * @param billPO
     * @param queryShipBillResp
     * @param detailPOList
     * @return
     */
    private WdShipAcceptBatchDetailForAcceptResp convertInfoForAccept(WdShipAcceptBillPO billPO, QueryShipBillResp queryShipBillResp, List<WdShipAcceptBatchDetailPO> detailPOList) {
        QueryShipBillForAcceptResp queryShipBillForAcceptResp = ShipAcceptConvert.INSTANCE.convertToQueryShipBillForAcceptResp(queryShipBillResp);


        List<WdShipAcceptBatchDetailInlineResp> wdShipAcceptBatchDetailInlineResps = ShipAcceptConvert.INSTANCE.convertToInlineRespList(detailPOList);
        if (Objects.nonNull(billPO)) {
            queryShipBillForAcceptResp.setBillNo(billPO.getBillNo());
            queryShipBillForAcceptResp.setRemarkShip(billPO.getRemarkShip());
            queryShipBillForAcceptResp.setRemarkAccept(billPO.getRemarkAccept());
            queryShipBillForAcceptResp.setAcceptTime(billPO.getCreateTime());
            queryShipBillForAcceptResp.setAcceptManCode(billPO.getCreateCode());
            queryShipBillForAcceptResp.setAcceptManName(billPO.getCreateName());
        }
        return WdShipAcceptBatchDetailForAcceptResp.builder()
                .acceptBill(queryShipBillForAcceptResp)
                //WdShipAcceptBatchDetailPO 转 WdShipAcceptBatchDetailInlineResp
                .detailList(wdShipAcceptBatchDetailInlineResps)
                .build();
    }

    @Override
    public WdShipAcceptBatchDetailQueryResp queryShipAcceptBatchDetail(String billNo) {
        // 根据单据号查询
        if (StringUtils.isNotBlank(billNo)) {
            WdShipAcceptBillPO billPO = shipAcceptBillRepository.queryByBillNo(billNo);
            if (Objects.isNull(billPO)) {
                BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_005_D006);
            }
            ShipBillPO shipBillPO = shipOrderRepository.queryShipBillPO(billPO.getShipBillNo());
            if (Objects.isNull(shipBillPO)) {
                BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_005_D001);
            }
            QueryShipBillResp queryShipBillResp = ShipOrderConvert.INSTANCE.shipPO2VO(shipBillPO);
            QueryShipBillForAcceptResp queryShipBillForAcceptResp = ShipAcceptConvert.INSTANCE.convertToQueryShipBillForAcceptResp(queryShipBillResp);
            queryShipBillForAcceptResp.setBillNo(billPO.getBillNo());
            queryShipBillForAcceptResp.setRemarkAccept(billPO.getRemarkAccept());
            queryShipBillForAcceptResp.setAcceptTime(billPO.getCreateTime());
            queryShipBillForAcceptResp.setAcceptManCode(billPO.getCreateCode());
            queryShipBillForAcceptResp.setAcceptManName(billPO.getCreateName());
            List<WdShipAcceptBatchDetailPO> detailPOList = shipAcceptBatchDetailRepository.queryByBillNo(billNo);
            return WdShipAcceptBatchDetailQueryResp.builder()
                    .acceptBill(queryShipBillForAcceptResp)
                    .detailList(ShipAcceptConvert.INSTANCE.convertToBatchDetailResultList(detailPOList))
                    .build();
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WdShipBatchAcceptBillCreateResp acceptBatch(WdShipBatchAcceptBillCreateReq acceptBillCreateReq) {
        OpInfo opInfo = userUtil.getDeptAndUpDeptOpInfoWithThrow();
        // 拆分
        Map<ShipBillPO, WdShipAcceptBillCreateReq> acceptMap = splitBatchAccept(acceptBillCreateReq);
        List<WdShipAcceptSumPO> unionBillList = new ArrayList<>();
        String unionBillNo = String.valueOf(IdWorker.getId());
        List<String> successBillNoList = new ArrayList<>();
        List<WdShipBatchAcceptBillCreateResp.ShipBatchAcceptErrorInfo> errorBillNoList = new ArrayList<>();
        // 循环调用单次验收
        acceptMap.keySet().forEach(shipBillPO -> {
            WdShipAcceptBillCreateReq acceptBatchDetailCreateReq = acceptMap.get(shipBillPO);
            acceptBatchDetailCreateReq.setOpInfo(opInfo);
            try {
                String acceptBill = createShipAcceptBill(acceptBatchDetailCreateReq);
                successBillNoList.add(acceptBill);
                unionBillList.add(WdShipAcceptSumPO.builder()
                        .billNo(acceptBill)
                        .shipBillNo(shipBillPO.getBillNo())
                        .unionBillNo(unionBillNo)
                        .createCode(opInfo.getOperatorCode())
                                .createName(opInfo.getOperatorName())
                                .createTime(LocalDateTime.now())
                        .build()
                );
            } catch (Exception e) {
                Logs.error("配送单 {} 验收异常", shipBillPO.getBillNo(), e);
                errorBillNoList.add(WdShipBatchAcceptBillCreateResp.ShipBatchAcceptErrorInfo
                        .builder()
                        .shipBillNo(shipBillPO.getBillNo())
                        .errorMsg(e.getMessage())
                        .build());
            }
        });
        // 记录合并验收表
        billEventServiceFactory.publishEvent(unionBillNo, BillActionTypeEnum.SHIP_BATCH_ACCEPT, new WdShipAcceptSumDTO(unionBillList));
        return WdShipBatchAcceptBillCreateResp.builder()
                .successBillNoList(successBillNoList)
                .errorBillNoList(errorBillNoList)
                .build();
    }


    @Override
    public String createShipAcceptBill(WdShipAcceptBillCreateReq createParams) {
        OpInfo opInfo = createParams.getOpInfo();
        // 发货单验收分布式锁 同时只能有一次验收操作存在
        Integer optType = createParams.getOptType();
        WDShipAcceptOptTypeEnum optTypeEnum = StandardEnum.codeOf(WDShipAcceptOptTypeEnum.class, optType);
        if (Objects.isNull(optTypeEnum)) {
            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_005_P002);
        }
        // 查询发货单 校验配送单状态
        ShipBillPO shipBillPO = shipOrderRepository.queryShipBillPO(createParams.getShipBillNo());
        if (shipBillPO == null) {
            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_005_D001);
        }
        String billNo = supplychainBizBillRuleService.getBillNo(MdBillNoBillTypeEnum.INCOMING_DELIVERY, shipBillPO.getInDeptCode());
        Integer status = shipBillPO.getStatus();
        if (!(
                WDBillStatusEnum.SHIP_STATUS_2.getStatus().equals(shipBillPO.getStatus())
                        || WDBillStatusEnum.SHIP_STATUS_3.getStatus().equals(shipBillPO.getStatus())
                        || WDBillStatusEnum.SHIP_STATUS_4.getStatus().equals(shipBillPO.getStatus())
        )) {
            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_005_D002);
        }
        // 查询发货单详情 wd_ship_batch_detail 校验发货标识
        List<ShipBatchDetailPO> shipBatchDetailPOS = shipBatchDetailRepository.queryShipBatchDetailList(shipBillPO.getBillNo());
        Optional<ShipBatchDetailPO> any = shipBatchDetailPOS.stream()
                .filter(shipBatchDetailPO -> YesOrNoEnum.NO.getCode().equals(shipBatchDetailPO.getAcceptSign()))
                .findAny();
        // 不存在未验收的发货单详情报错
        if (!any.isPresent()) {
            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_005_D003);
        }
        long result = redisUtil.incr(RedisKeyConstant.SHIP_ACCEPT_LOCK_KEY + shipBillPO.getBillNo(), 1);
        if (result > 1) {
            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_005_D005);
        }
        try {
            WdShipAcceptBillPO acceptBillPO;
            boolean isUpdate = false;
            // 拉取所有暂存状态拨入
            List<WdShipAcceptBillPO> wdShipAcceptBillPOS = shipAcceptBillRepository.queryListByShipBillNo(shipBillPO.getBillNo());
            // 暂存或者待审核拨入单
            List<WdShipAcceptBillPO> alreadyList = wdShipAcceptBillPOS.stream()
                    .filter(e -> e.getStatus().equals(WDShipAcceptBillStatusEnum.DRAFT.getCode())
                            || e.getStatus().equals(WDShipAcceptBillStatusEnum.WAIT_AUDIT.getCode()))
                    .collect(Collectors.toList());
            // 存在
            if (CollectionUtils.isNotEmpty(alreadyList)) {
                acceptBillPO = alreadyList.get(0);
                isUpdate = true;
                acceptBillPO.setRemarkAccept(createParams.getRemarkAccept());
            } else {
                // 不存在
                // 构建 验收单据
                acceptBillPO = ShipAcceptBillConvertUtil.convertFromShipBill(shipBillPO);
                // 填充部门信息
                List<String> deptCodeList = Lists.newArrayList();
                deptCodeList.add(acceptBillPO.getDeptCode());
                deptCodeList.add(acceptBillPO.getWhCode());
                StoreDetailListResp storeList = baseDataSystemFeignClient.queryDeptList(QueryDeptListReq.builder().deptCodeList(deptCodeList).build());
                Map<String, StoreDetailResp> storeMap = storeList.getRows().stream().collect(Collectors.toMap(StoreDetailResp::getCode, Function.identity(), (k1, k2) -> k1));
                StoreDetailResp inDeptInfo = storeMap.get(acceptBillPO.getDeptCode());
                StoreDetailResp whDeptInfo = storeMap.get(acceptBillPO.getWhCode());
                acceptBillPO.setDeptType(inDeptInfo.getType());
                acceptBillPO.setInAccCode(inDeptInfo.getAccountCode());
                acceptBillPO.setInAccDate(LocalDate.now());
                acceptBillPO.setWhAccCode(whDeptInfo.getAccountCode());
                acceptBillPO.setStatus(optTypeEnum.getShipAcceptBillStatusEnum().getCode());
                acceptBillPO.setCreateTime(opInfo.getOperateTime());
                acceptBillPO.setCreateName(opInfo.getOperatorName());
                acceptBillPO.setCreateCode(opInfo.getOperatorCode());
                acceptBillPO.setUpdateCode(opInfo.getOperatorCode());
                acceptBillPO.setUpdateName(opInfo.getOperatorName());
                acceptBillPO.setUpdateTime(opInfo.getOperateTime());
                acceptBillPO.setBillNo(billNo);
                acceptBillPO.setRemarkAccept(createParams.getRemarkAccept());
            }
            if (DeptTypeEnum.DELIVERY_STORE.getCode().equals(acceptBillPO.getDeptType())) {
                // 入货部门为配送中心 需要查询储位信息
                List<String> skuCodeList = createParams.getAcceptDetailList().stream().map(WdShipAcceptBatchDetailCreateReq::getSkuCode).distinct().collect(Collectors.toList());
                createParams.setLocationMap(locationMapHandle(acceptBillPO.getDeptCode(), skuCodeList));
            }

            // 构建 验收详情
            List<WdShipAcceptBatchDetailPO> acceptBatchDetailPOList = ShipAcceptBillConvertUtil.convertFromShipBillDetail(acceptBillPO, shipBatchDetailPOS, createParams);
            SaveShipAcceptBillDTO saveShipAcceptBillDTO = SaveShipAcceptBillDTO.builder()
                    .isUpdate(isUpdate)
                    .acceptBillPO(acceptBillPO)
                    .shipAcceptBatchDetailPOS(acceptBatchDetailPOList)
                    .optTypeEnum(optTypeEnum)
                    .opInfo(opInfo)
                    .shipBillPO(shipBillPO)
                    .build();
            // 保存单据信息
            Result<Boolean> booleanResult = self.saveOrUpdateBillInfo(saveShipAcceptBillDTO);
            if (!booleanResult.isSuccess()) {
                BizExceptionUtil.throwWithErrorCodeAndMsg(WDErrorCodeEnum.SC_WDS_005_D007, booleanResult.getMsg());
            }
            return acceptBillPO.getBillNo();
        } catch (BizException | DecodeException e) {
            throw e;
        } catch (Exception e) {
            Logs.error("配送单 {} 验收失败 ", shipBillPO.getBillNo(), e);
            BizExceptions.throwWithCodeAndMsg(WDErrorCodeEnum.SC_WDS_005_D007.getCode(), WDErrorCodeEnum.SC_WDS_005_D007.getErrorMsg() + e.getMessage());
        } finally {
            redisUtil.del(RedisKeyConstant.SHIP_ACCEPT_LOCK_KEY + shipBillPO.getBillNo());
        }
        return null;
    }


    /**
     * 处理仓库储位信息
     *
     * @param deptCode
     * @param skuCodeList
     * @return
     */
    public Map<String, WhLocationDTO> locationMapHandle(String deptCode,List<String> skuCodeList) {
        Map<String, List<WhLocationDTO>> locationMap = new HashMap<>();
        List<WhLocationResp> locationResp = commonStockService.getAcceptSkuLocList(deptCode, skuCodeList);
        locationResp.forEach(e->{
            List<WhLocationDTO> orDefault = locationMap.getOrDefault(e.getSkuCode(), new ArrayList<>());
            orDefault.addAll(e.getWhLocList());
            locationMap.put(e.getSkuCode(), orDefault);
        });
        Map<String, WhLocationDTO> locationMapHandle = new HashMap<>();
        locationMap.forEach((skuCode, locList)->{
            if (CollectionUtils.isNotEmpty(locList)) {
                List<WhLocationDTO> numList = locList.stream().filter(item -> Pattern.matches("^[0-9]*$", item.getLocationCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(numList)) {
                    locationMapHandle.put(skuCode, numList.get(0));
                }else {
                    locationMapHandle.put(skuCode, locList.get(0));
                }
            }
        });
        return locationMapHandle;
    }

    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveOrUpdateBillInfo(SaveShipAcceptBillDTO saveShipAcceptBillDTO) {
        WdShipAcceptBillPO acceptBillPO = saveShipAcceptBillDTO.getAcceptBillPO();
        List<WdShipAcceptBatchDetailPO> shipAcceptBatchDetailPOS = saveShipAcceptBillDTO.getShipAcceptBatchDetailPOS();
        WDShipAcceptOptTypeEnum optTypeEnum = saveShipAcceptBillDTO.getOptTypeEnum();
        OpInfo opInfo = saveShipAcceptBillDTO.getOpInfo();
        boolean isUpdate = saveShipAcceptBillDTO.isUpdate();
        if (!isUpdate) {
            shipAcceptBillRepository.save(acceptBillPO);
        }
        WdShipAcceptBillPO.WdShipAcceptBillPOBuilder acceptBillPOBuilder = WdShipAcceptBillPO.builder()
                .status(optTypeEnum.getShipAcceptBillStatusEnum().getCode())
                .remarkAccept(acceptBillPO.getRemarkAccept())
                .id(acceptBillPO.getId());
        // 填充合计信息
        List<Integer> oldStatusList = Arrays.asList(WDBillStatusEnum.SHIP_STATUS_2.getStatus(), WDBillStatusEnum.SHIP_STATUS_3.getStatus());
        List<Integer> oldStatusListForConfirm = Arrays.asList(WDBillStatusEnum.SHIP_STATUS_2.getStatus(),
                WDBillStatusEnum.SHIP_STATUS_3.getStatus(), WDBillStatusEnum.SHIP_STATUS_4.getStatus()
        );
        switch (optTypeEnum) {
            case SAVE:
                // 更新配送单 验收中
                shipOrderRepository.updateStatus(acceptBillPO.getShipBillNo(), WDBillStatusEnum.SHIP_STATUS_3.getStatus(), new ArrayList<>(oldStatusList));
                break;
            case CONFIRM:
                // 更新配送单 验收待审核
                shipOrderRepository.updateStatus(acceptBillPO.getShipBillNo(), WDBillStatusEnum.SHIP_STATUS_4.getStatus(), new ArrayList<>(oldStatusListForConfirm));
                // 更新配送单 验收待审核
                acceptBillPOBuilder.confirmManCode(opInfo.getOperatorCode()).confirmManName(opInfo.getOperatorName()).confirmTime(opInfo.getOperateTime());
                acceptBillPO.setConfirmManCode(opInfo.getOperatorCode());
                acceptBillPO.setConfirmManName(opInfo.getOperatorName());
                acceptBillPO.setConfirmTime(opInfo.getOperateTime());
                break;
            case AUDIT:
                acceptBillPOBuilder.approveManCode(opInfo.getOperatorCode()).approveManName(opInfo.getOperatorName()).approveTime(opInfo.getOperateTime());
                acceptBillPO.setApproveManCode(opInfo.getOperatorCode());
                acceptBillPO.setApproveManName(opInfo.getOperatorName());
                acceptBillPO.setApproveTime(opInfo.getOperateTime());
            default:
        }
        // 填充合计信息
        acceptBillPOBuilder
                .totalAcceptQty(shipAcceptBatchDetailPOS.stream().map(WdShipAcceptBatchDetailPO::getAcceptQty).reduce(BigDecimal.ZERO, BigDecimal::add))
                .totalAcceptTax(shipAcceptBatchDetailPOS.stream().map(WdShipAcceptBatchDetailPO::getAcceptTax).reduce(BigDecimal.ZERO, BigDecimal::add))
                .totalAcceptTaxMoney(shipAcceptBatchDetailPOS.stream().map(WdShipAcceptBatchDetailPO::getAcceptTaxMoney).reduce(BigDecimal.ZERO, BigDecimal::add))
        ;
        WdShipAcceptBillPO build = acceptBillPOBuilder.build();
        build.setUpdateTime(opInfo.getOperateTime());
        build.setUpdateCode(opInfo.getOperatorCode());
        build.setUpdateName(opInfo.getOperatorName());

        shipAcceptBatchDetailRepository.deleteByBillNo(acceptBillPO.getBillNo());
        shipAcceptBatchDetailRepository.saveBatch(shipAcceptBatchDetailPOS);

        ShipBillPO shipBillPO = saveShipAcceptBillDTO.getShipBillPO();
        BatchRecordReq batchRecordReq = null;
        if (optTypeEnum == WDShipAcceptOptTypeEnum.AUDIT) {
            // 同步库存
            try {
                CommonOperateEnum operateEnum = shipBillPO.getReversalBillSign().equals(YesOrNoEnum.YES.getCode()) ? CommonOperateEnum.RED : CommonOperateEnum.POST;
                batchRecordReq = convertStockReq(CommonBillTypeEnum.DA, operateEnum, shipBillPO.getBillNo(), acceptBillPO, shipAcceptBatchDetailPOS);
                BatchRecordResp batchRecordResp = iCommonStockService.costStockExecute(batchRecordReq);
                // 入账时间
                String costModel = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(PMSSystemParamEnum.COST_SAM_MODE, acceptBillPO.getDeptCode());
                if (AccountingMethodEnum.PURCHASE_COST.getCode().toString().equals(costModel)) {
                    // *******.如果系统参数.成本核算方式=进价成本，则发货入账日期=审核日期；
                    build.setInAccDate(acceptBillPO.getApproveTime().toLocalDate());
                }else {
                    //*******.如果系统参数.成本核算方式<>进价成本，则发货入账日期=批次流水返回的入账日期；
                    LocalDate occurDate = batchRecordResp.getAccDate();
                    //  批次返回的入账日期
                    if (Objects.nonNull(occurDate)) {
                        build.setInAccDate(occurDate);
                    }
                }
                // 更新中台商品价格
                // 是否需要改价
                boolean changePrice = supplychainControlEngineService.getSupplychainBizSysParamRuleService().isEnable(WDSystemParamEnum.ENABLE_MODIFY_STORE_LAST_PRICE_TRANSFER, acceptBillPO.getDeptCode());
                if (changePrice) {
                    updateGoodsPrice(acceptBillPO, shipAcceptBatchDetailPOS);
                }
                shipAcceptBillRepository.updateById(build);
                // 更新配送订单收货信息  配送单行收货信息  发 q 处理
                billEventServiceFactory.publishEvent(acceptBillPO.getBillNo(), BillActionTypeEnum.SHIP_ACCEPT_COMPLETE, saveShipAcceptBillDTO);
                return Results.ofSuccess();
            } catch (BizException | DecodeException e) {
                Logs.error("配送单 {} 验收同步库存报错", shipBillPO.getBillNo(), e);
                // 库存异常保存单据信息 状态待审核
                build.setStatus(WDShipAcceptBillStatusEnum.WAIT_AUDIT.getCode());
                // 更新配送单 验收待审核
                shipOrderRepository.updateStatus(acceptBillPO.getShipBillNo(), WDBillStatusEnum.SHIP_STATUS_4.getStatus(), new ArrayList<>(oldStatusListForConfirm));
                // 更新配送单 验收待审核
                build.setConfirmManCode(opInfo.getOperatorCode());
                build.setConfirmManName(opInfo.getOperatorName());
                build.setConfirmTime(opInfo.getOperateTime());
                build.setUpdateCode(opInfo.getOperatorCode());
                build.setUpdateName(opInfo.getOperatorName());
                build.setUpdateTime(opInfo.getOperateTime());
                shipAcceptBillRepository.updateById(build);
                return Results.ofCommonError(WDErrorCodeEnum.SC_WDS_005_D008);
            } catch (Exception e) {
                // 其它异常直接回滚
                BizExceptionUtil.throwWithErrorCodeAndMsg(WDErrorCodeEnum.SC_WDS_005_D008, e.getMessage());
            }
        }else {
            build.setUpdateCode(opInfo.getOperatorCode());
            build.setUpdateName(opInfo.getOperatorName());
            build.setUpdateTime(opInfo.getOperateTime());
            shipAcceptBillRepository.updateById(build);
        }
        return Results.ofSuccess();
    }


    /**
     * 改价  分批调用
     * @param acceptBillPO
     * @param shipAcceptBatchDetailPOS
     */
    public void updateGoodsPrice(WdShipAcceptBillPO acceptBillPO, List<WdShipAcceptBatchDetailPO> shipAcceptBatchDetailPOS) {
        List<List<WdShipAcceptBatchDetailPO>> partition = Lists.partition(shipAcceptBatchDetailPOS, 500);
        partition.forEach(redeployInDetailList->{
            try {
                Set<String> skuSet = new HashSet<>();
                List<UpdatePurchPriceReq.GoodsPriceInfo> collect = new ArrayList<>();
                redeployInDetailList.forEach(item -> {
                    if (!skuSet.contains(item.getSkuCode())) {
                        UpdatePurchPriceReq.GoodsPriceInfo build = UpdatePurchPriceReq.GoodsPriceInfo.builder()
                                .purchPrice(MoneyUtil.yuanToMilli(item.getAcceptPrice().doubleValue()))
                                .skuCode(item.getSkuCode())
                                .spuCode(item.getSkuCode())
                                .build();
                        collect.add(build);
                        skuSet.add(item.getSkuCode());
                    }
                });
                UpdatePurchPriceReq build = UpdatePurchPriceReq.builder()
                        .source(SpringContextUtil.getApplicationName())
                        .serialNo(acceptBillPO.getBillNo())
                        .storeCode(acceptBillPO.getDeptCode())
                        .goodsList(collect)
                        .build();
                commonGoodsService.updatePurchPrice(build);
            } catch (Exception e) {
                Logs.error("验收单号 {} 改价异常 ", acceptBillPO.getBillNo(), e.getMessage());
            }
        });
    }

    public BatchRecordReq convertStockReq(CommonBillTypeEnum commonBillTypeEnum,CommonOperateEnum operateEnum, String srcBillNo, WdShipAcceptBillPO acceptBillPO,
                                          List<WdShipAcceptBatchDetailPO> shipAcceptBatchDetailPOS) {

        CommonDpgzTypeEnum dpgzTypeEnum = acceptBillPO.getBillDirection() < 0 ? CommonDpgzTypeEnum.DPGZ_6 : CommonDpgzTypeEnum.DPGZ_5;

        // 逆向配送 需要判断门店负库存
        Integer negativeAllowedFlag = 0;
        if (acceptBillPO.getBillDirection() < 0) {
            negativeAllowedFlag = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getIntValue(WDSystemParamEnum.DRSTOCKCONTROL, acceptBillPO.getDeptCode());
        }
        // 根据单据类型判断来源单据类型
        CommonBillTypeEnum srcBillType = commonBillTypeEnum;
        switch (commonBillTypeEnum) {
            case DA:
                srcBillType = CommonBillTypeEnum.DN;
                break;
            case DNR:
                srcBillType = CommonBillTypeEnum.DR;
                break;
            case DNR_DIFF:
                srcBillType = CommonBillTypeEnum.DN_DIFF;
                break;
        }
        final String srcBillTypeStr = srcBillType.getCode();
        final Integer negativeAllowedFlagFinal = negativeAllowedFlag;
        String deptCode = acceptBillPO.getDeptCode();
        String deptName = acceptBillPO.getDeptName();
        BatchRecordReq build = BatchRecordReq.builder()
                .deptCode(deptCode)
                .deptName(deptName)
                .whCode(deptCode)
                .whName(deptName)
                .billNo(acceptBillPO.getBillNo())
                .billType(commonBillTypeEnum.getCode())
//                .dpgzType(dpgzTypeEnum.getCode())
                .billTime(LocalDateTime.now())
                .skuList(shipAcceptBatchDetailPOS.stream().map(item -> {
                    StkTaskItemExecuteDto detail = StkTaskItemExecuteDto.builder()
                            .whCode(deptCode)
                            .whName(deptName)
                            .deptCode(deptCode)
                            .deptName(deptName)
                            .outWhCode(acceptBillPO.getWhCode())
                            .billType(commonBillTypeEnum.getCode())
                            .insideId(item.getInsideId())
                            .skuCode(item.getSkuCode())
                            .skuName(item.getSkuName())
                            .skuType(item.getSkuType())
                            .inTaxRate(item.getInputTaxRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                            .operateCode(operateEnum.getCode())
                            .costTaxPrice(item.getAcceptPrice())
                            .realQty(item.getShipQty())
                            .deliveryBillNo(srcBillNo)
                            .negativeAllowedFlag(negativeAllowedFlagFinal)
                            .build();
                    if (CommonBillTypeEnum.DNR_DIFF.equals(commonBillTypeEnum)) {
                        detail.setRefBillNo(srcBillNo);
                        detail.setRefBillType(CommonBillTypeEnum.DN_DIFF.getCode());
                        detail.setRefBillAccDate(acceptBillPO.getCreateTime().toLocalDate());
                    }
                    if (CommonOperateEnum.RED.equals(operateEnum)) {
                        detail.setSrcBillNo(item.getSrcBillNo());
                        detail.setSrcBillType(item.getSrcBillType());
                        detail.setSrcInsideId(item.getSrcInsideId().intValue());
                    }
                    return detail;
                }).collect(Collectors.toList()))
                .releaseSkuList(shipAcceptBatchDetailPOS.stream().map(item -> {
                    StkTaskIReleaseExecuteDto detail = StkTaskIReleaseExecuteDto.builder()
                            .whCode(acceptBillPO.getDeptCode())
                            .outWhCode(acceptBillPO.getWhCode())
//                            .ioType()
                            .skuCode(item.getSkuCode())
                            .skuType(item.getSkuType())
                            .operateCode(operateEnum.getCode())
                            .realQty(item.getShipQty())
                            .srcBillType(srcBillTypeStr)
                            .srcBillNo(srcBillNo)
                            .build();
                    return detail;
                }).collect(Collectors.toList()))
                .build();
        return build;
    }

    /**
     * 拆分多单合并验收按 skuCode 聚合的效期数据
     * <p>
     * 目的是把收货商品均摊到每个配送单上
     *
     * @param batchAcceptReq
     * @return 配送单，改单验收商品
     */
    public Map<ShipBillPO, WdShipAcceptBillCreateReq> splitBatchAccept(WdShipBatchAcceptBillCreateReq batchAcceptReq) {
        // 拆分目标
        Map<ShipBillPO, WdShipAcceptBillCreateReq> resultMap = new HashMap<>();
        List<WdShipAcceptBatchDetailCreateReq> goodsDetailList = batchAcceptReq.getAcceptDetailList();
        // 未填充单据号为合并验收
        List<WdShipAcceptBatchDetailCreateReq> unionGoodsList = goodsDetailList.stream()
                .filter(item -> StringUtils.isBlank(item.getShipBillNo()))
                .collect(Collectors.toList());
        // 相同
        Map<String, List<WdShipAcceptBatchDetailCreateReq>> acceptGoodsGroup = goodsDetailList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getShipBillNo()))
                .collect(Collectors.groupingBy(WdShipAcceptBatchDetailCreateReq::getShipBillNo));
        List<ShipBillPO> shipBillPOList = batchAcceptReq.getShipBillNoList().stream().map(shipOrderRepository::queryShipBillPO).collect(Collectors.toList());

        // 先添加相同的商品
        shipBillPOList.forEach(shipBillPO -> {
            WdShipAcceptBillCreateReq defaultReq = getDefaultReq(batchAcceptReq, shipBillPO);
            WdShipAcceptBillCreateReq orDefault = resultMap.getOrDefault(shipBillPO, defaultReq);
            List<WdShipAcceptBatchDetailCreateReq> acceptGoods = acceptGoodsGroup.get(shipBillPO.getBillNo());
            if (CollectionUtils.isNotEmpty(acceptGoods)) {
                orDefault.getAcceptDetailList().addAll(acceptGoods);
            }
            resultMap.put(shipBillPO, orDefault);
        });
        if (CollectionUtils.isNotEmpty(unionGoodsList)) {
            Map<String, ShipBillPO> transferOrderMap = shipBillPOList.stream().collect(Collectors.toMap(ShipBillPO::getBillNo, Function.identity()));
            // 统一按sku维度聚合 需要拆分
            Map<String, List<WdShipAcceptBatchDetailCreateReq>> unionAcceptGoodsMap = unionGoodsList.stream().collect(Collectors.groupingBy(WdShipAcceptBatchDetailCreateReq::getSkuCode));
            // 查询所有单据 对应商品的配送验收商品详情
            // 验收的所有单内 未验收的商品行
            List<ShipBatchDetailPO> allSkuList = new ArrayList<>();
            shipBillPOList.stream()
                    .map(item ->
                            shipBatchDetailRepository.queryShipBatchDetailList(item.getBillNo())
                                    .stream().filter(goods -> YesOrNoEnum.NO.getCode().equals(goods.getAcceptSign()))
                                    .collect(Collectors.toList())
                    )
                    .collect(Collectors.toList())
                    .forEach(allSkuList::addAll);
            // 筛选出本次验收 被合并收货的商品行
            List<ShipBatchDetailPO> waitUnionGoodsListFromDb = allSkuList.stream()
                    .filter(item -> unionAcceptGoodsMap.containsKey(item.getSkuCode()))
                    .collect(Collectors.toList());
            // 可能存在多个 行 sku 相同  分组 skuCode
            Map<String, List<ShipBatchDetailPO>> waitUnionGoodsMapFromDb = waitUnionGoodsListFromDb.stream()
                    .collect(Collectors.groupingBy(ShipBatchDetailPO::getSkuCode));
            // 按验收数据循环 对收货数量进行拆分 分摊到不同 billNumber
            unionAcceptGoodsMap.forEach((goodsCode, acceptGoodsList) -> {
                List<ShipBatchDetailPO> waitAcceptGoodsListFromDb = waitUnionGoodsMapFromDb.get(goodsCode);
                // 效期数据不同会有多行
                Map<String, WdShipAcceptBatchDetailCreateReq> acceptGoodsListExpierMap = acceptGoodsList.stream()
                        .filter(this::acceptReqIsBlankExpire)
                        .collect(Collectors.toMap(this::getAcceptReqExpireKey, Function.identity()));
                Map<String, List<ShipBatchDetailPO>> waitExpireMapFromDb = waitAcceptGoodsListFromDb.stream()
                        .collect(Collectors.groupingBy(this::getExpireKey));
                // 按效期信息循环
                waitExpireMapFromDb.keySet().forEach(expireKey -> {
                    WdShipAcceptBatchDetailCreateReq expireAcceptGoods = acceptGoodsListExpierMap.get(expireKey);
                    List<ShipBatchDetailPO> waitAcceptGoodsFromDb = waitExpireMapFromDb.get(expireKey);
                    Map<String, ShipBatchDetailPO> waitAcceptGoodsMapFromDb = waitAcceptGoodsFromDb.stream().collect(Collectors.toMap(ShipBatchDetailPO::getBillNo, Function.identity()));
                    // 没找到填充 则随机
                    if (Objects.isNull(expireAcceptGoods)) {
                        // 接收为空 则直接填充 库中的商品
                        waitAcceptGoodsMapFromDb.keySet().forEach(billNumber -> {
                            ShipBatchDetailPO shipBatchDetailPO = waitAcceptGoodsMapFromDb.get(billNumber);
                            ShipBillPO shipBillPO = transferOrderMap.get(billNumber);
                            WdShipAcceptBillCreateReq defaultReq = getDefaultReq(batchAcceptReq, shipBillPO);
                            WdShipAcceptBillCreateReq orDefault = resultMap.getOrDefault(shipBillPO, defaultReq);
                            BigDecimal applyNum = shipBatchDetailPO.getShipQty();
                            WdShipAcceptBatchDetailCreateReq temp = new WdShipAcceptBatchDetailCreateReq();
                            temp.setShipInsideId(shipBatchDetailPO.getInsideId());
                            temp.setAcceptQty(applyNum);
                            orDefault.getAcceptDetailList().add(temp);
                            resultMap.put(shipBillPO, orDefault);
                        });
                    } else {
                        BigDecimal receiveGoodsNumOrigin = expireAcceptGoods.getAcceptQty();
                        if (receiveGoodsNumOrigin.compareTo(BigDecimal.ZERO) > 0) {
                            final AtomicDouble receiveGoodsNum = new AtomicDouble(receiveGoodsNumOrigin.doubleValue());
                            // 按照效期信息匹配 优先填充满第一个配送单据  直到 接受数量被使用完毕
                            waitAcceptGoodsMapFromDb.keySet().forEach(billNumber -> {
                                ShipBatchDetailPO shipBatchDetailPO = waitAcceptGoodsMapFromDb.get(billNumber);
                                ShipBillPO shipBillPO = transferOrderMap.get(billNumber);
                                WdShipAcceptBillCreateReq defaultReq = getDefaultReq(batchAcceptReq, shipBillPO);
                                WdShipAcceptBillCreateReq orDefault = resultMap.getOrDefault(shipBillPO, defaultReq);
                                BigDecimal applyNum = shipBatchDetailPO.getShipQty();
                                WdShipAcceptBatchDetailCreateReq temp = new WdShipAcceptBatchDetailCreateReq();
                                temp.setShipInsideId(shipBatchDetailPO.getInsideId());
                                temp.setExpireDate(shipBatchDetailPO.getExpireDate());
                                temp.setProductDate(shipBatchDetailPO.getProductDate());
                                temp.setPeriodBarcode(shipBatchDetailPO.getPeriodBarcode());
                                temp.setPeriodBatchNo(shipBatchDetailPO.getPeriodBatchNo());
                                if (applyNum.doubleValue() <= receiveGoodsNum.get()) {
                                    receiveGoodsNum.addAndGet(-1 * applyNum.doubleValue());
                                    temp.setAcceptQty(applyNum);
                                } else {
                                    receiveGoodsNum.addAndGet(-1 * receiveGoodsNum.get());
                                    temp.setAcceptQty(BigDecimal.valueOf(receiveGoodsNum.get()));
                                }
                                temp.setAcceptQty(shipBatchDetailPO.getShipPrice());
                                orDefault.getAcceptDetailList().add(temp);
                                resultMap.put(shipBillPO, orDefault);
                            });
                            if (receiveGoodsNum.get() > 0) {
                                String format = String.format("商品编码 %s 验收数量 %s 大于所选单据所有商品申请数量只和 %s", goodsCode, receiveGoodsNumOrigin, receiveGoodsNumOrigin.doubleValue() - receiveGoodsNum.get());
                                BizExceptions.throwWithMsg(format);
                            }
                            // 按照 效期条码 效期批号 生产、过期日期  分组
                        } else {
                            // 合并但无验收数量统一填充 0
                            waitAcceptGoodsMapFromDb.keySet().forEach(billNumber -> {
                                ShipBatchDetailPO shipBatchDetailPO = waitAcceptGoodsMapFromDb.get(billNumber);
                                ShipBillPO shipBillPO = transferOrderMap.get(billNumber);
                                WdShipAcceptBillCreateReq defaultReq = getDefaultReq(batchAcceptReq, shipBillPO);
                                WdShipAcceptBillCreateReq orDefault = resultMap.getOrDefault(shipBillPO, defaultReq);
                                WdShipAcceptBatchDetailCreateReq temp = new WdShipAcceptBatchDetailCreateReq();
                                temp.setShipInsideId(shipBatchDetailPO.getInsideId());
                                temp.setExpireDate(shipBatchDetailPO.getExpireDate());
                                temp.setProductDate(shipBatchDetailPO.getProductDate());
                                temp.setPeriodBarcode(shipBatchDetailPO.getPeriodBarcode());
                                temp.setPeriodBatchNo(shipBatchDetailPO.getPeriodBatchNo());
                                temp.setAcceptQty(shipBatchDetailPO.getShipPrice());
                                temp.setAcceptQty(BigDecimal.ZERO);
                                orDefault.getAcceptDetailList().add(temp);
                                resultMap.put(shipBillPO, orDefault);
                            });
                        }
                    }
                });
            });
        }
        return resultMap;
    }

    public String getExpireKey(ShipBatchDetailPO shipBatchDetailPO) {
        return DateUtil.localDateFormateYmd(shipBatchDetailPO.getProductDate()) + "_"
                + DateUtil.localDateFormateYmd(shipBatchDetailPO.getExpireDate()) + "_"
                + shipBatchDetailPO.getPeriodBarcode() + "_" + shipBatchDetailPO.getPeriodBatchNo();
    }


    public String getAcceptReqExpireKey(WdShipAcceptBatchDetailCreateReq acceptBatchDetailCreateReq) {
        return DateUtil.localDateFormateYmd(acceptBatchDetailCreateReq.getProductDate()) + "_" +
                DateUtil.localDateFormateYmd(acceptBatchDetailCreateReq.getExpireDate()) + "_" +
                getBlankStr(acceptBatchDetailCreateReq.getPeriodBarcode()) + "_" +
                getBlankStr(acceptBatchDetailCreateReq.getPeriodBatchNo());
    }

    public boolean acceptReqIsBlankExpire(WdShipAcceptBatchDetailCreateReq acceptBatchDetailCreateReq) {
        return "___".equals(getAcceptReqExpireKey(acceptBatchDetailCreateReq));
    }

    private String getBlankStr(String string) {
        return StringUtils.isBlank(string) ? "" : string;
    }

    private WdShipAcceptBillCreateReq getDefaultReq(WdShipBatchAcceptBillCreateReq batchAcceptReq, ShipBillPO shipBillPO) {
        WdShipAcceptBillCreateReq defaultReq = new WdShipAcceptBillCreateReq();
        defaultReq.setShipBillNo(shipBillPO.getBillNo());
        defaultReq.setOptType(batchAcceptReq.getOptType());
        defaultReq.setRemarkAccept(batchAcceptReq.getRemark());
        defaultReq.setAcceptDetailList(new ArrayList<>());
        return defaultReq;
    }
}
