package com.meta.supplychain.wds.domain.delivery.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meta.supplychain.common.component.service.impl.IWdsAdjustDeliveryOrderCoreService;
import com.meta.supplychain.common.component.service.impl.commonbiz.CommonFranchiseService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.wds.DeliveryOrderBillConvert;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.bds.req.QueryDeptListReq;
import com.meta.supplychain.entity.dto.bds.resp.StoreDetailListResp;
import com.meta.supplychain.entity.dto.bds.resp.StoreDetailResp;
import com.meta.supplychain.entity.dto.common.resp.BillAdjustLogResp;
import com.meta.supplychain.entity.dto.franline.req.FranLineAdjustReq;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.dto.stock.resp.BatchRecordResp;
import com.meta.supplychain.entity.dto.wds.WdsDeliveryAdjustDTO;
import com.meta.supplychain.entity.dto.wds.req.*;
import com.meta.supplychain.entity.dto.wds.resp.*;
import com.meta.supplychain.entity.po.pms.BillAdjustLogPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryFulfillDetailPO;
import com.meta.supplychain.enums.DeptOperateModeEnum;
import com.meta.supplychain.enums.FranLineTypeEnum;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.enums.wds.*;
import com.meta.supplychain.infrastructure.feign.BaseDataSystemFeignClient;
import com.meta.supplychain.infrastructure.repository.service.intf.common.IBillAdjustLogRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsAcceptBillRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchaseOrderRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillDetailRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryFulfillDetailRepository;
import com.meta.supplychain.util.*;
import com.meta.supplychain.wds.domain.delivery.IDeliveryOrderDomainService;
import feign.codec.DecodeException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DeliveryOrderDomainServiceImpl implements IDeliveryOrderDomainService {

    private final IWdDeliveryBillDetailRepository iWdDeliveryBillDetailRepository;

    private final IWdDeliveryFulfillDetailRepository iWdDeliveryFulfillDetailRepository;

    private final IWdDeliveryBillRepository iWdDeliveryBillRepository;

    final UserResourceUtil userResourceUtil;

    private final ISupplychainControlEngineService supplychainControlEngineService;
    private final IWdsAdjustDeliveryOrderCoreService adjustDeliveryOrderCoreService;
    private final BaseDataSystemFeignClient baseDataSystemFeignClient;

    private final DeliveryOrderAuditService deliveryOrderAuditService;

    private final PmsPurchaseOrderRepositoryService pmsPurchaseOrderRepositoryService;

    private final PmsAcceptBillRepositoryService pmsAcceptBillRepositoryService;

    private final IBillAdjustLogRepositoryService billAdjustLogRepositoryService;
    private final UserUtil userUtil;
    final ICommonStockService iCommonStockService;
    final CommonFranchiseService commonFranchiseService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> createDeliveryOrder(WdDeliveryBillCreateParams create) {
        WdDeliveryBillPO wdDeliveryBillPO = DeliveryOrderBillConvert.MAPPER.convertToDeliveryBillPO(create);
        Integer optType = create.getOptType();
        WDDeliveryOptTypeEnum optTypeEnum = WDDeliveryOptTypeEnum.getInstance(optType);
        wdDeliveryBillPO.setStatus(optTypeEnum.getWdDeliveryOrderBillStatusEnum().getCode());
        List<WdDeliveryBillDetailPO> deliveryBillDetailPOList = DeliveryOrderBillConvert.MAPPER.convertToDeliveryBillDetailPOList(create.getDetailList());

        // 填充商品行信息到单据
        doAfterConvertBillAndDetail(wdDeliveryBillPO, deliveryBillDetailPOList);
        // 填充部门信息
        fillDeptInfo(wdDeliveryBillPO);
        if(wdDeliveryBillPO.handleJiaMeng()) {
            // 校验加盟额度
            commonFranchiseService.checkFranLine(wdDeliveryBillPO.getInDeptCode(), wdDeliveryBillPO.getTotalTaxMoney());
        }
        // 入库单据
        try {
            // 入库明细
            return doSaveByOptType(create, wdDeliveryBillPO, deliveryBillDetailPOList, optTypeEnum, false);
        } catch (DataAccessException dataAccessException) {
            Logs.error("保存配送订单入库报错 {}", dataAccessException.getMessage());
            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_001_D003);
        } catch (Exception e) {
            Logs.error("保存配送订单报错 {}", e.getMessage());
            throw e;
        }
        return new Result<>();
    }

    /**
     *  补充部门信息
     *
     * @param deliveryBillPO 配送订单
     */
    private void fillDeptInfo(WdDeliveryBillPO deliveryBillPO) {
        List<String> deptCodeList = Lists.newArrayList();
        deptCodeList.add(deliveryBillPO.getWhCode());
        deptCodeList.add(deliveryBillPO.getInDeptCode());
        StoreDetailListResp storeList = baseDataSystemFeignClient.queryDeptList(QueryDeptListReq.builder().deptCodeList(deptCodeList).build());
        Map<String, StoreDetailResp> storeMap = storeList.getRows().stream().collect(Collectors.toMap(StoreDetailResp::getCode, Function.identity(), (k1, k2) -> k1));
        deliveryBillPO.setDeptOperateMode(storeMap.get(deliveryBillPO.getInDeptCode()).getOperateMode());
    }



    /**
     * 填充商品行信息到单据
     *
     * @param wdDeliveryBillPO
     * @param deliveryBillDetailPOList
     */
    private void doAfterConvertBillAndDetail(WdDeliveryBillPO wdDeliveryBillPO, List<WdDeliveryBillDetailPO> deliveryBillDetailPOList) {
        AtomicLong insideId = new AtomicLong(1);
        deliveryBillDetailPOList.forEach(item -> {
            item.setBillNo(wdDeliveryBillPO.getBillNo());
            item.setWhCode(wdDeliveryBillPO.getWhCode());
            item.setWhName(wdDeliveryBillPO.getWhName());
            item.setInDeptCode(wdDeliveryBillPO.getInDeptCode());
            item.setInDeptName(wdDeliveryBillPO.getInDeptName());
            item.setBillType(wdDeliveryBillPO.getBillType());
            item.setInsideId(insideId.getAndIncrement());

        });
        List<String> skuCodeList = deliveryBillDetailPOList.stream().map(WdDeliveryBillDetailPO::getSkuCode).distinct().collect(Collectors.toList());
        // sku 品种数量
        wdDeliveryBillPO.setTotalSkuCount(skuCodeList.size());

        // 汇总信息
        BigDecimal totalQty = BigDecimal.ZERO;
        BigDecimal totalTaxMoney = BigDecimal.ZERO;
        BigDecimal totalTax = BigDecimal.ZERO;
        for (WdDeliveryBillDetailPO item : deliveryBillDetailPOList) {
            totalQty = totalQty.add(item.getDeliveryQty());
            totalTaxMoney = totalTaxMoney.add(item.getDeliveryTaxMoney());
            totalTax = totalTax.add(item.getDeliveryTax());
        }
        wdDeliveryBillPO.setTotalQty(totalQty);
        wdDeliveryBillPO.setTotalTaxMoney(totalTaxMoney);
        wdDeliveryBillPO.setTotalTax(totalTax);
    }

    public Result<Boolean> doSaveByOptType(WdDeliveryBillCreateParams params, WdDeliveryBillPO wdDeliveryBillPO
            , List<WdDeliveryBillDetailPO> deliveryBillDetailPOList, WDDeliveryOptTypeEnum optTypeEnum,boolean updateFlag) {
        OpInfo opInfo = userUtil.getOpInfoWithThrow();
        //  根据 WDOptTypeEnum 触发不同操作
        if (!updateFlag) {
            wdDeliveryBillPO.setStatus(optTypeEnum.getWdDeliveryOrderBillStatusEnum().getCode());
            iWdDeliveryBillRepository.save(wdDeliveryBillPO);
            iWdDeliveryBillDetailRepository.saveBatch(deliveryBillDetailPOList);
        }else {
            WdDeliveryBillPO update = WdDeliveryBillPO.builder()
                    .id(wdDeliveryBillPO.getId())
                    .status(optTypeEnum.getWdDeliveryOrderBillStatusEnum().getCode())
                    .totalQty(wdDeliveryBillPO.getTotalQty())
                    .totalTax(wdDeliveryBillPO.getTotalTax())
                    .totalTaxMoney(wdDeliveryBillPO.getTotalTaxMoney())
                    .totalSkuCount(wdDeliveryBillPO.getTotalSkuCount())
                    .deptOperateMode(wdDeliveryBillPO.getDeptOperateMode())
                    .build();
            update.setUpdateCode(opInfo.getOperatorCode());
            update.setUpdateName(opInfo.getOperatorName());
            update.setUpdateTime(opInfo.getOperateTime());
            iWdDeliveryBillRepository.updateById(update);
            // 删除原商品信息
            iWdDeliveryBillDetailRepository.deleteByBillNo(wdDeliveryBillPO.getBillNo());
            // 入库新商品信息
            iWdDeliveryBillDetailRepository.saveBatch(deliveryBillDetailPOList);
        }
        if (WDDeliveryOptTypeEnum.AUDIT.equals(optTypeEnum)) {
            // 审核
            wdDeliveryBillPO.setApproveRemark(params.getOptRemark());
            Result<Boolean> booleanResult = deliveryOrderAuditService.doAudit(opInfo, wdDeliveryBillPO, deliveryBillDetailPOList);
            if (!booleanResult.isSuccess()) {
                BizExceptions.throwWithCodeAndMsg(booleanResult.getCode(), booleanResult.getMsg());
            }
        }
        return Results.ofSuccess(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modify(WdDeliveryBillCreateParams modify) {
        // 校验单据状态
        WdDeliveryBillOptParams build = WdDeliveryBillOptParams.builder()
                .optTypeEnum(WDDeliveryOptTypeEnum.MODIFY)
                .billNo(modify.getBillNo()).build();
        optCheck(build);
        WdDeliveryBillPO wdDeliveryBillPO = build.getWdDeliveryBillPO();
        // 商品信息重新生成
        List<WdDeliveryBillDetailPO> deliveryBillDetailPOList = DeliveryOrderBillConvert.MAPPER.convertToDeliveryBillDetailPOList(modify.getDetailList());

        // 填充商品行信息到单据
        doAfterConvertBillAndDetail(wdDeliveryBillPO, deliveryBillDetailPOList);
        // 填充部门信息
        fillDeptInfo(wdDeliveryBillPO);
        if(wdDeliveryBillPO.handleJiaMeng()) {
            // 校验加盟额度
            commonFranchiseService.checkFranLine(wdDeliveryBillPO.getInDeptCode(), wdDeliveryBillPO.getTotalTaxMoney());
        }
        // 编辑后具体操作状态
        Integer optType = modify.getOptType();
        WDDeliveryOptTypeEnum optTypeEnum = WDDeliveryOptTypeEnum.getInstance(optType);
        return doSaveByOptType(modify, wdDeliveryBillPO, deliveryBillDetailPOList, optTypeEnum, true);
    }

    @Override
    public PageResult<WdDeliveryBillResp> queryDeliveryOrderList(WdDeliveryBillQueryReq query) {

        IPage<WdDeliveryBillPO> deliveryOrderHdrPos = iWdDeliveryBillRepository.queryDeliveryOrderBillList(query,
                new Page(query.getCurrent(), query.getPageSize()));
        IPage<WdDeliveryBillResp> convert = deliveryOrderHdrPos.convert(DeliveryOrderBillConvert.MAPPER::convertPo2Vo);
        return new PageResult<>(convert.getTotal(), convert.getRecords());
    }

    @Override
    public PageResult<WdDeliveryBillResp> getPageByCall(WdDeliveryBillQueryByPickReq query) {
        IPage<WdDeliveryBillPO> deliveryOrderHdrPos = iWdDeliveryBillRepository.getPageByCall(query,
                new Page(query.getCurrent(), query.getPageSize()));
        IPage<WdDeliveryBillResp> convert = deliveryOrderHdrPos.convert(DeliveryOrderBillConvert.MAPPER::convertPo2Vo);
        return new PageResult<>(convert.getTotal(), convert.getRecords());
    }

    /**
     * 不同状态单据数量统计接口
     *
     * @return 状态统计
     */
    @Override
    public List<QueryWdDeliveryStatistic> getStateStatistic(WdDeliveryBillQueryReq query) {
        return iWdDeliveryBillRepository.getStateStatistic(query);
    }

    @Override
    public List<WdDeliveryFulfillDetailResult> queryFulfillDetailListByBillNo(String billNo) {

        List<WdDeliveryFulfillDetailPO> fulfillDetailPOList = iWdDeliveryFulfillDetailRepository.queryFulfillDetailListByBillNo(billNo);
        return DeliveryOrderBillConvert.MAPPER.convertDetail2FulfillDetailVOList(fulfillDetailPOList);
    }


    @Override
    public WdDeliveryBillWithDetailForPrintResult queryDeliveryOrderPrintInfo(WdDeliveryBillOptParams query) {
        optCheck(query);
        WdDeliveryBillPO wdDeliveryBillPO = query.getWdDeliveryBillPO();
        List<WdDeliveryBillDetailPO> detailPOList = iWdDeliveryBillDetailRepository.queryDetailListByBillNo(wdDeliveryBillPO.getBillNo());

        // 转换为视图对象

        List<WdDeliveryBillDetailResult> detailResultList = DeliveryOrderBillConvert.MAPPER.convertDetailList2VO(detailPOList);
        boolean showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_WDS_SPPS_VIEW_PRICE_BUTTON));
        WdDeliveryBillResp wdDeliveryBillResp = DeliveryOrderBillConvert.MAPPER.convertPo2Vo(wdDeliveryBillPO);
        if (Boolean.FALSE.equals(showPriceFlag)){
            detailResultList.forEach(detailResult -> {
                detailResult.setDeliveryPrice(SysConstants.ENCRYPT);
                detailResult.setDeliveryTax(SysConstants.ENCRYPT);
                detailResult.setDeliveryTaxMoney(SysConstants.ENCRYPT);
            });
            wdDeliveryBillResp.setTotalTaxMoney(SysConstants.ENCRYPT);
            wdDeliveryBillResp.setTotalTax(SysConstants.ENCRYPT);
        }
        return WdDeliveryBillWithDetailForPrintResult.builder()
                .detailList(detailResultList)
                .billInfo(wdDeliveryBillResp)
                .build();
    }

    @Override
    public List<WdDeliveryBillWithDetailForPrintResult> queryDeliveryOrderPrintInfo(WdDeliveryBillBatchQueryReq query) {

        List<String> billNoList = query.getBillNoList();
        List<WdDeliveryBillPO> wdDeliveryBillPOS = iWdDeliveryBillRepository.queryDeliveryOrderBillByBillNo(billNoList);
        Map<String, WdDeliveryBillPO> deliveryBillPOMap = wdDeliveryBillPOS.stream().collect(Collectors.toMap(WdDeliveryBillPO::getBillNo, Function.identity()));
        List<WdDeliveryBillDetailPO> detailPOList = iWdDeliveryBillDetailRepository.queryDetailListByBillNo(billNoList);
        Map<String, List<WdDeliveryBillDetailPO>> detailMap = detailPOList.stream().collect(Collectors.groupingBy(WdDeliveryBillDetailPO::getBillNo));
        List<WdDeliveryBillWithDetailForPrintResult> collect = deliveryBillPOMap.keySet().stream().map(billNo -> {
            WdDeliveryBillPO wdDeliveryBillPO = deliveryBillPOMap.get(billNo);
            List<WdDeliveryBillDetailPO> deliveryBillDetailPOList = detailMap.get(billNo);
            // 转换为视图对象

            List<WdDeliveryBillDetailResult> detailResultList = DeliveryOrderBillConvert.MAPPER.convertDetailList2VO(deliveryBillDetailPOList);
            return WdDeliveryBillWithDetailForPrintResult.builder()
                    .detailList(detailResultList)
                    .billInfo(DeliveryOrderBillConvert.MAPPER.convertPo2Vo(wdDeliveryBillPO))
                    .build();
        }).collect(Collectors.toList());
        return collect;
    }

    @Override
    public WdDeliveryBillWithDetailResult queryDeliveryOrderHdr(WdDeliveryBillOptParams query) {
        optCheck(query);
        WdDeliveryBillPO wdDeliveryBillPO = query.getWdDeliveryBillPO();
        List<WdDeliveryBillDetailPO> detailPOList = iWdDeliveryBillDetailRepository.queryDetailListByBillNo(wdDeliveryBillPO.getBillNo());

        // 转换为视图对象

        List<WdDeliveryBillDetailResult> detailResultList = DeliveryOrderBillConvert.MAPPER.convertDetailList2VO(detailPOList);

        return WdDeliveryBillWithDetailResult.builder()
                .detailList(detailResultList)
                .billInfo(DeliveryOrderBillConvert.MAPPER.convertPo2Vo(wdDeliveryBillPO))
                .build();
    }

    @Override
    public WdBatchOptResp batchAudit(WdDeliveryBillBatchOptReq audit) {
        //  循环审核  每个事务独立 收集错误信息
        WdBatchOptResp optResp = new WdBatchOptResp();
        audit.getBillNoList().forEach(billNo -> {
            try {
                optCheck(WdDeliveryBillOptParams.builder().billNo(billNo).build());
                WdDeliveryBillPO wdDeliveryBillPO = iWdDeliveryBillRepository.queryDeliveryOrderBillByBillNo(billNo);
                List<WdDeliveryBillDetailPO> detailPOList = iWdDeliveryBillDetailRepository.queryDetailListByBillNo(billNo);
                if (audit.getBillNoList().size() == 1) {
                    wdDeliveryBillPO.setApproveRemark(audit.getOptRemark());
                }
                Result<Boolean> booleanResult = deliveryOrderAuditService.doAuditForBatch(audit.getOpInfo(), wdDeliveryBillPO, detailPOList);
                if (!booleanResult.isSuccess()) {
                    optResp.getFailedInfoList().add(WdBatchOptResp.FailedInfo.builder()
                            .billNo(billNo)
                            .failReason(booleanResult.getMsg())
                            .build());
                    return;
                }
                optResp.setSuccessCount(optResp.getSuccessCount() + 1);
            } catch (Exception e) {
                Logs.error("配送订单审核失败 单号 {} ,原因 ", billNo, e);
                String message = e.getMessage();
                WdBatchOptResp.FailedInfo.FailedInfoBuilder reason = WdBatchOptResp.FailedInfo.builder()
                        .billNo(billNo)
                        .failReason(message);
                optResp.getFailedInfoList().add(reason.build());
            }
        });
        return optResp;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> audit(WdDeliveryBillOptParams audit) {
        optCheck(audit);
        WdDeliveryBillPO wdDeliveryBillPO = audit.getWdDeliveryBillPO();
        List<WdDeliveryBillDetailPO> detailPOList = iWdDeliveryBillDetailRepository.queryDetailListByBillNo(wdDeliveryBillPO.getBillNo());
        wdDeliveryBillPO.setApproveRemark(audit.getOptRemark());
        OpInfo opInfo = userUtil.getOpInfoWithThrow();
        Result<Boolean> booleanResult = deliveryOrderAuditService.doAudit(opInfo, wdDeliveryBillPO, detailPOList);
        if (!booleanResult.isSuccess()) {
            BizExceptions.throwWithCodeAndMsg(booleanResult.getCode(), booleanResult.getMsg());
        }
        return booleanResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> close(WdDeliveryBillOptParams close) {
        optCheck(close);
        OpInfo opInfo = userUtil.getOpInfoWithThrow();
        WdDeliveryBillPO wdDeliveryBillPO = close.getWdDeliveryBillPO();

        // 更新状态为作废
        wdDeliveryBillPO.setStatus(WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_CANCELLED.getCode());

        // 执行更新逻辑
        WdDeliveryBillPO update = WdDeliveryBillPO.builder()
                .billNo(wdDeliveryBillPO.getBillNo())
                .id(wdDeliveryBillPO.getId())
                .status(wdDeliveryBillPO.getStatus())
                .build();

        iWdDeliveryBillRepository.updateById(update);
        FranLineAdjustReq adjustReq = deliveryOrderAuditService.buildFranLineAdjustReq(wdDeliveryBillPO, opInfo);
        try {
            //加盟额度释放
            if(DeptOperateModeEnum.JM.getCode().equals(wdDeliveryBillPO.getDeptOperateMode())){
                adjustReq.setType(FranLineTypeEnum.DELIVERY_CANCEL.getCode());
                commonFranchiseService.adjustFranLine(adjustReq);
            }
        } catch (Exception e) {
            BizExceptionUtil.throwWithErrorCodeAndMsg(WDErrorCodeEnum.SC_WDS_001_P015, "");
        }
        try {
            endDelivery(wdDeliveryBillPO);
        } catch (Exception e) {
            commonFranchiseService.franLineAdjustRollback(adjustReq);
            throw e;
        }
        return Results.ofSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> cancel(WdDeliveryBillOptParams cancel) {
        optCheck(cancel);
        OpInfo opInfo = userUtil.getOpInfoWithThrow();
        WdDeliveryBillPO wdDeliveryBillPO = cancel.getWdDeliveryBillPO();
        // 执行作废逻辑
        wdDeliveryBillPO.setStatus(WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_CANCELLED.getCode());

        // 执行更新逻辑
        WdDeliveryBillPO update = WdDeliveryBillPO.builder()
                .billNo(wdDeliveryBillPO.getBillNo())
                .id(wdDeliveryBillPO.getId())
                .status(wdDeliveryBillPO.getStatus())
                .cancelTime(LocalDateTime.now())
                .cancelManCode(opInfo.getOperatorCode())
                .cancelManName(opInfo.getOperatorName())
                .cancelRemark(cancel.getOptRemark())
                .build();

        iWdDeliveryBillRepository.updateById(update);
        try {
            //加盟额度释放
            if(DeptOperateModeEnum.JM.getCode().equals(wdDeliveryBillPO.getDeptOperateMode())){
                FranLineAdjustReq adjustReq = deliveryOrderAuditService.buildFranLineAdjustReq(wdDeliveryBillPO, opInfo);
                adjustReq.setType(FranLineTypeEnum.DELIVERY_CANCEL.getCode());
                commonFranchiseService.adjustFranLine(adjustReq);
            }
        } catch (Exception e) {
            BizExceptionUtil.throwWithErrorCodeAndMsg(WDErrorCodeEnum.SC_WDS_001_P015, "");
        }
        return new Result<>();
    }

    @Override
    public Result<Boolean> createCheck(WdDeliveryBillCreateParams check) {
        // 判断单据发货日期 大于等于当前日期  小于等于 有效日期
        LocalDate currentDate = LocalDate.now();
        LocalDate deliveryDate = check.getDeliveryDate();
        LocalDate validDate = check.getValidDate();

        if (deliveryDate.isBefore(currentDate)) {
            return Results.of(WDErrorCodeEnum.SC_WDS_001_P001, null);
        }

        if (deliveryDate.isAfter(validDate)) {
            return Results.of(WDErrorCodeEnum.SC_WDS_001_P002, null);
        }
        // 有效日期 大于当前日期
        if (validDate.isBefore(currentDate)) {
            return Results.of(WDErrorCodeEnum.SC_WDS_001_P003, null);
        }
        List<WdDeliveryBillCreateDetail> billCreateDetails = check.getDetailList();

        List<String> bussList = billCreateDetails.stream().map(WdDeliveryBillCreateDetail::getBusinessStatusCode).distinct().collect(Collectors.toList());

        List<String> ciruList = billCreateDetails.stream().map(WdDeliveryBillCreateDetail::getCirculationRouteCode).distinct().collect(Collectors.toList());

        long numSum = billCreateDetails.stream().mapToLong(item -> item.getDeliveryQty().longValue()).sum();
        double moneySum = billCreateDetails.stream().mapToDouble(item -> item.getDeliveryTaxMoney().doubleValue()).sum();
        //  校验阈值
        Integer billDirection = check.getBillDirection();
        BigDecimal maxMoney;
        BigDecimal maxNum;
        if (WDDeliveryOrderDirectionEnum.PEI_SONG_TUI.getCode().equals(billDirection)) {
            maxMoney = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(WDSystemParamEnum.TUI_PEI_DING_DAN_JIN_E_YI_CHANG_FA_ZHI);
            maxNum = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(WDSystemParamEnum.TUI_PEI_DING_DAN_SHU_LIANG_YI_CHANG_FA_ZHI);
        }else {
            maxMoney = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(WDSystemParamEnum.PEI_SONG_DING_DAN_JIN_E_YI_CHANG_FA_ZHI);
            maxNum = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(WDSystemParamEnum.PEI_SONG_DING_DAN_SHU_LIANG_YI_CHANG_FA_ZHI);
        }

        List<String> skuCodeList = billCreateDetails.stream().map(WdDeliveryBillCreateDetail::getSkuKey).distinct().collect(Collectors.toList());
        if (skuCodeList.size() != billCreateDetails.size()) {
            return Results.of(WDErrorCodeEnum.SC_WDS_001_P011, null);
        }

        // 校验部门
        StoreDetailResp inDept = baseDataSystemFeignClient.getSingleDeptInfo(check.getInDeptCode());
        if (Objects.isNull(inDept)) {
            return Results.of(WDErrorCodeEnum.SC_WDS_001_P007, null);
        }
        StoreDetailResp whDept = baseDataSystemFeignClient.getSingleDeptInfo(check.getWhCode());
        if (Objects.isNull(whDept)) {
            return Results.of(WDErrorCodeEnum.SC_WDS_001_P006, null);
        }
        // 门店类型(1:门店2:配送)
        Integer type = inDept.getType();
        check.setBillType(WDDeliveryOrderTypeEnum.INTER_DEPARTMENT_TRANSFER.getCode());
        if (type == 1) {
            check.setBillType(WDDeliveryOrderTypeEnum.WAREHOUSE_DELIVERY.getCode());
        }
        // 校验商品经营状态 流转途径 在目录
        for (WdDeliveryBillCreateDetail detail : billCreateDetails) {
            // 校验商品异常值
            if (detail.getDeliveryQty().compareTo(maxNum) > 0) {
                BizExceptions.throwWithCodeAndMsg(WDErrorCodeEnum.SC_WDS_001_P004.getCode(), "商品：" + detail.getSkuCode() + WDErrorCodeEnum.SC_WDS_001_P004.getDesc());
            }
            if (detail.getDeliveryTaxMoney().compareTo(maxMoney) > 0) {
                BizExceptions.throwWithCodeAndMsg(WDErrorCodeEnum.SC_WDS_001_P005.getCode(), "商品：" + detail.getSkuCode() + WDErrorCodeEnum.SC_WDS_001_P005.getDesc());
            }
        }
        return Results.ofSuccess(true);
    }

    @Override
    public Result<Boolean> print(WdDeliveryBillOptParams print) {
        optCheck(print);
        WdDeliveryBillPO wdDeliveryBillPO = print.getWdDeliveryBillPO();
        LambdaUpdateWrapper<WdDeliveryBillPO> updateWrapper = Wrappers.lambdaUpdate(WdDeliveryBillPO.class);
        updateWrapper.eq(WdDeliveryBillPO::getId, wdDeliveryBillPO.getId());
        updateWrapper.setSql("`print_count` = `print_count` + 1");
        iWdDeliveryBillRepository.update(updateWrapper);
        return Results.ofSuccess(true);
    }

    @Override
    public boolean printBatch(WdDeliveryBillBatchQueryReq batchOptReq) {
        if (CollectionUtils.isEmpty(batchOptReq.getBillNoList())) {
            return false;
        }
        LambdaUpdateWrapper<WdDeliveryBillPO> updateWrapper = Wrappers.lambdaUpdate(WdDeliveryBillPO.class);
        updateWrapper.in(WdDeliveryBillPO::getBillNo, batchOptReq.getBillNoList());
        updateWrapper.setSql("`print_count` = `print_count` + 1");
        return iWdDeliveryBillRepository.update(updateWrapper);
    }

    @Override
    public Result<Boolean> updateDeliveryStatus(DeliveryUpdateRequest updateDeliveryStatus) {
        WdDeliveryBillOptParams query = WdDeliveryBillOptParams.builder()
                .optTypeEnum(WDDeliveryOptTypeEnum.QUERY)
                .billNo(updateDeliveryStatus.getBillNo()).build();
        optCheck(query);
        WdDeliveryBillPO wdDeliveryBillPO = query.getWdDeliveryBillPO();

        List<WdDeliveryBillDetailPO> detailPOList = iWdDeliveryBillDetailRepository.queryDetailListByBillNo(wdDeliveryBillPO.getBillNo());
        Map<Long, WdDeliveryBillDetailPO> detailMap = detailPOList.stream().collect(Collectors.toMap(WdDeliveryBillDetailPO::getInsideId, Function.identity()));
        Map<Long, DeliveryUpdateRequest.GoodsLine> updateMap = updateDeliveryStatus.getItems().stream().collect(Collectors.toMap(DeliveryUpdateRequest.GoodsLine::getInsideId, Function.identity()));
        // 校验发货数据
        if (!new HashSet<>(detailMap.keySet()).containsAll(updateMap.keySet())) {
            return Results.of(WDErrorCodeEnum.SC_WDS_001_P008, null);
        }

        // 更新单据为发货中
        wdDeliveryBillPO.setStatus(WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_SHIPPING.getCode());

        // 执行更新逻辑
        WdDeliveryBillPO update = WdDeliveryBillPO.builder()
                .billNo(wdDeliveryBillPO.getBillNo())
                .id(wdDeliveryBillPO.getId())
                .status(wdDeliveryBillPO.getStatus())
                .build();

        iWdDeliveryBillRepository.updateById(update);
        // 更新 明细行 是否发货  发货数量 发货率
        List<WdDeliveryBillDetailPO> updateDetailList = new ArrayList<>();
        for (Long insideId : updateMap.keySet()) {
            WdDeliveryBillDetailPO wdDeliveryBillDetailPO = detailMap.get(insideId);
            DeliveryUpdateRequest.GoodsLine goodsLine = updateMap.get(insideId);
            BigDecimal deliveryQty = goodsLine.getDeliveryQty();
            if (deliveryQty.compareTo(wdDeliveryBillDetailPO.getDeliveryQty()) > 0) {
                return Results.of(WDErrorCodeEnum.SC_WDS_001_P009, null);
            }

            // 设置发货相关明细信息
            WdDeliveryBillDetailPO build = WdDeliveryBillDetailPO.builder()
                    .insideId(insideId)
                    .id(wdDeliveryBillDetailPO.getId())
                    .shipSign(YesOrNoEnum.YES.getCode())
                    .fulfillRate(deliveryQty
                            .multiply(BigDecimal.valueOf(100))
                            .divide(wdDeliveryBillDetailPO.getWholeQty(), 0, RoundingMode.HALF_UP))
                    .build();
            updateDetailList.add(build);
        }

        iWdDeliveryBillDetailRepository.updateBatchById(updateDetailList);
        return Results.ofSuccess(true);
    }


    public static void main(String[] args) {
        LocalDateTime now = LocalDateTime.now();
        System.out.println(DateUtil.date2LocalDateTime("2025-06-03").isBefore(now));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Result<Boolean> expire(WdDeliveryBillOptParams expire) {
        optCheck(expire);
        OpInfo opInfo = new OpInfo();
        WdDeliveryBillPO wdDeliveryBillPO = expire.getWdDeliveryBillPO();

        // 更新状态为过期
        wdDeliveryBillPO.setStatus(WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_EXPIRED.getCode());

        // 执行更新逻辑
        WdDeliveryBillPO update = WdDeliveryBillPO.builder()
                .billNo(wdDeliveryBillPO.getBillNo())
                .id(wdDeliveryBillPO.getId())
                .status(wdDeliveryBillPO.getStatus())
                .build();

        iWdDeliveryBillRepository.updateById(update);
        FranLineAdjustReq adjustReq = deliveryOrderAuditService.buildFranLineAdjustReq(wdDeliveryBillPO, opInfo);
        try {
            //加盟额度释放
            if(DeptOperateModeEnum.JM.getCode().equals(wdDeliveryBillPO.getDeptOperateMode())){
                adjustReq.setType(FranLineTypeEnum.DELIVERY_CANCEL.getCode());
                commonFranchiseService.adjustFranLine(adjustReq);
            }
        } catch (Exception e) {
            BizExceptionUtil.throwWithErrorCodeAndMsg(WDErrorCodeEnum.SC_WDS_001_P015, "");
        }
        // 库存释放
        // 执行作废相关后续业务操作 库存 批次等
        BatchRecordReq batchRecordReq = null;
        try {
            List<WdDeliveryBillDetailPO> deliveryBillDetailPOList = iWdDeliveryBillDetailRepository.queryDetailListByBillNo(wdDeliveryBillPO.getBillNo());
            // 同步成本、库存
            batchRecordReq = deliveryOrderAuditService.convertStockReq(wdDeliveryBillPO, deliveryBillDetailPOList, WDDeliveryOptTypeEnum.EXPIRE);
            BatchRecordResp batchRecordResp = iCommonStockService.costStockExecute(batchRecordReq);
        } catch (DecodeException e) {
            Logs.error("成本库存同步失败，{}", e.getMessage());
//            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_001_P010);
            iCommonStockService.costStockRollback(batchRecordReq);
            deliveryOrderAuditService.franLineRollback(wdDeliveryBillPO, adjustReq);
        }

        return Results.ofSuccess(true);
    }

    @Override
    public Result<Boolean> optCheck(WdDeliveryBillOptParams check) {

        // 校验单据是否存在
        WdDeliveryBillPO wdDeliveryBillPO = iWdDeliveryBillRepository.queryDeliveryOrderBillByBillNo(check.getBillNo());

        if (Objects.isNull(wdDeliveryBillPO)) {
            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_001_D001);
        }
        check.setWdDeliveryBillPO(wdDeliveryBillPO);
        WDDeliveryOptTypeEnum optTypeEnum = check.getOptTypeEnum();
        Integer status = wdDeliveryBillPO.getStatus();
        if (Objects.nonNull(optTypeEnum)) {
            switch (optTypeEnum) {
                case MODIFY:
                    // 校验 哪些状态允许编辑
                    if (!WDDeliveryOrderBillStatusEnum.allowModify(status)) {
                        BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_001_D002);
                    }
                    break;
                case EXPIRE:
                    break;
                case CANCEL:
                    //  哪些状态允许作废
                    if (!WDDeliveryOrderBillStatusEnum.allowCancel(status)) {
                        BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_001_D004);
                    }
                    break;
                case AUDIT:
                    break;
                case CLOSE:
                    //  哪些状态允许手动关闭
                    if (!WDDeliveryOrderBillStatusEnum.allowClose(status)) {
                        BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_001_D005);
                    }
                    break;
                case ADJUST:
                    if (!WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_APPROVED.getCode().equals(status)) {
                        BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_001_D006);
                    }
                default:
            }
        }
        return Results.ofSuccess(true);
    }

    @Override
    public List<BillAdjustLogResp> getAdjustLog(String billNo) {
        List<BillAdjustLogResp> resps = new ArrayList<>();
        List<BillAdjustLogPO> adjustLog = billAdjustLogRepositoryService.getAdjustLog(billNo);
        Map<String, List<BillAdjustLogPO>> collect = adjustLog.stream().collect(Collectors.groupingBy(BillAdjustLogPO::getBillNo));
        collect.keySet().forEach(item -> {
            BillAdjustLogResp resp = BillAdjustLogResp.builder().build();
            resp.setAdjustBillNo(item);
            resp.setBillType(collect.get(item).get(0).getBillType());
            resp.setBillSource(collect.get(item).get(0).getBillSource());
            resp.setRefBillNo(collect.get(item).get(0).getRefBillNo());
            resp.setRemark(collect.get(item).get(0).getRemark());
            resp.setCreateUid(collect.get(item).get(0).getCreateUid());
            resp.setCreateCode(collect.get(item).get(0).getCreateCode());
            resp.setCreateName(collect.get(item).get(0).getCreateName());
            resp.setCreateTime(collect.get(item).get(0).getCreateTime());
            resp.setAdjustLogList(collect.get(item));
            resps.add(resp);
        });
        List<BillAdjustLogResp> respList = resps.stream().sorted(Comparator.comparing(BillAdjustLogResp::getCreateTime).reversed()).collect(Collectors.toList());
        return respList;
    }

    @Override
    public WdsDeliveryOrderAdjustResp adjustDeliveryBill(WdsDeliveryOrderAdjustReq wdsDeliveryOrderAdjustReq) {
        WdDeliveryBillOptParams query = WdDeliveryBillOptParams.builder()
                .optTypeEnum(WDDeliveryOptTypeEnum.ADJUST)
                .billNo(wdsDeliveryOrderAdjustReq.getBillNo()).build();
        optCheck(query);
        WdDeliveryBillPO wdDeliveryBillPO = query.getWdDeliveryBillPO();
        try {
            OpInfo operatorInfo = userUtil.getOpInfoWithThrow();
            //单据状态 已审核+未预约
            Boolean adjustCondition = WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_APPROVED.getCode().equals(wdDeliveryBillPO.getStatus());
            if (!adjustCondition) {
                return new WdsDeliveryOrderAdjustResp(wdDeliveryBillPO.getBillNo(), WDErrorCodeEnum.SC_WDS_001_P012.getDesc());
            }

            //原商品明细
            List<WdDeliveryBillDetailPO> billDetailPOList = iWdDeliveryBillDetailRepository.queryDetailListByBillNo(wdDeliveryBillPO.getBillNo());

            Map<Long, WdDeliveryBillDetailPO> detailAlreadyMap = billDetailPOList.stream().collect(Collectors.toMap(WdDeliveryBillDetailPO::getInsideId, Function.identity()));


            String adjustBillNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.ADJUST_LOG, wdDeliveryBillPO.getInDeptCode());

            WdsDeliveryAdjustDTO wdsDeliveryAdjustDTO = adjustDeliveryOrderCoreService.assembleAdjustReq(wdsDeliveryOrderAdjustReq, detailAlreadyMap);
            wdsDeliveryAdjustDTO.setAdjustBillNo(adjustBillNo);
            wdsDeliveryAdjustDTO.setOldBill(wdDeliveryBillPO);
            wdsDeliveryAdjustDTO.setNewBill(CglibCopier.copy(wdDeliveryBillPO, WdDeliveryBillPO.class));
            List<BillAdjustLogPO> billAdjustLog = adjustDeliveryOrderCoreService.assembleChangeInfo(wdsDeliveryAdjustDTO);
            adjustDeliveryOrderCoreService.doAdjust(wdsDeliveryAdjustDTO, operatorInfo, billAdjustLog);
            return new WdsDeliveryOrderAdjustResp(wdDeliveryBillPO.getBillNo());
        } catch (BizException | DecodeException e) {
            return new WdsDeliveryOrderAdjustResp("请求库存异常，" + e.getMessage(), wdDeliveryBillPO.getBillNo());
        } catch (Exception e) {
            Logs.error("配送单 {} 调整异常 ", wdDeliveryBillPO.getBillNo(), e);
            return new WdsDeliveryOrderAdjustResp("调整异常", wdDeliveryBillPO.getBillNo());
        }
    }

    @Override
    public Result<Boolean> endDelivery(WdDeliveryBillPO wdDeliveryBillPO) {
        // 库存释放
        // 执行作废相关后续业务操作 库存 批次等
        BatchRecordReq batchRecordReq = null;
        try {
            List<WdDeliveryBillDetailPO> deliveryBillDetailPOList = iWdDeliveryBillDetailRepository.queryDetailListByBillNo(wdDeliveryBillPO.getBillNo());
            // 同步成本、库存
            batchRecordReq = deliveryOrderAuditService.convertStockReq(wdDeliveryBillPO, deliveryBillDetailPOList, WDDeliveryOptTypeEnum.DELIVERY_END);
            if (batchRecordReq != null) {
                BatchRecordResp batchRecordResp = iCommonStockService.costStockExecute(batchRecordReq);
            }
        } catch (BizException | DecodeException e) {
            Logs.error("成本库存同步失败，{}", e.getMessage());
//            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_001_P010);
//            iCommonStockService.costStockRollback(batchRecordReq);
            throw e;
        }
        return Results.ofSuccess(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Result<Boolean> saveFulfillDeliveryInfo(List<FulfillDeliveryInfoRequest> requestList) {
        OpInfo opInfo = new OpInfo();
        List<String> collect = requestList.stream().map(FulfillDeliveryInfoRequest::getBillNo).distinct().collect(Collectors.toList());
        List<WdDeliveryBillPO> wdDeliveryBillPOList = iWdDeliveryBillRepository.queryDeliveryOrderBillByBillNo(collect);
        Map<String, WdDeliveryBillPO> deliveryBillPOMap = wdDeliveryBillPOList.stream().collect(Collectors.toMap(WdDeliveryBillPO::getBillNo, Function.identity()));
        List<WdDeliveryBillDetailPO> detailPOList = iWdDeliveryBillDetailRepository.queryDetailListByBillNo(collect);
        Map<String, List<WdDeliveryBillDetailPO>> detailAllMap = detailPOList.stream().collect(Collectors.groupingBy(WdDeliveryBillDetailPO::getBillNo));
        List<WdDeliveryFulfillDetailPO> fulfillDetailPOList = new ArrayList<>();
        requestList.forEach(fulfillDeliveryInfoRequest -> {
            WdDeliveryBillPO wdDeliveryBillPO = deliveryBillPOMap.get(fulfillDeliveryInfoRequest.getBillNo());
            List<WdDeliveryBillDetailPO> deliveryBillDetailPOList = detailAllMap.get(fulfillDeliveryInfoRequest.getBillNo());
            Map<Long, WdDeliveryBillDetailPO> detailMap = deliveryBillDetailPOList.stream().collect(Collectors.toMap(WdDeliveryBillDetailPO::getInsideId, Function.identity()));
            for (FulfillDeliveryInfoRequest.GoodsLine goodsLine : fulfillDeliveryInfoRequest.getItems()) {
                WdDeliveryBillDetailPO wdDeliveryBillDetailPO = detailMap.get(goodsLine.getInsideId());
                // 新增履行明细表
                WdDeliveryFulfillDetailPO build = WdDeliveryFulfillDetailPO.builder()
                        .billNo(wdDeliveryBillPO.getBillNo())
                        .whCode(wdDeliveryBillPO.getWhCode())
                        .whName(wdDeliveryBillPO.getWhName())
                        .inDeptCode(wdDeliveryBillPO.getInDeptCode())
                        .inDeptName(wdDeliveryBillPO.getInDeptName())
                        .insideId(goodsLine.getInsideId())
                        .skuCode(wdDeliveryBillDetailPO.getSkuCode())
                        .skuName(wdDeliveryBillDetailPO.getSkuName())
                        .skuType(wdDeliveryBillDetailPO.getSkuType())
                        .deliveryQty(wdDeliveryBillDetailPO.getDeliveryQty())
                        .deliveryPrice(wdDeliveryBillDetailPO.getDeliveryPrice())
                        .shipBillNo(goodsLine.getShipBillNo())
                        .shipInsideId(goodsLine.getShipInsideId())
                        .shipQty(goodsLine.getShipQty())
                        .shipPrice(wdDeliveryBillDetailPO.getDeliveryPrice())
                        .shipTaxMoney(wdDeliveryBillDetailPO.getDeliveryPrice().multiply(goodsLine.getShipQty()))
                        .acceptBillNo(goodsLine.getAcceptBillNo())
                        .acceptInsideId(goodsLine.getAcceptInsideId())
                        .acceptQty(goodsLine.getAcceptQty())
                        .shipTime(goodsLine.getShipTime())
                        .shipManName(goodsLine.getShipManName())
                        .shipManCode(goodsLine.getShipManCode())
                        .shipCreateTime(goodsLine.getShipCreateTime())
                        .shipCreateCode(goodsLine.getShipCreateCode())
                        .shipCreateName(goodsLine.getShipCreateName())
                        .shipCreateUid(goodsLine.getShipCreateUid())
                        .build();
                build.setCreateCode(opInfo.getOperatorCode());
                build.setCreateName(opInfo.getOperatorName());
                build.setUpdateCode(opInfo.getOperatorCode());
                build.setUpdateName(opInfo.getOperatorName());
                fulfillDetailPOList.add(build);
            }
        });
        //更新配送订单状态发货中，发货完成
        List<String> billNoSending = new ArrayList<>();
        List<String> billNoSendFinish = new ArrayList<>();
        requestList.forEach(fulfillDeliveryInfoRequest -> {
            if (YesOrNoEnum.YES.getCode().equals(fulfillDeliveryInfoRequest.getDeliveryFlag())) {
                billNoSending.add(fulfillDeliveryInfoRequest.getBillNo());
            } else {
                billNoSendFinish.add(fulfillDeliveryInfoRequest.getBillNo());
            }
        });
        if (!billNoSending.isEmpty()) {
            iWdDeliveryBillRepository.lambdaUpdate()
                    .in(WdDeliveryBillPO::getBillNo, billNoSending)
                    .set(WdDeliveryBillPO::getStatus, WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_SHIPPING.getCode()).update();
        }
        if (!billNoSendFinish.isEmpty()) {
            iWdDeliveryBillRepository.lambdaUpdate()
                    .in(WdDeliveryBillPO::getBillNo, billNoSendFinish)
                    .set(WdDeliveryBillPO::getStatus, WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_SHIPPED.getCode()).update();
        }
        iWdDeliveryFulfillDetailRepository.saveBatch(fulfillDetailPOList);

        //  触发履行率计算
        collect.forEach(item->{
            calDeliveryOrderFulfillRate(item, opInfo);});

         return Results.ofSuccess(true);
    }

    /**
     * 计算配送单履行率
     *
     * @param billNo
     */
    public void calDeliveryOrderFulfillRate(String billNo, OpInfo opInfo) {
        try {
            List<WdDeliveryFulfillDetailPO> wdDeliveryFulfillDetailPOList = iWdDeliveryFulfillDetailRepository.queryFulfillDetailListByBillNo(billNo);
            List<WdDeliveryBillDetailPO> wdDeliveryBillDetailPOList = iWdDeliveryBillDetailRepository.queryDetailListByBillNo(billNo);
            Map<Long, WdDeliveryBillDetailPO> detailMap = wdDeliveryBillDetailPOList.stream().collect(Collectors.toMap(WdDeliveryBillDetailPO::getInsideId, Function.identity()));
            Map<Long, Double> shipQtyMap = wdDeliveryFulfillDetailPOList.stream()
                    .collect(Collectors.groupingBy(WdDeliveryFulfillDetailPO::getInsideId, Collectors.summingDouble(item -> MoneyUtil.bigDecimal2Double(item.getShipQty()))));
            List<WdDeliveryBillDetailPO> updateList = new ArrayList<>();
            for (Long insideId : shipQtyMap.keySet()) {
                WdDeliveryBillDetailPO wdDeliveryBillDetailPO = detailMap.get(insideId);
                Double shipQtySum = shipQtyMap.get(insideId);
                WdDeliveryBillDetailPO build = WdDeliveryBillDetailPO.builder()
                        .id(wdDeliveryBillDetailPO.getId())
                        .lastFulfillTime(LocalDateTime.now())
                        .fulfillRate(BigDecimal.valueOf(shipQtySum)
                                .divide(wdDeliveryBillDetailPO.getDeliveryQty(), 4, RoundingMode.HALF_UP)
                                .multiply(MoneyUtil.ONE_HUNDRED)
                        )
                        .shipQty(BigDecimal.valueOf(shipQtySum))
                        .build();
                build.setUpdateCode(opInfo.getOperatorCode());
                build.setUpdateName(opInfo.getOperatorName());
                updateList.add(build);
            }
            iWdDeliveryBillDetailRepository.updateBatchById(updateList);
        } catch (Exception e) {
            Logs.error("配送单 {} 履行率计算报错", billNo, e);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> fulfillDeliveryInfo(FulfillDeliveryInfoRequest fulfillDeliveryInfoRequest) {
        WdDeliveryBillOptParams query = WdDeliveryBillOptParams.builder()
                .optTypeEnum(WDDeliveryOptTypeEnum.QUERY)
                .billNo(fulfillDeliveryInfoRequest.getBillNo()).build();
        optCheck(query);
        WdDeliveryBillPO wdDeliveryBillPO = query.getWdDeliveryBillPO();
        fulfillDeliveryInfoRequest.setWdDeliveryBillPO(wdDeliveryBillPO);
        saveFulfillDeliveryInfo(Collections.singletonList(fulfillDeliveryInfoRequest));
        //  终止履行时同步库存
        if (YesOrNoEnum.YES.getCode().equals(fulfillDeliveryInfoRequest.getEndFlag())) {
            endDelivery(wdDeliveryBillPO);
        }
        return Results.ofSuccess(true);
    }

}
