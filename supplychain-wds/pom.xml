<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>supplychain</artifactId>
        <groupId>com.meta.supplychain</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.meta.supplychain.wds</groupId>
    <artifactId>supplychain-wds</artifactId>

    <name>supplychain-wds</name>
    <dependencies>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-alarm</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-lock</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-app-center</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-common</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-logger</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-monitor</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-rest</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>cn.linkkids.framework.croods</groupId>
            <artifactId>croods-monitor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meta.supplychain.infrastructure</groupId>
            <artifactId>supplychain-infrastructure</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.meta.supplychain.common.component</groupId>
            <artifactId>supplychain-component</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.meta.supplychain.util</groupId>
            <artifactId>supplychain-util</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.meta.supplychain.entity</groupId>
            <artifactId>supplychain-entity</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>supplychain-wds</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>lombok-maven-plugin</artifactId>
                <groupId>org.projectlombok</groupId>
            </plugin>
        </plugins>
    </build>
</project>
