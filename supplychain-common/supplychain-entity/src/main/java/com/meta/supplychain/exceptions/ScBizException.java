package com.meta.supplychain.exceptions;

import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.ErrorCode;

/**
 * 供应链业务异常
 * <AUTHOR>
 */
public class ScBizException extends BizException {

    public ScBizException(ErrorCode errorCode, String tips) {
        super(errorCode.getErrorCode(), String.format("[%s]%s", errorCode.getErrorMsg(), tips));
    }

    public ScBizException(ErrorCode errorCode, Object[] args) {
        super(errorCode.getErrorCode(), errorCode.getErrorMsg(), args);
    }

    public ScBizException(ErrorCode errorCode) {
        super(errorCode);
    }
}
