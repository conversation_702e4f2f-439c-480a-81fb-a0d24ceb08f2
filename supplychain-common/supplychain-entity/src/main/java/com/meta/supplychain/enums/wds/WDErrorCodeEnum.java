package com.meta.supplychain.enums.wds;

import cn.linkkids.framework.croods.common.exception.ErrorCode;
import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "仓储配送错误状态码枚举", code = "SCWDErrorCodeEnum")
public enum WDErrorCodeEnum implements VerifiableEnum<String>, ErrorCode {
    /**
     * #错误码示例：SCMD001P001
     * #SC(领域)MD(子系统)001(模块代码)B(异常类型)001(具体异常码)
     * #B-业务错误
     * #P-参数错误
     * #N-网络错误
     * #D-数据库错误
     * #F-文件IO错误
     * #U-未知错误
     * #SCMD001P001  SCMD 供应链主数据  001模块代码 001错误代码  001:费用管理,002合同管理
     * #SC(领域-供应链)WDS(子系统-仓储配送)001(模块代码-波次管理)P(参数异常)001 SCWDS001P001  SCWDS 供应商仓库配送
     * #SC(领域-供应链)PMS(子系统-订货采购)001(模块代码-订货申请)P(参数异常)001 SCPMS001P001  SCPMS 供应商仓库配送
     * SCWDS001P001  供应链配送管理
     * SCWDS002P001  供应链波次管理
     * SCWDS003P001  供应链拣货管理
     * SCWDS004P001  供应链配送发货管理
     * SCWDS005P001  供应链配送验收管理
     * SCWDS006P001  供应链退配收货管理
     * SCWDS007P001  供应链仓库配送差异管理
     * SCWDS008P001  供应链仓库商品移位管理
     * SCWDS009P001  供应链加工拆分管理
     * SCWDS010P001  供应链调拨管理
     *
     */

    // 配送订单
    SC_WDS_001_D001("SCWDS001D001","不存在该单据"),
    SC_WDS_001_D002("SCWDS001D002","单据状态不允许编辑"),
    SC_WDS_001_D003("SCWDS001D003","保存配送订单出错"),
    SC_WDS_001_D004("SCWDS001D004","单据仅草稿，待审核允许作废"),
    SC_WDS_001_D005("SCWDS001D005","单据仅 已审核/发货中/已装箱 允许手动关闭"),
    SC_WDS_001_D006("SCWDS001D006","单据仅 已审核 允许调整"),


    SC_WDS_001_P001("SCWDS00P001","发货日期不能小于当前日期"),
    SC_WDS_001_P002("SCWDS00P002","发货日期不能大于有效日期"),
    SC_WDS_001_P003("SCWDS00P003","有效日期必须大于当前日期"),
    SC_WDS_001_P004("SCWDS00P004","配送订单数量超过异常阈值"),
    SC_WDS_001_P005("SCWDS00P005","配送订单金额超过异常阈值"),
    SC_WDS_001_P006("SCWDS00P006","播出部门不存在"),
    SC_WDS_001_P007("SCWDS00P007","拨入部门不存在"),
    SC_WDS_001_P008("SCWDS00P008","发货数据有误,存在多余商品行号"),
    SC_WDS_001_P009("SCWDS00P009","发货数量不能大于商品数量"),
    SC_WDS_001_P010("SCWDS00P010","配送订单同步库存报错"),
    SC_WDS_001_P011("SCWDS00P011","配送订单不允许存在同一个品存在多行"),


    SC_WDS_001_P012("SCWDS00P012","配送订单只允许已审核状态单据调整"),
    SC_WDS_001_P013("SCWDS00P013","配送订单加盟额度不足，请检查"),
    SC_WDS_001_P014("SCWDS00P014","配送订单加盟额度扣减失败"),
    SC_WDS_001_P015("SCWDS00P015","配送订单加盟额度释放失败"),
    SC_WDS_001_P016("SCWDS00P016","配送订单调整失败"),

    // 配送验收管理
    SC_WDS_005_P001("SCWDS005P001","单据号不能为空"),
    SC_WDS_005_P002("SCWDS005P002","操作类型值不存在，0 暂存 1 提交 2 审核"),

    SC_WDS_005_D001("SCWDS005D001","验收的配送单不存在"),
    SC_WDS_005_D002("SCWDS005D002","仅已发货、验收中、验收待审核状态配送单可以验收"),
    SC_WDS_005_D003("SCWDS005D003","配送单不存在未验收的商品"),
    SC_WDS_005_D004("SCWDS005D004","配送单存在多个未验收中的单据"),
    SC_WDS_005_D005("SCWDS005D005","配送单不允许重复验收"),
    SC_WDS_005_D006("SCWDS005D006","配送验收单不存在"),
    SC_WDS_005_D007("SCWDS005D007","配送验收处理异常"),
    SC_WDS_005_D008("SCWDS007D003","配送验收单同步库存失败"),
    // 差异处理
    SC_WDS_007_D001("SCWDS007D001","差异处理单不存在"),
    SC_WDS_007_D002("SCWDS007D002","差异处理单仅待处理状态支持审核"),
    SC_WDS_007_D003("SCWDS007D003","差异单同步库存失败"),




    // 退货收货
    SC_WDS_006_P001("SCWDS006P001","退配收货单不存在"),

    SC_WDS_006_D001("SCWDS006D001","退配收货单仅草稿状态允许修改"),
    SC_WDS_006_D002("SCWDS006D002","退配收货单仅待收货状态允许收货"),
    SC_WDS_006_D003("SCWDS006D003","退配收货单仅待收货状态允许作废"),
    SC_WDS_006_D004("SCWDS006D004","退货收货同步库存报错"),


    WD_BIZ_ERROR_002U001("SCWDS002U001","系统未知异常"),
    WD_BIZ_ERROR_002U002("SCWDS002U002","未定义操作类型"),
    WD_BIZ_ERROR_002P001("SCWDS002P001","缺少波次单号入参"),
    WD_BIZ_ERROR_002B001("SCWDS002B001","未查询到波次单信息"),
    WD_BIZ_ERROR_002B002("SCWDS002B002","订单【%s】状态发生变化，请重新调入"),
    WD_BIZ_ERROR_002B003("SCWDS002B003","波次状态非待处理，不可作废"),
    WD_BIZ_ERROR_002B004("SCWDS002B004","波次状态非待处理，订单对照不可变更"),
    WD_BIZ_ERROR_002B005("SCWDS002B005","波次状态非待处理，不可拣货分配"),
    WD_BIZ_ERROR_002B006("SCWDS002B006","拣货分配处理中，请稍后处理"),
    WD_BIZ_ERROR_002B007("SCWDS002B007","未查到有效储位库存"),
    WD_BIZ_ERROR_002B008("SCWDS002B008","波次未查到对照订单明细"),
    WD_BIZ_ERROR_002B009("SCWDS002B009","波次状态非已分配，不可反分配"),
    WD_BIZ_ERROR_002B010("SCWDS002B010","存在已拣货的拣货单，不可反分配，拣货单号：%s"),
    WD_BIZ_ERROR_002B011("SCWDS002B011","存在未验收直流采购订单，请先验收，直流采购订单号：%s"),
    WD_BIZ_ERROR_002B012("SCWDS002B012","存在已冲红直流验收单，请重新选择，采购订单号：%s"),
    WD_BIZ_ERROR_002B013("SCWDS002B013","拣货分配订单非已审核，配送订单号：%s"),
    //拣货
    WD_BIZ_ERROR_003U001("SCWDS003U001","系统未知异常"),
    WD_BIZ_ERROR_003P001("SCWDS003P001","缺少拣货单号入参"),
    WD_BIZ_ERROR_003B001("SCWDS003B001","未查询到拣货单信息"),
    WD_BIZ_ERROR_003B002("SCWDS003B002","未查询到拣货单明细信息"),
    WD_BIZ_ERROR_003B003("SCWDS003B003","拣货单状态非待拣货，不可拣货"),
    WD_BIZ_ERROR_003B004("SCWDS003B004","拣货单状态非已拣货，不可发货"),
    WD_BIZ_ERROR_003B005("SCWDS003B005","整单拣货数量为0，不可拣货确认"),
    WD_BIZ_ERROR_003B006("SCWDS003B006","拣货确认中，请稍后重试"),
    WD_BIZ_ERROR_003B007("SCWDS003B007","拣货数量不允许超过分配数量"),
    //配送发货
    WD_BIZ_ERROR_004P001("SCWDS004P001","缺少配送发货单号入参"),
    WD_BIZ_ERROR_004B001("SCWDS004B001","未查询到配送发货单信息"),
    WD_BIZ_ERROR_004B002("SCWDS004B002","未查询到配送发货单明细信息"),
    WD_BIZ_ERROR_004B003("SCWDS004B003","配送发货单已存在"),
    WD_BIZ_ERROR_004B004("SCWDS004B004","配送发货单不是待发货状态"),
    WD_BIZ_ERROR_004B005("SCWDS004B005","配送发货库存不足，%s"),
    WD_BIZ_ERROR_004B006("SCWDS004B006","状态非待发货，不可作废"),
    WD_BIZ_ERROR_004B007("SCWDS004B007","当前状态，不可冲红"),
    WD_BIZ_ERROR_004B008("SCWDS004B008","调配送订单状态已变更，请重新选择"),
    WD_BIZ_ERROR_004B009("SCWDS004B009","商品未取到配送价，%s"),
    //商品移位
    WD_BIZ_ERROR_008P001("SCWDS008P001","缺少移位单号入参"),
    WD_BIZ_ERROR_008B001("SCWDS008B001","未查询到移位单信息"),
    WD_BIZ_ERROR_008B002("SCWDS008B002","未查询到移位单明细信息"),
    WD_BIZ_ERROR_008B003("SCWDS008B003","移位单已存在"),
    WD_BIZ_ERROR_008B004("SCWDS008B004","移位单不是待审核状态"),
    WD_BIZ_ERROR_008B005("SCWDS008B005","状态非待审核，不可作废"),;

    private final String code;
    private final String desc;

    @Override
    public String getErrorCode() {
        return code;
    }

    @Override
    public String getErrorMsg() {
        return desc;
    }

    public String getErrorMsgFormat(String errorParam) {
        return String.format( this.desc, errorParam);
    }
}
