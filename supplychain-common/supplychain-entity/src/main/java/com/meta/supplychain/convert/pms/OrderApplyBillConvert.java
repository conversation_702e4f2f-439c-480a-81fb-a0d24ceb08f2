package com.meta.supplychain.convert.pms;

import com.meta.supplychain.entity.dto.pms.req.apply.ApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.ApplyBillGoodsDTO;
import com.meta.supplychain.entity.dto.pms.resp.ApplyBillDemandResp;
import com.meta.supplychain.entity.dto.pms.resp.apply.ApplyStatisticResp;
import com.meta.supplychain.entity.dto.pms.view.ApplyBillExportView;
import com.meta.supplychain.entity.po.pms.ApplyStatisticPO;
import com.meta.supplychain.entity.po.pms.PmsApplyBillGoodsPO;
import com.meta.supplychain.entity.po.pms.PmsApplyBillPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface OrderApplyBillConvert {
    OrderApplyBillConvert MAPPER = Mappers.getMapper(OrderApplyBillConvert.class);

    ApplyBillDTO convertPO2DTO(PmsApplyBillPO applyBillDTO);
    ApplyBillExportView convertPO2ExportView(PmsApplyBillPO applyBillDTO);
    List<ApplyBillExportView> convertPO2ExportViews(List<PmsApplyBillPO> applyBillDTO);

    List<ApplyBillDTO> convertPO2DTOList(List<PmsApplyBillPO> pmsApplyBillPOS);


    PmsApplyBillPO convertDTO2PO(ApplyBillDTO applyBillDTO);

    ApplyStatisticResp convertCountPO2Resp(ApplyStatisticPO applyStatisticPO);

    List<ApplyStatisticResp> convertCountPOList2Resp(List<ApplyStatisticPO> applyStatisticPOList);

    ApplyBillGoodsDTO convertGoodsPO2Resp(PmsApplyBillGoodsPO pmsApplyBillGoodsPO);

    List<ApplyBillGoodsDTO> convertGoodsPOList2Resp(List<PmsApplyBillGoodsPO> pmsApplyBillGoodsPOS);

    ApplyBillDemandResp convertDemandPO2Resp(PmsApplyBillPO pmsApplyBillPO);

    List<ApplyBillDemandResp> convertDemandPOList2Resp(List<PmsApplyBillPO> pmsApplyBillPOS);




}
