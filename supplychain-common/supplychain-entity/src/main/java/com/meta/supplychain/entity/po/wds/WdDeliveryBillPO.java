package com.meta.supplychain.entity.po.wds;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import com.meta.supplychain.enums.DeptOperateModeEnum;
import com.meta.supplychain.enums.wds.WDDeliveryOrderDirectionEnum;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@TableName(value ="wd_delivery_bill")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WdDeliveryBillPO extends BaseEntity {

    /** id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 仓库编码 */
    private String whCode;

    /** 仓库名称 */
    private String whName;

    /** 配送单据号 */
    private String billNo;

    /** 入货部门编码 */
    private String inDeptCode;

    /** 入货部门名称 */
    private String inDeptName;

    /**
     * 门店经营模式 1 直营 2 加盟
     */
    private Integer deptOperateMode;

    /** 收货联系人 */
    private String contactMan;

    /** 收货联系电话 */
    private String contactTel;

    /** 收货联系地址 */
    private String contactAddr;

    /** 附件名称与地址,json格式[{"name":"","url":""}] */
    private String attachmentUrl;

    /** 单据方向 -1退货 1正向 */
    private Integer billDirection;

    /** 配送单据类型 1.仓库配送、2.仓间调拨 */
    private Integer billType;

    /**  配送单来源 0 手工 1 需求单 */
    private Integer billSource;

    /** 单据状态 -1处理中 0草稿 1待审核 2审核 3已分配 4发货中 5已发货 6 已装箱 7已过期 9已作废 */
    private Integer status;

    /** 收货标记 0 未收货 1已收货 */
    private Integer acceptSign;

    /** 是否被调用 0 否 1是 */
    private Integer callSign;

    /** 订单有效日期 */
    private LocalDate validDate;

    /** 订单送货日期 */
    private LocalDate deliveryDate;
    /**
     * 送货方式 0-到店，1-到客户
     */
    private Integer sendMode;

    /** 原因编码 */
    private String causeCode;

    /** 原因名称 */
    private String causeName;

    /** 订货属性编码 */
    private String orderAttributeCode;

    /** 订货属性名称 */
    private String orderAttributeName;

    /** 关联单号 */
    private String srcBillNo;

    /** 关联单号备注 */
    private String srcBillRemark;

    /** 是否直流[0 非直流 1 直流 ] */
    private Integer directSign;
    
    /** 直接采购单号 */
    private String directPurchaseBillNo;

    /** 备注 */
    private String remark;

    /** 打印次数 */
    private Integer printCount;

    /** 商品品项数 */
    private Integer totalSkuCount;

    /** 配送订单总数量 */
    private BigDecimal totalQty;

    /** 配送订单总金额 */
    private BigDecimal totalTaxMoney;

    /** 配送订单总税金 */
    private BigDecimal totalTax;

    /** 需求批次 */
    private String purchBatchNo;

    /** 作废时间 */
    private LocalDateTime cancelTime;

    /** 作废人编码 */
    private String cancelManCode;

    /** 作废人名称 */
    private String cancelManName;

    /** 作废备注 */
    private String cancelRemark;

    /** 审核时间 */
    private LocalDateTime approveTime;

    /** 审核人编码 */
    private String approveManCode;

    /** 审核人名称 */
    private String approveManName;
    
    /** 审核备注 */
    private String approveRemark;
    
    /** 客户编码 */
    private String customerCode;
    
    /** 客户名称 */
    private String customerName;
    
    /** 供应商编码 */
    private String supplierCode;
    
    /** 供应商名称 */
    private String supplierName;

    /**
     * 是否需要处理加盟
     *
     * @return
     */
    public boolean handleJiaMeng() {
        return DeptOperateModeEnum.JM.getCode().equals(getDeptOperateMode())
                && !WDDeliveryOrderDirectionEnum.PEI_SONG_TUI.getCode().equals(getBillDirection());
    }
}