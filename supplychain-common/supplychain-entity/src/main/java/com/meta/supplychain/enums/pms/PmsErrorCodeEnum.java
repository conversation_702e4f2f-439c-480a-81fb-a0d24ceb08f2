package com.meta.supplychain.enums.pms;

import cn.linkkids.framework.croods.common.exception.ErrorCode;
import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "订货采购管理错误状态码枚举", code = "PmsErrorCodeEnum")
public enum PmsErrorCodeEnum implements VerifiableEnum<String>, ErrorCode {
    /**
     * #错误码示例：SCMD001P001
     * #SC(领域)MD(子系统)001(模块代码)B(异常类型)001(具体异常码)
     * #B-业务错误
     * #P-参数错误
     * #N-网络错误
     * #D-数据库错误
     * #F-文件IO错误
     * #U-未知错误
     * #SCMD001P001  SCMD 供应链主数据  SCPMS 订货采购管理 001模块代码 001错误代码  001:费用管理,002合同管理
     * #SC(领域-供应链)PMS(子系统-订货采购)000(模块代码-共同模块)P(参数异常)001 SCPMS000P001
     * #SC(领域-供应链)PMS(子系统-订货采购)001(模块代码-订货申请管理)P(参数异常)001 SCPMS001P001
     * #SC(领域-供应链)PMS(子系统-订货采购)002(模块代码-需求单管理)P(参数异常)001  SCPMS002P001
     * #SC(领域-供应链)PMS(子系统-订货采购)003(模块代码-采购订单管理)P(参数异常)001 SCPMS003P001
     * #SC(领域-供应链)PMS(子系统-订货采购)004(模块代码-采购验收管理)P(参数异常)001 SCPMS004P001
     * #SC(领域-供应链)PMS(子系统-订货采购)005(模块代码-采购计划单管理)P(参数异常)001 SCPMS005P001
     * #SC(领域-供应链)PMS(子系统-订货采购)006(模块代码-供应链预约单)P(参数异常)001 SCPMS006P001
     * #SC(领域-供应链)PMS(子系统-订货采购)007(模块代码-采购调整)P(参数异常)001 SCPMS007P001
     * SCPMS001P001  订货申请管理
     * SCPMS002P001  需求单管理
     * SCPMS003P001  采购订单管理
     * SCPMS004P001  采购验收管理
     * SCPMS005P001  采购计划单管理
     * SCPMS006P001  供应链预约单
     * SCPMS007P001  采购调整
     * SCPMS008P001  配转采
     * SCPMS999P001  公共业务
     */

    /**P-参数错误**/
    SC_PMS_000_P001("SC_PMS_000_P001","单号为空，请检查"),
    SC_PMS_000_P002("SC_PMS_000_P002","不存在该单据"),
    SC_PMS_000_P003("SC_PMS_000_P003","不存在该单据详情信息"),
    SC_PMS_000_P004("SC_PMS_000_P004","送货日期不能小于当前日期"),
    SC_PMS_000_P005("SC_PMS_000_P005","有效日期不能小于当前日期"),
    SC_PMS_000_P006("SC_PMS_000_P006","送货日期不能晚于有效日期"),
    SC_PMS_000_P007("SC_PMS_000_P007","数量超过异常阀值"),
    SC_PMS_000_P008("SC_PMS_000_P008","金额超过异常阀值"),
    SC_PMS_000_P010("SC_PMS_000_P010","无有效待审核单据"),
    SC_PMS_000_P011("SC_PMS_000_P011","无有效作废单据"),

    SC_PMS_001_P012("SC_PMS_001_P012","申请单据已存在"),
    SC_PMS_005_P001("SC_PMS_005_P001","有效日期不能小于当前日期"),
    SC_PMS_005_P002("SC_PMS_005_P002","数量超过异常阀值"),
    SC_PMS_005_P003("SC_PMS_005_P003","金额超过异常阀值"),
    SC_PMS_005_P004("SC_PMS_005_P004","不存在该单据"),
    SC_PMS_005_P005("SC_PMS_005_P005","单据已存在"),
    SC_PMS_004_P001("SC_PMS_004_P001","当前单据不存在"),
    SC_PMS_004_P002("SC_PMS_004_P002","当前单据不可取消"),
    SC_PMS_004_P003("SC_PMS_004_P003","当前用户无作废权限"),
    SC_PMS_004_P004("SC_PMS_004_P004","当前采购订单不存在"),
    SC_PMS_004_P005("SC_PMS_004_P005","到客户采购申请请在批销系统验收"),
    SC_PMS_004_P006("SC_PMS_004_P006","超过订货数量异常阀值"),
    SC_PMS_004_P007("SC_PMS_004_P007","超过订货金额异常阀值"),
    SC_PMS_004_P008("SC_PMS_004_P008","当前单据商品经营状态和流转途径校验失败"),
    SC_PMS_004_P009("SC_PMS_004_P009","当前单据商品在目录校验失败"),
    SC_PMS_004_P010("SC_PMS_004_P010","当前单据不支持增加商品和数量"),
    SC_PMS_004_P011("SC_PMS_004_P011","当前单据状态不可修改"),
    SC_PMS_004_P012("SC_PMS_004_P012","当前用户无冲红权限"),
    SC_PMS_004_P013("SC_PMS_004_P013","当前无可冲红单据"),
    SC_PMS_004_P014("SC_PMS_004_P014","创建采购验收单失败"),
    SC_PMS_004_P015("SC_PMS_004_P015","修正验收单失败"),
    SC_PMS_004_P016("SC_PMS_004_P016","审核验收单失败"),
    SC_PMS_004_P017("SC_PMS_004_P017","更新验收单失败"),
    SC_PMS_004_P018("SC_PMS_004_P018","作废验收单失败"),
    SC_PMS_004_P019("SC_PMS_004_P019","冲红验收单失败"),
    SC_PMS_004_P020("SC_PMS_004_P020","当前采购单商品不存在"),
    SC_PMS_004_P021("SC_PMS_004_P021","当前申请单正在操作中"),
    SC_PMS_004_P022("SC_PMS_004_P022","联营管库存商品不支持代配过账"),
    /**B-业务错误**/
    SC_PMS_001_B001("SC_PMS_001_B001","该单据状态不允许编辑"),
    SC_PMS_001_B002("SC_PMS_001_B002","不存在可审核状态单据"),
    SC_PMS_001_B005("SC_PMS_001_B005","商品不存在"),
    SC_PMS_001_B006("SC_PMS_001_B006","商品流转途径非直流"),
    SC_PMS_001_B007("SC_PMS_001_B007","没有有效的合同商品定义"),
    SC_PMS_001_B008("SC_PMS_001_B008","直流供应商没有有效的合同商品定义"),
    SC_PMS_001_B009("SC_PMS_001_B009","不是商品流转途径设定的直流供应商"),
    SC_PMS_001_B010("SC_PMS_001_B010","超过订货数量异常阀值"),
    SC_PMS_001_B011("SC_PMS_001_B011","超过订货金额异常阀值"),
    SC_PMS_001_B014("SC_PMS_001_B014","审核后不可新增商品"),
    SC_PMS_001_B015("SC_PMS_001_B015","存在重复商品"),
    SC_PMS_001_B017("SC_PMS_001_B017","该单据状态不允许作废"),
    SC_PMS_001_B018("SC_PMS_001_B018","库存占用失败"),
    SC_PMS_001_B019("SC_PMS_001_B019","不允许负库存"),
    SC_PMS_001_B020("SC_PMS_001_B020","超出订单订货策略时间阈值"),
    /**B-业务错误 --- 采购订单 **/
    SC_PMS_003_B001("SC_PMS_003_B001","该单据状态不允许编辑"),
    SC_PMS_003_B002("SC_PMS_003_B002","该单据状态不允许审核"),
    SC_PMS_003_B003("SC_PMS_003_B003","采购订单部分审核失败"),
    SC_PMS_003_B004("SC_PMS_003_B004","采购订单部分取消失败"),
    SC_PMS_003_P005("SC_PMS_003_P005","订货属性不可为团购/追加/追减"),
    SC_PMS_003_B012("SC_PMS_003_B012","仅未发货未确认的已审核订单可进行确认收货"),
    SC_PMS_003_B013("SC_PMS_003_B013","仅未发货已审核的采购订单可标记发货"),
    SC_PMS_003_B016("SC_PMS_003_B016","仅已审核+未预约可进行采购订单调整"),
    SC_PMS_003_B017("SC_PMS_003_B017","商品行调整金额信息为空，请检查"),
    SC_PMS_003_B018("SC_PMS_003_B018","调入的采购计划单不存在，请检查"),
    SC_PMS_003_B019("SC_PMS_003_B019","调用计划单校验异常"),
    SC_PMS_003_B020("SC_PMS_003_B020","采购订单明细不存在"),
    SC_PMS_003_B021("SC_PMS_003_B021","订货申请加盟额度扣减失败"),
    SC_PMS_003_B022("SC_PMS_003_B022","订货申请加盟额度释放失败"),
    SC_PMS_003_B023("SC_PMS_003_B023","单号[{}]调整失败,原因:{}"),



    /**B-业务错误 --- 需求单 **/
    SC_PMS_002_B001("SC_PMS_002_B001","需求单不存在"),
    SC_PMS_002_B002("SC_PMS_002_B002","需求单暂存失败"),
    SC_PMS_002_B003("SC_PMS_002_B003","商品类型[{}]商品编码[{}]部门[{}]配转采不允许有出货方-供应商"),
    SC_PMS_002_B004("SC_PMS_002_B004","需求单组装数据失败"),
    SC_PMS_002_B005("SC_PMS_002_B005","需求单正在处理中，请稍后再试"),
    SC_PMS_002_B006("SC_PMS_002_B006","需求单配转采保存失败，请重试"),
    SC_PMS_002_B007("SC_PMS_002_B007","需求单清空配转采失败，请重试"),
    SC_PMS_002_B008("SC_PMS_002_B008","需求单响应的订货申请已经被其他单据响应"),
    SC_PMS_002_B009("SC_PMS_002_B009","需求单作废失败"),
    SC_PMS_002_B010("SC_PMS_002_B010","需求单提交失败"),
    SC_PMS_002_B011("SC_PMS_002_B011","需求单作废失败，不是草稿状态"),
    SC_PMS_002_B012("SC_PMS_002_B012","需求单中的门店[{}]未查到"),
    SC_PMS_002_B013("SC_PMS_002_B013","需求单中的门店[{}]商品[{}]出货方-供应商不能为空"),
    SC_PMS_002_B014("SC_PMS_002_B014","需求单中的门店[{}]商品[{}]出货方-配送部门必须为空"),
    SC_PMS_002_B015("SC_PMS_002_B015","需求单中的门店[{}]商品[{}]出货方-配送部门不能为空"),
    SC_PMS_002_B016("SC_PMS_002_B016","需求单中的门店[{}]商品[{}]出货方-配送部门只能有一条"),
    SC_PMS_002_B017("SC_PMS_002_B017","需求单中的门店[{}]商品[{}]属于直流商品,需要出货方-配送一条，出货方-供应商一条"),
    SC_PMS_002_B018("SC_PMS_002_B018","需求单中的门店[{}]商品[{}]属于配转采,需要出货方-配送一条，出货方-供应商多条"),
    SC_PMS_002_B019("SC_PMS_002_B019","需求单中商品行数最大支持{}条,当前{}条"),
    SC_PMS_002_B020("SC_PMS_002_B020","需求单提交自动转单配送订单成功{}条"),
    SC_PMS_002_B021("SC_PMS_002_B021","需求单提交自动转单配送订单成功{}条,失败配送订单号{},请手工转单"),
    SC_PMS_002_B022("SC_PMS_002_B022","订货申请单号{}商品{}是直流,但是部门商品不是直流"),
    SC_PMS_002_B023("SC_PMS_002_B023","订货申请单号{}商品{}是直流供应商为{},部门商品直流供应商为{}"),
    SC_PMS_002_B024("SC_PMS_002_B024","订货申请单号{}商品{}无效的经营状态"),
    SC_PMS_002_B025("SC_PMS_002_B025","订货申请单号{}商品{}无效的流转途径"),
    SC_PMS_002_B026("SC_PMS_002_B026","订货申请单号{}商品{}经营状态需要允许进货，允许进货退货"),
    SC_PMS_002_B027("SC_PMS_002_B027","订货申请单号{}商品{}流转途径需要允许供应商送货，允许退供应商"),
    SC_PMS_002_B028("SC_PMS_002_B028","订货申请单号{}商品{}流转途径需要允许配送送货，允许退配送"),
    SC_PMS_002_B029("SC_PMS_002_B029","只有追加追减的单据才能生成需求单"),
    SC_PMS_002_B030("SC_PMS_002_B030","自动需求单更新订货申请行为已作废状态"),
    SC_PMS_002_B031("SC_PMS_002_B031","手工需求单更新订货申请行为已作废状态"),
    SC_PMS_002_B032("SC_PMS_002_B032","需求单作废时更新订货申请行为已作废状态"),
    SC_PMS_002_B033("SC_PMS_002_B033","需求单自动配送部门{}商品{}未查到有效的合同商品信息"),
    SC_PMS_002_B034("SC_PMS_002_B034","需求单提交成功，审核失败，请手工转采购配送订单"),
    SC_PMS_002_B035("SC_PMS_002_B035","需求单订货明细不能为空"),
    SC_PMS_002_B036("SC_PMS_002_B036","需求单部门{}商品{}的出货方-供应商未选择"),
    SC_PMS_002_B037("SC_PMS_002_B037","需求单中的门店[{}]商品[{}]订货明细中的响应数量不能为0或者空"),
    SC_PMS_002_B038("SC_PMS_002_B038","需求单中的门店[{}]商品[{}]已选中配送出货方的配送金额不能为0或者空"),
    SC_PMS_002_B039("SC_PMS_002_B039","需求单转采购订单自动审核失败"),
    SC_PMS_002_B040("SC_PMS_002_B040","需求单转配送订单自动审核失败"),
    SC_PMS_002_B041("SC_PMS_002_B041","需求单中的门店[{}]商品[{}]的出货方-配送部门和出货方-供应商不能同时为空"),
    SC_PMS_002_B042("SC_PMS_002_B042","需求单中的门店[{}]商品[{}]的出货方-配送部门和出货方-供应商不能同时未选中"),
    SC_PMS_002_B043("SC_PMS_002_B043","需求单中的门店[{}]商品[{}]已选中配送出货方的配送金额不能为空"),

    /**B-业务错误 --- 配转采 **/
    SC_PMS_008_B001("SC_PMS_008_B001","经营状态不是允许进货或者流转途径不是允许供应商送货"),
    SC_PMS_008_B002("SC_PMS_008_B002","不存在有效的供应商"),
    SC_PMS_008_B003("SC_PMS_008_B003","商品不存在"),
    SC_PMS_008_B004("SC_PMS_008_B004","没有可转采的配送订单"),
    SC_PMS_008_B005("SC_PMS_008_B005","直流商品{}不可配转采"),
    SC_PMS_008_B006("SC_PMS_008_B006","商品{}品类编码不可为空"),
    SC_PMS_008_B007("SC_PMS_008_B007","部门{}商品{}单内序号不可为空"),
    SC_PMS_008_B008("SC_PMS_008_B008","部门{}商品{}单内序号与配送出货方{}的上级序号不同"),

    /**B-业务错误 --- 公共业务 **/
    SC_PMS_999_B001("SC_PMS_999_B001","单据保存失败"),

    /**B-业务错误 --- 采购计划单 **/
    SC_PMS_005_B001("SC_PMS_005_B001","商品不存在"),
    SC_PMS_005_B002("SC_PMS_005_B002","商品[{}]不存在"),
    SC_PMS_005_B003("SC_PMS_005_B003","采购价格必须大于0"),
    SC_PMS_005_B004("SC_PMS_005_B004","非称重品数量不能为小数"),
    SC_PMS_005_B005("SC_PMS_005_B005","计划采购数量范围0~9999999"),
    SC_PMS_005_B006("SC_PMS_005_B006","合同号不存在"),
    SC_PMS_005_B007("SC_PMS_005_B007","计划整件数量不能为小数"),
    SC_PMS_005_B008("SC_PMS_005_B008","计划单状态不是草稿或待审核，不允许审核!"),
    SC_PMS_005_B009("SC_PMS_005_B009","计划单状态不是草稿或待审核，不允许作废!"),
    SC_PMS_005_B0010("SC_PMS_005_B0010","计划单状态不是已审核或使用中，不允许过期!"),
    SC_PMS_005_B0011("SC_PMS_005_B0011","计划单状态不是已审核或使用中，不允许使用!"),
    SC_PMS_005_B0012("SC_PMS_005_B0012","商品[{}]剩余可采数量不足"),
    SC_PMS_005_B0013("SC_PMS_005_B0013","修改信息不能为空"),
    SC_PMS_005_B0014("SC_PMS_005_B0014","商品[{}]不存在"),
    SC_PMS_005_B0015("SC_PMS_005_B0015","保存采购计划单失败"),
    SC_PMS_005_B0016("SC_PMS_005_B0016","审核采购计划单失败"),
    SC_PMS_005_B0017("SC_PMS_005_B0017","取消采购计划单失败"),
    SC_PMS_005_B0018("SC_PMS_005_B0018","过期采购计划单失败"),
    SC_PMS_005_B0019("SC_PMS_005_B0019","修改采购计划数量失败"),
    SC_PMS_005_B0020("SC_PMS_005_B0020","关闭采购计划单失败"),
    SC_PMS_005_B0021("SC_PMS_005_B0021","计划单状态不是已审核或使用中，不允许关闭!"),
    SC_PMS_005_B0022("SC_PMS_005_B0022","采购计划单部分审核失败"),
    SC_PMS_005_B0023("SC_PMS_005_B0023","采购计划单明细不允许重复"),
    SC_PMS_005_B0024("SC_PMS_005_B024","计划单状态不是待审核，不允许审核!"),

    /**D-数据库错误**/

    SC_PMS_001_D001("SC_PMS_001_D001","供应链订货采购业务异常"),
    SC_PMS_005_D001("SC_PMS_005_D001","当前操作正在处理，请稍后重试..."),

    /**U-未知错误**/
    SC_PMS_001_U001("SC_PMS_001_U001","系统未知异常");

    private final String code;
    private final String desc;

    @Override
    public String getErrorCode() {
        return code;
    }

    @Override
    public String getErrorMsg() {
        return desc;
    }

    public String getErrorMsgFormat(String errorParam) {
        return String.format( this.desc+",param：%s", errorParam);
    }
}
