package com.meta.supplychain.enums;

import java.util.*;

/**
 * 标准枚举
 * <AUTHOR>
 */
public interface StandardEnum<T> {
    /**
     * 获取code属性值
     * <AUTHOR>
     * @return code 字段值
     */
    T getCode();
    /**
     * 获取desc描述信息
     * <AUTHOR>
     * @return desc 字段值
     */
    String getDesc();

    /**
     * 获取指定枚举 code 的实例
     * <AUTHOR>
     * @param enumType 枚举类 Class
     * @param value code值
     * @return 指定枚举实例
     */
    @SuppressWarnings("all")
    static <X> X codeOf(Class<X> enumType, Object value) {
        if (!enumType.isEnum() || !StandardEnum.class.isAssignableFrom(enumType) || Objects.isNull(value)) {
            return null;
        }
        return Arrays.stream(enumType.getEnumConstants()).filter(enumValue -> Objects.equals(((StandardEnum)enumValue).getCode(), value)).findFirst().orElse(null);
    }
    static <X> Optional<X> codeOfOptional(Class<X> enumType, Object value) {
        return Optional.ofNullable(codeOf(enumType, value));
    }

    /**
     * 获取指定枚举 code 的实例
     * <AUTHOR>
     * @param enumType 枚举类 Class
     * @param value desc值
     * @return 指定枚举实例
     */
    @SuppressWarnings("all")
    static <X> X descOf(Class<X> enumType, Object value) {
        if (!enumType.isEnum() || !StandardEnum.class.isAssignableFrom(enumType) || Objects.isNull(value)) {
            return null;
        }
        return Arrays.stream(enumType.getEnumConstants()).filter(enumValue -> Objects.equals(((StandardEnum)enumValue).getDesc(), value)).findFirst().orElse(null);
    }
    static <X> Optional<X> descOfOptional(Class<X> enumType, Object value) {
        return Optional.ofNullable(descOf(enumType, value));
    }

    /**
     * 当前枚举实例是否通过api暴露
     * <AUTHOR>
     */
    default boolean export2Api() {
        return true;
    }
}
