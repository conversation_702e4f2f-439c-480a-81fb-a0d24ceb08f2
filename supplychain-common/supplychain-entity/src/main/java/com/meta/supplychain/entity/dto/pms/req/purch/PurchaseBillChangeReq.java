package com.meta.supplychain.entity.dto.pms.req.purch;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.meta.supplychain.entity.dto.OpInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 采购订单请求信息
 * 新增 更新 审核
 */
@Schema(description = "采购订单请求信息")
@Data
public class PurchaseBillChangeReq {
    private OpInfo operatorInfo = new OpInfo();

    @Schema(description = "操作类型 0 暂存 1 提交  2 审核")
    private Integer optType;

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "采购订单号")
    @NotBlank(message = "采购订单号不能为空")
    private String billNo;

    @Schema(description = "部门编码")
    @NotBlank(message = "部门编码不能为空")
    private String deptCode;

    @Schema(description = "部门名称")
    @NotBlank(message = "部门名称不能为空")
    private String deptName;

    @Schema(description = "门店经营模式（1:直营，2:加盟）")
    private Integer deptOperateMode = 1;

    @Schema(description = "供应商编码")
    @NotBlank(message = "供应商编码不能为空")
    private String supplierCode;

    @Schema(description = "供应商名称")
    @NotBlank(message = "供应商名称不能为空")
    private String supplierName;

    @Schema(description = "单据类别（-1:采退，1:采购）")
    @NotNull(message = "单据类别不能为空")
    private Integer billDirection;

    @Schema(description = "采购类型 0-门店采购，1-配送采购")
    @NotNull(message = "采购类型不能为空")
    private Integer billType;

    @Schema(description = "订货属性编码")
    private String orderAttributeCode;

    @Schema(description = "订货属性名称")
    private String orderAttributeName;

    @Schema(description = "送货方式 0-到店，1-到客户，默认到店")
    private Integer sendMode = 0;

    @Schema(description = "送货日期 yyyy-MM-dd")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN, timezone = "GMT+8")
    private LocalDate deliverDate;

    @Schema(description = "有效日期 yyyy-MM-dd")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN, timezone = "GMT+8")
    private LocalDate validityDate;

    @Schema(description = "停靠点编码")
    private String dockCode;

    @Schema(description = "停靠点名称")
    private String dockName;

    @Schema(description = "联系人")
    private String contactMan;

    @Schema(description = "联系地址")
    private String contactAddr;

    @Schema(description = "联系电话")
    private String contactTel;

    @Schema(description = "采购订单备注")
    private String purchRemark;

    @Schema(description = "退货原因")
    private String refundReason;

    @Schema(description = "退货原因描述")
    private String refundReasonDesc;

    @Schema(description = "合同号")
    @NotBlank(message = "合同号不能为空")
    private String contractNo;

    @Schema(description = "来源 0-手工单，1-需求单，2-配转采")
    private Integer billSource = 0;

    @Schema(description = "来源单号")
    private String srcBillNo;

    @Schema(description = "来源单据备注")
    private String srcRemark;

    @Schema(description = "采购计划单号")
    private String planBillNo;

    @Schema(description = "需求批次")
    private String purchBatchNo;

    @Schema(description = "审核备注")
    private String auditRemark;

    @Schema(description = "是否直流订单 0-非直流 1-直流")
    private Integer directSign = 0;

    @Schema(description = "是否配转采0-否 1-是")
    private Integer transferPurchSign = 0;

    @Schema(description = "商品品项数")
    private Integer totalSkuCount;

    @Schema(description = "采购总数量")
    private BigDecimal totalQty;

    @Schema(description = "采购总金额")
    private BigDecimal totalTaxMoney;

    @Schema(description = "采购总税金")
    private BigDecimal totalTax;

    /**
     * 附件名称与地址,json格式[{"name":"","url":""}]
     */
    @Schema(description = "附件名称与地址,json格式[{\"name\":\"\",\"url\":\"\"}]")
    private String attachmentUrl;

    @Schema(description="管理分类编码")
    private String manageCategoryCode;

    @Schema(description="管理分类名称")
    private String manageCategoryName;

    @Schema(description="管理分类项编码")
    private String manageCategoryClass;

    /**
     * 商品信息
     */
    @NotEmpty(message = "商品信息不能为空")
    @Schema(description = "商品信息列表")
    @Valid
    List<PurchaseBillDetailCreateReq> detailList;
}
