package com.meta.supplychain.entity.dto.pms.req.purch;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 采购订单商品明细请求信息
 * 新增 更新
 */
@Schema(description = "采购订单商品明细请求信息")
@Data
public class PurchaseBillDetailCreateReq {
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "采购订单号")
    private String billNo;

    @Schema(description = "单内序号")
    @NotNull(message = "单内序号不能为空")
    private Long insideId;

    @Schema(description = "商品类型 0商品1附赠商品2附赠赠品")
    @NotNull(message = "商品类型不能为空")
    private Integer skuType;

    @Schema(description = "商品编码")
    @NotBlank(message = "商品编码不能为空")
    private String skuCode;

    @Schema(description = "商品名称")
    @NotBlank(message = "商品名称不能为空")
    private String skuName;

    @Schema(description = "商品条码")
    @NotBlank(message = "商品条码不能为空")
    private String barcode;

    @Schema(description = "商品货号")
    private String goodsNo;

    @Schema(description = "品类编码")
    @NotBlank(message = "品类编码不能为空")
    private String categoryCode;

    @Schema(description = "品类名称")
    private String categoryName;

    @Schema(description = "品牌编码")
    @NotBlank(message = "品牌编码不能为空")
    private String brandCode;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "单位")
    private String basicUnit;

    @Schema(description = "整件单位")
    private String packageUnit;

    @Schema(description = "规格")
    private String skuModel;

    @Schema(description = "销售模式 1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码")
    @NotNull(message = "销售模式不能为空")
    private Integer saleMode = 1;

    @Schema(description = "进项税率")
    @NotNull(message = "进项税率不能为空")
    private BigDecimal inputTaxRate = BigDecimal.ZERO;

    @Schema(description = "销项税率")
    @NotNull(message = "销项税率不能为空")
    private BigDecimal outputTaxRate = BigDecimal.ZERO;

    @Schema(description = "计量属性 0：普通 1：计量 2：称重")
    @NotNull(message = "计量属性不能为空")
    private Integer uomAttr;

    @Schema(description = "商品包装率")
    private BigDecimal unitRate;

    @Schema(description = "订货包装率")
    @NotNull(message = "订货包装率不能为空")
    private BigDecimal purchUnitRate;

    @Schema(description = "促销期进价")
    private BigDecimal promotePeriodPrice;

    @Schema(description = "促销活动编码")
    private String promoteActivityCode;

    @Schema(description = "促销活动名称")
    private String promoteActivityName;

    @Schema(description = "合同号")
    private String contractNo;

    @Schema(description = "合同特供价")
    private BigDecimal contractSpecialPrice;

    @Schema(description = "合同进价")
    private BigDecimal contractPrice;

    @Schema(description = "合同最高进价")
    private BigDecimal contractMaxPrice;

    @Schema(description = "最后进价")
    private BigDecimal lastPurchPrice;

    @Schema(description = "采购价格")
    @NotNull(message = "采购价格不能为空")
    private BigDecimal purchPrice;

    @Schema(description = "部门库存")
    private BigDecimal stockQty;

    @Schema(description = "部门可用库存")
    private BigDecimal atpQty;

    @Schema(description = "采购计划可采数量")
    private BigDecimal planReqQty;

    @Schema(description = "整件数量")
    private BigDecimal wholeQty;

    @Schema(description = "零头数量")
    private BigDecimal oddQty;

    @Schema(description = "采购数量")
    @NotNull(message = "采购数量不能为空")
    private BigDecimal purchQty;

    @Schema(description = "采购金额")
    @NotNull(message = "采购金额不能为空")
    private BigDecimal purchMoney;

    @Schema(description = "采购税金")
    @NotNull(message = "采购税金不能为空")
    private BigDecimal purchTax;

    @Schema(description = "零售单价")
    private BigDecimal salePrice;

    @Schema(description = "零售金额")
    private BigDecimal saleMoney;

    @Schema(description = "效期商品标识 1是 0否")
    private Integer periodFlag = 0;

    @Schema(description = "效期条码")
    private String periodBarcode;

    @Schema(description = "效期批号")
    private String periodBatchNo;

    @Schema(description = "效期生产日期")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
    private LocalDate productDate;

    @Schema(description = "效期到期日期")
    @JsonFormat(pattern = LocalDates.DEFAULT_DATE_PATTERN)
    private LocalDate expireDate;

    @Schema(description = "来源 0-手工单，1-需求单，2-配转采")
    private Integer billSource = 0;

    @Schema(description = "来源单据类型")
    private String srcBillType;

    @Schema(description = "来源单号")
    private String srcBillNo;

    @Schema(description = "来源单单内序号")
    private Long srcInsideId;

    @Schema(description = "需求批次")
    private String purchBatchNo;

    @Schema(description="管理分类编码")
    private String manageCategoryCode;

    @Schema(description="管理分类名称")
    private String manageCategoryName;

    @Schema(description="管理分类项编码")
    private String manageCategoryClass;
}
