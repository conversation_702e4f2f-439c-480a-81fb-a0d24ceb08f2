package com.meta.supplychain.util.message;

import cn.linkkids.framework.croods.common.context.ApplicationContextHolder;
import cn.linkkids.framework.croods.common.exception.ErrorCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/09 14:18
 **/
@Component
public class MsgUtils {

//    @Autowired
//    private static MessageSource messageSource;

    /**
     * 指定语言获得单个国际化翻译值
     * @param msgKey    国际化文件key值
     * @param args      其他参数，可为null
     */
    public static String getMsg(String msgKey, Object... args) {
        Locale locale = new Locale("zh", "CN");

        MessageSource messageSource = ApplicationContextHolder.get(MessageSource.class);
        return messageSource.getMessage(msgKey, args, locale);
    }

}