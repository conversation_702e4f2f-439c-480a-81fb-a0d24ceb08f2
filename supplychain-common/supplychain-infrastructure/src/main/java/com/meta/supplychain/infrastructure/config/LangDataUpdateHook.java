package com.meta.supplychain.infrastructure.config;

import cn.linkkids.framework.business.gull.language.manager.ErrorMessageManager;
import cn.linkkids.framework.croods.common.logger.Logs;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.*;

/**
 * 多语言上报hook
 */
@Component
public class LangDataUpdateHook implements ApplicationRunner {

    @Resource
    private ErrorMessageManager errorMessageManager;

    @Override
    public void run(ApplicationArguments args) {
        ExecutorService tempThreadPool = Executors.newCachedThreadPool();
        try {
            CompletableFuture.runAsync(() -> {
                Logs.info("开始上报多语言错误信息");
                errorMessageManager.pushErrorMessage(false);
                Logs.info("上报多语言错误信息结束");
            }, tempThreadPool).handleAsync((r, throwable) -> {
                if (throwable != null) {
                    Logs.error("错误信息上报处理失败：", throwable);
                }
                return null;
            }, tempThreadPool).get(2, TimeUnit.MINUTES);
        } catch (Exception e) {
            Logs.error("多语言错误信息上报执行失败", e);
        } finally {
            tempThreadPool.shutdown();
        }
    }
}
