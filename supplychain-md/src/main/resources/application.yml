spring:
  profiles:
    active: local
  application:
    name: supplychain-md
  messages:
    #在i18n文件下message开头的配置文件
    basename: i18n/messages
    #配置缓存的时间，单位 s
    cache-duration: 10
    #指定编码格式
    encoding: UTF-8
  # 优雅关闭配置
  lifecycle:
    timeout-per-shutdown-phase: 60s
server:
  servlet:
    context-path: /
  # 配置优雅关闭
  shutdown: graceful
logback:
  path: /data/logs/supplychain-md


unit-deploy:
  prefix-main: /supplychain-md
  prefix-inner: /supplychain-md-inner

springdoc:
  use-management-port: false
  api-docs:
    path: /supplychain-md/v3/api-docs
  swagger-ui:
    disable-swagger-default-url: true
    path: /supplychain-md/swagger-ui.html
    config-url: /supplychain-md/v3/api-docs/swagger-config
    urls:
      - url: /supplychain-md/v3/api-docs
        name: "供应链-主数据API"

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0
