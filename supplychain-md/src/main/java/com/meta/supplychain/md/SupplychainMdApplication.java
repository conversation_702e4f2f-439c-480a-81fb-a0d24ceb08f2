package com.meta.supplychain.md;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@MapperScan("com.meta.supplychain.infrastructure.repository.mapper.*")
@ComponentScan("com.meta.supplychain.*")
@EnableFeignClients(basePackages = "com.meta.supplychain.*")
@EnableAspectJAutoProxy(exposeProxy = true)
public class SupplychainMdApplication {

    public static void main(String[] args) {
        SpringApplication.run(SupplychainMdApplication.class, args);
    }

}
