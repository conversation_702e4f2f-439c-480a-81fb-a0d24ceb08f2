package com.meta.supplychain.md;

import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.enums.md.MdSystemParamEnum;
import com.meta.supplychain.util.message.MsgUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @Description:
 * @date 2024年08月26日 10:54
 */
@SpringBootTest
public class MsgTest {

//    @Resource
//    private MsgUtils msgUtils;
    @Resource
    private ISupplychainControlEngineService supplychainControlEngineService;

    @Test
    public void testGetMsg(){
        System.out.println(MsgUtils.getMsg("SCMD001001"));
        System.out.println(MsgUtils.getMsg("SCMD001002","11","22"));
    }

    @Test
    public void testSystemParam(){
        String value = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(MdSystemParamEnum.GOODS_CATE_CODE_LENGTH_CONFIG);
        System.out.println("testSystemParam:" + value);
    }
}
